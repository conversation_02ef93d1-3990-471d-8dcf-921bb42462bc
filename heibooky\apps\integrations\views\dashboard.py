"""
Dashboard API views for aggregated data.

This module provides API endpoints for dashboard functionality,
including unread counts for various user-related data.
"""

import logging

from apps.integrations.models import Notification
from apps.integrations.serializers import DashboardUnreadCountsSerializer
from apps.reviews.models import Review
from apps.support.models import SupportMessage
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

logger = logging.getLogger(__name__)


class DashboardUnreadCountsView(APIView):
    """
    API endpoint that aggregates and returns unread counts for the dashboard.

    This endpoint provides a single API call to get all unread counts needed
    for the dashboard, including:
    - Unread reviews for user's properties
    - Unread notifications for the user
    - Unread support messages for the user

    **Authentication Required**: Yes

    **HTTP Methods**: GET

    **Response Format**:
    ```json
    {
        "unread_reviews_count": 5,
        "unread_notifications_count": 12,
        "unread_support_messages_count": 3
    }
    ```

    **Error Responses**:
    - 401: Authentication required
    - 500: Internal server error
    """

    permission_classes = [IsAuthenticated]
    serializer_class = DashboardUnreadCountsSerializer

    def get(self, request):
        """
        Get aggregated unread counts for the authenticated user.

        Returns:
            Response: JSON response with unread counts
        """
        try:
            user = request.user
            logger.info(f"Fetching dashboard unread counts for user: {user.id}")

            # Get unread counts using optimized queries
            unread_counts = self._get_unread_counts(user)

            # Serialize the response
            serializer = self.serializer_class(data=unread_counts)
            serializer.is_valid(raise_exception=True)

            logger.info(
                f"Successfully retrieved unread counts for user {user.id}: "
                f"reviews={unread_counts['unread_reviews_count']}, "
                f"notifications={unread_counts['unread_notifications_count']}, "
                f"support_messages={unread_counts['unread_support_messages_count']}"
            )

            return Response(serializer.validated_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                f"Error fetching dashboard unread counts for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": "An error occurred while fetching unread counts"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _get_unread_counts(self, user):
        """
        Get unread counts for all dashboard items for the given user.

        This method optimizes database queries by using select_related and
        prefetch_related where appropriate to avoid N+1 query problems.

        Args:
            user: The authenticated user

        Returns:
            dict: Dictionary containing unread counts
        """
        try:
            # Get unread reviews count for user's properties
            # Users can access reviews for properties where they are staff members
            unread_reviews_count = self._get_unread_reviews_count(user)

            # Get unread notifications count for the user
            unread_notifications_count = self._get_unread_notifications_count(user)

            # Get unread support messages count for the user
            unread_support_messages_count = self._get_unread_support_messages_count(
                user
            )

            return {
                "unread_reviews_count": unread_reviews_count,
                "unread_notifications_count": unread_notifications_count,
                "unread_support_messages_count": unread_support_messages_count,
            }

        except Exception as e:
            logger.error(
                f"Error calculating unread counts for user {user.id}: {str(e)}"
            )
            # Return zero counts on error to prevent dashboard from breaking
            return {
                "unread_reviews_count": 0,
                "unread_notifications_count": 0,
                "unread_support_messages_count": 0,
            }

    def _get_unread_reviews_count(self, user):
        """
        Get count of unread reviews for properties where user is a staff member.

        Args:
            user: The authenticated user

        Returns:
            int: Count of unread reviews
        """
        try:
            # Check if user is superuser (can see all reviews)
            if user.is_superuser:
                return Review.objects.filter(is_read=False).count()

            # Get reviews for properties where user is staff
            # Using select_related to optimize the query
            unread_count = (
                Review.objects.filter(property__staffs=user, is_read=False)
                .select_related("property")
                .count()
            )

            logger.debug(f"Found {unread_count} unread reviews for user {user.id}")
            return unread_count

        except Exception as e:
            logger.error(
                f"Error getting unread reviews count for user {user.id}: {str(e)}"
            )
            return 0

    def _get_unread_notifications_count(self, user):
        """
        Get count of unread notifications for the user.

        Args:
            user: The authenticated user

        Returns:
            int: Count of unread notifications
        """
        try:
            unread_count = Notification.objects.filter(user=user, is_read=False).count()

            logger.debug(
                f"Found {unread_count} unread notifications for user {user.id}"
            )
            return unread_count

        except Exception as e:
            logger.error(
                f"Error getting unread notifications count for user {user.id}: {str(e)}"
            )
            return 0

    def _get_unread_support_messages_count(self, user):
        """
        Get count of unread support messages for the user.

        For regular users: Count unread messages from support staff
        For support staff: Count unread messages from users (if applicable)

        Args:
            user: The authenticated user

        Returns:
            int: Count of unread support messages
        """
        try:
            # For regular users, count unread messages from support staff in their chats
            unread_count = (
                SupportMessage.objects.filter(
                    chat__user=user,
                    is_read=False,
                    is_from_support=True,  # Only count messages from support staff as unread for users
                )
                .select_related("chat")
                .count()
            )

            logger.debug(
                f"Found {unread_count} unread support messages for user {user.id}"
            )
            return unread_count

        except Exception as e:
            logger.error(
                f"Error getting unread support messages count for user {user.id}: {str(e)}"
            )
            return 0
