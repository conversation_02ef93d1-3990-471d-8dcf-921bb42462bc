# Chat API Documentation

This document provides detailed information about the chat endpoints available in the Heibooky application, including both REST API and WebSocket connections.

## REST API Endpoints

### User Support Messages

#### List Your Support Messages

Retrieves all support messages for the authenticated user.

**URL:** `/chat/support/`

**Method:** `GET`

**Auth Required:** Yes

**Permissions:** Authenticated User

**Query Parameters:**

- `page` - Page number (default: 1)
- `page_size` - Number of items per page (default: 10, max: 100)
- `ordering` - Order results (created_at, -created_at, status, priority)

**Sample Request:**
```
GET /chat/support/?page=1&page_size=10&ordering=-created_at
```

**Sample Response:**

```json
{
    "count": 25,
    "next": "/chat/support/?page=2&page_size=10&ordering=-created_at",
    "previous": null,
    "results": [
        {
            "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "message": "I need help with my subscription",
            "created_at": "2023-07-15T10:30:45Z",
            "updated_at": "2023-07-15T10:30:45Z",
            "status": "pending",
            "priority": "medium",
            "sender": "user",
            "attachments": []
        },
        {
            "id": "8da95f64-5717-4562-b3fc-2c963f66bcd7",
            "message": "We'll help you sort that out right away!",
            "created_at": "2023-07-15T10:35:20Z",
            "updated_at": "2023-07-15T10:35:20Z",
            "status": "in_progress",
            "priority": "medium",
            "sender": "support",
            "attachments": []
        }
    ]
}
```

#### Create Support Message

Creates a new support message from the authenticated user.

**URL:** `/chat/support/`

**Method:** `POST`

**Auth Required:** Yes

**Permissions:** Authenticated User

**Content Type:** `application/json` or `multipart/form-data` (if sending files)

**Request Body (JSON):**

```json
{
    "message": "I'm having trouble with my account settings",
    "priority": "medium"
}
```

**Request Body (Multipart Form):**

- `message`: String - Required. The message content.
- `priority`: String - Optional. One of: "low", "medium", "high", "urgent".
- `attachments`: File - Optional. Can include multiple file attachments.

**Sample Response:**

```json
{
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "message": "I'm having trouble with my account settings",
    "created_at": "2023-07-15T11:45:30Z",
    "updated_at": "2023-07-15T11:45:30Z",
    "status": "pending",
    "priority": "medium",
    "sender": "user",
    "attachments": []
}
```

### Admin Support Endpoints

#### List All User Messages (Admin Only)

Retrieves all support messages across users for admin staff.

**URL:** `/chat/support/list_all/`

**Method:** `GET`

**Auth Required:** Yes

**Permissions:** Staff User

**Query Parameters:**

- `page` - Page number (default: 1)
- `page_size` - Number of items per page (default: 10, max: 100)
- `status` - Filter by status (pending, in_progress, resolved, sent)
- `priority` - Filter by priority (low, medium, high, urgent)
- `days` - Filter messages from the last X days
- `search` - Search in message content, user email, or user name
- `ordering` - Order results (created_at, -created_at, status, priority, etc.)

**Sample Request:**

```
GET /chat/support/list_all/?status=pending&days=7&ordering=-created_at
```

**Sample Response:**

```json
{
    "count": 120,
    "next": "/chat/support/list_all/?status=pending&days=7&ordering=-created_at&page=2",
    "previous": null,
    "results": [
        {
            "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "message": "I need help with my subscription",
            "created_at": "2023-07-15T10:30:45Z",
            "status": "pending",
            "priority": "medium",
            "user_name": "John Doe",
            "user_email": "<EMAIL>"
        },
        {
            "id": "8da95f64-5717-4562-b3fc-2c963f66bcd7",
            "message": "When will my room be ready?",
            "created_at": "2023-07-14T10:35:20Z",
            "status": "pending",
            "priority": "high",
            "user_name": "Jane Smith",
            "user_email": "<EMAIL>"
        }
    ]
}
```

#### List Unique Support Conversations

Retrieves a list of unique support conversations grouped by user.

**URL:** `/chat/support/`

**Method:** `GET`

**Auth Required:** Yes

**Permissions:** Staff User

**Sample Request:**

```
GET /chat/support/rooms/
```

**Sample Response:**

```json
[
    {
        "user_id": "5ga85f64-5717-4562-b3fc-2c963f66def8",
        "user_name": "John Doe",
        "user_email": "<EMAIL>",
        "last_message": "We'll help you sort that out right away!",
        "last_updated": "2023-07-15T10:35:20Z",
        "status": "in_progress",
        "priority": "medium"
    },
    {
        "user_id": "7hb85f64-5717-4562-b3fc-2c963f66ghi9",
        "user_name": "Jane Smith",
        "user_email": "<EMAIL>",
        "last_message": "Thank you for your assistance",
        "last_updated": "2023-07-14T15:22:10Z",
        "status": "resolved",
        "priority": "low"
    }
]
```

#### Get Messages for Specific User

Retrieves the full message thread for a specific user.

**URL:** `/chat/messages/`

**Method:** `GET`

**Auth Required:** Yes

**Permissions:** Staff User

**Query Parameters:**

- `user_id` - Required. The ID of the user whose messages to retrieve
- `page` - Page number (default: 1)
- `page_size` - Number of items per page (default: 20, max: 50)

**Sample Request:**
```
GET /chat/messages/?user_id=5ga85f64-5717-4562-b3fc-2c963f66def8
```

**Sample Response:**

```json
{
    "count": 8,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": "1fa85f64-5717-4562-b3fc-2c963f66aaa1",
            "message": "I need help with my subscription",
            "created_at": "2023-07-15T10:30:45Z",
            "status": "pending",
            "priority": "medium",
            "sender": "user",
            "attachments": []
        },
        {
            "id": "2da95f64-5717-4562-b3fc-2c963f66bbb2",
            "message": "We'll help you sort that out right away!",
            "created_at": "2023-07-15T10:35:20Z",
            "status": "in_progress",
            "priority": "medium",
            "sender": "support",
            "attachments": []
        },
        {
            "id": "3ea95f64-5717-4562-b3fc-2c963f66ccc3",
            "message": "Thank you. My subscription should expire next month.",
            "created_at": "2023-07-15T10:38:12Z",
            "status": "in_progress",
            "priority": "medium",
            "sender": "user",
            "attachments": []
        }
    ]
}
```

#### Send Support Message

Allows staff to send a message to a user.

**URL:** `/chat/message/`

**Method:** `POST`

**Auth Required:** Yes

**Permissions:** Staff User

**Content Type:** `application/json` or `multipart/form-data` (if sending attachments)

**Request Body (JSON):**

```json
{
    "user_id": "5ga85f64-5717-4562-b3fc-2c963f66def8",
    "message": "Your subscription has been updated. Please let me know if you need anything else!"
}
```

**Request Body (Multipart Form):**

- `user_id`: String - Required. UUID of the user to send the message to.
- `message`: String - Required. The message content.
- `attachments`: File - Optional. Can include multiple file attachments.

**Sample Response:**

```json
{
    "id": "4fa95f64-5717-4562-b3fc-2c963f66ddd4",
    "status": "sent",
    "timestamp": "2023-07-15T11:05:34Z"
}
```

### Analytics Endpoints (Admin Only)

#### Support Analytics Summary

Retrieves summary analytics for the support team.

**URL:** `/chat/analytics/summary/`

**Method:** `GET`

**Auth Required:** Yes

**Permissions:** Admin User

**Sample Request:**
```
GET /chat/analytics/summary/
```

**Sample Response:**
```json
{
    "resolved_chats": 87,
    "average_response_time_minutes": 18,
    "active_agents_this_week": 5,
    "total_messages_sent": 645
}
```

#### Support Analytics Timeseries

Retrieves time series analytics for the support team.

**URL:** `/chat/analytics/timeseries/`

**Method:** `GET`

**Auth Required:** Yes

**Permissions:** Admin User

**Query Parameters:**
- `range` - Either "daily" or "weekly" (default: daily)

**Sample Request:**
```
GET /chat/analytics/timeseries/?range=daily
```

**Sample Response:**
```json
{
    "labels": [
        "2023-06-16",
        "2023-06-17",
        "2023-06-18",
        "2023-06-19",
        "2023-06-20"
    ],
    "resolved_counts": [3, 5, 2, 4, 6],
    "new_chats": [4, 6, 3, 5, 7]
}
```

## WebSocket Endpoints

### Notifications WebSocket

**URL:** `ws://{hostname}/ws/notifications/`

**Auth Required:** Yes (via session authentication)

**Description:** Provides real-time notifications for the authenticated user, including support chat notifications.

**Connection:**
```javascript
// Assuming you have an authenticated session
const notificationSocket = new WebSocket('ws://localhost:8000/ws/notifications/');

notificationSocket.onopen = function(e) {
  console.log("Notification connection established");
};

notificationSocket.onmessage = function(e) {
  const data = JSON.parse(e.data);
  console.log("Notification received:", data);
};
```

**Events:**

1. **Ping/Pong (Keep-alive)**

   Send:
   ```json
   {
     "action": "ping"
   }
   ```

   Response:
   ```json
   {
     "type": "pong"
   }
   ```

2. **Mark Notifications as Read**

   Send:

   ```json
   {
     "action": "mark_as_read",
     "notification_ids": ["3fa85f64-5717-4562-b3fc-2c963f66afa6"]
   }
   ```

3. **Receive New Support Message Notification**

   Incoming message:

   ```json
   {
     "type": "notification_message",
     "message": {
       "type": "support_message",
       "data": {
         "message": "Your subscription has been updated",
         "timestamp": "2023-07-15T11:05:34Z"
       }
     }
   }
   ```

### Support Chat WebSocket (Staff Only)

**URL:** `ws://{hostname}/ws/support/{user_id}/?token={jwt_token}`

**Auth Required:** Yes (via JWT token in URL query parameter)

**Description:** WebSocket for support staff to send and receive real-time messages with users.

**Connection:**
```javascript
// JWT token must belong to a staff user
const jwt = "eyJhbGci..."; // Your JWT token
const userId = "7f79707f-a905-486f-9791-c42f7d4d254e"; // User ID to chat with
const supportSocket = new WebSocket(`ws://localhost:8000/ws/support/${userId}/?token=${jwt}`);

supportSocket.onopen = function(e) {
  console.log("Support chat connection established");
};

supportSocket.onmessage = function(e) {
  const data = JSON.parse(e.data);
  console.log("Message received:", data);
};
```

**Events:**

1. **Send Support Message**

   Send:
   ```json
   {
     "type": "chat.message",
     "message": "How can I assist you today?"
   }
   ```

2. **Receive Message**

   Incoming message:
   ```json
   {
     "type": "chat.message",
     "message": "I need help with my booking",
     "sender": "user",
     "timestamp": "2023-07-15T11:05:34Z",
     "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
     "status": "pending",
     "priority": "medium"
   }
   ```

3. **Keep-alive Ping**

   Send:
   ```json
   {
     "type": "ping"
   }
   ```

   Response:
   ```json
   {
     "type": "pong"
   }
   ```

**Connection Close Codes:**
- `4001`: No authentication token provided
- `4002`: Invalid authentication token
- `4003`: Error authenticating token
- `4004`: User is not a staff member
- `4005`: No user ID provided in the URL
- `4006`: Invalid user ID format

## Error Responses

All REST API endpoints may return the following error responses:

### Unauthorized (401)
```json
{
    "detail": "Authentication credentials were not provided."
}
```

### Forbidden (403)
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### Bad Request (400)
```json
{
    "error": ["Invalid input provided."]
}
```
Or:
```json
{
    "message": ["Message field is required."]
}
```

### Internal Server Error (500)
```json
{
    "error": ["An unexpected error occurred"]
}
```

## Complete URL List

Here's a complete list of all chat-related endpoints:

### REST API URLs:
- `GET /chat/support/` - List authenticated user's support messages
- `POST /chat/support/` - Create a new support message
- `GET /chat/support/list_all/` - Staff: List all support messages across users
- `GET /chat/support/rooms/` - Staff: List unique support conversations
- `GET /chat/messages/` - Staff: Get messages for a specific user
- `POST /chat/message/` - Staff: Send a message to a user
- `GET /chat/analytics/summary/` - Admin: Get support analytics summary
- `GET /chat/analytics/timeseries/` - Admin: Get support analytics timeseries

### WebSocket URLs:
- `ws://{hostname}/ws/notifications/` - User notifications
- `ws://{hostname}/ws/support/{user_id}/?token={jwt_token}` - Support chat
