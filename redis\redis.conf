# Redis configuration for local development without authentication

# Network configuration
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300
tcp-backlog 511

# General configuration
daemonize no
supervised no
pidfile /data/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Persistence configuration (disabled for faster development)
save ""
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication configuration
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Security configuration - NO AUTHENTICATION
protected-mode no
# requirepass is commented out to disable authentication

# ACL configuration - minimal setup for development
# Load ACL file with default user configuration
aclfile /etc/redis/users.acl
acllog-max-len 128

# Memory management
maxmemory-policy allkeys-lru
# maxmemory is set via Docker constraints

# Append only file configuration (disabled for development)
appendonly no

# Lua scripting
lua-time-limit 5000

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 0

# Event notification
notify-keyspace-events ""

# Hash configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set configuration
set-max-intset-entries 512

# Sorted set configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog configuration
hll-sparse-max-bytes 3000

# Streams configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol buffer limit
proto-max-bulk-len 512mb

# Frequency of rehashing
hz 10

# AOF rewrite configuration
aof-rewrite-incremental-fsync yes

# RDB snapshot configuration
rdb-save-incremental-fsync yes

# Module loading (if needed)
# loadmodule /path/to/module.so

# Include additional config files if needed
# include /path/to/local.conf
