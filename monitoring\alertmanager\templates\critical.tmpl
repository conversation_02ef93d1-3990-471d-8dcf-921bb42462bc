<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Critical Alert</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .alert { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .critical { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .details { margin-top: 10px; }
        .timestamp { font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <h2>🚨 Critical Alert Notification</h2>
    <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
    <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>
    <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp }}</p>

    {{ range .Alerts }}
    <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}critical{{ end }}">
        <h3>{{ .Annotations.summary }}</h3>
        <p><strong>Status:</strong> {{ .Status }}</p>
        <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
        <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
        <div class="details">
            <p>{{ .Annotations.description }}</p>
            {{ if .Annotations.runbook_url }}
            <p><a href="{{ .Annotations.runbook_url }}">📖 Runbook</a></p>
            {{ end }}
        </div>
        <p class="timestamp">
            {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
            {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
        </p>
    </div>
    {{ end }}

    <p><small>This is an automated alert from Heibooky Monitoring System</small></p>
</body>
</html>
