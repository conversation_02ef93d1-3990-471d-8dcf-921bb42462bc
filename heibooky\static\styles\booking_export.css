/* Booking Export Page Styles */
/* Ensure high specificity to override any conflicting styles */

.booking-export .card-header {
    background-color: #0d6efd !important;
    color: white !important;
}

.booking-export .btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.booking-export .btn-primary:hover,
.booking-export .btn-primary:focus {
    background-color: #0b5ed7 !important;
    border-color: #0a58ca !important;
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
    -webkit-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.booking-export .card {
    -webkit-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    border: none !important;
}

.booking-export .form-control:focus {
    border-color: #0d6efd !important;
    -webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    -moz-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: none;
}

.booking-export .spinner-border {
    width: 3rem !important;
    height: 3rem !important;
}

/* Loading state improvements */
.booking-export .btn-primary:disabled {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    cursor: not-allowed;
}

/* Additional responsive improvements */
@media (max-width: 768px) {
    .booking-export .container {
        padding: 0 15px;
    }

    .booking-export .card-body {
        padding: 1.5rem !important;
    }

    .booking-export .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Print styles */
@media print {
    .booking-export .btn,
    .booking-export .spinner-border {
        display: none !important;
    }
}
