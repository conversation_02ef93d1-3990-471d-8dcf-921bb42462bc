import logging
from typing import Any, Dict

from celery import shared_task
from django.core.files.base import ContentFile
from services.storage.storage import CDNStorageService

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def optimize_and_upload_image(
    self,
    image_data: bytes,
    filename: str,
    model_name: str,
    object_id: str,
    field_name: str,
) -> Dict[str, Any]:
    """
    Asynchronously optimize and upload image to CDN storage

    Args:
        image_data: Raw image data as bytes
        filename: Original filename
        model_name: Django model name (e.g., 'stay.Photo')
        object_id: ID of the model instance
        field_name: Name of the field to update

    Returns:
        Dictionary with upload result information
    """
    try:
        logger.info(
            f"Starting async image optimization for {filename} (model: {model_name}, id: {object_id})"
        )

        # Create ContentFile from bytes
        content_file = ContentFile(image_data, name=filename)

        # Upload to CDN with optimization
        upload_result = CDNStorageService.upload_file_with_path(
            content_file, filename, storage_type="photo"
        )

        if not upload_result:
            raise Exception("Failed to upload to CDN")

        cdn_url, relative_path = upload_result

        # Update the model instance with the relative path (for database storage)
        from django.apps import apps

        try:
            model = apps.get_model(model_name)
            instance = model.objects.get(id=object_id)
            setattr(instance, field_name, relative_path)
            instance.save(update_fields=[field_name])

            logger.info(
                f"Successfully updated {model_name} instance {object_id} with relative path: {relative_path}"
            )

        except Exception as e:
            logger.error(f"Failed to update model instance: {str(e)}")
            # Don't fail the task if we can't update the model, the file is still uploaded

        return {
            "success": True,
            "cdn_url": cdn_url,
            "filename": filename,
            "message": "Image optimized and uploaded successfully",
        }

    except Exception as e:
        logger.error(f"Error in async image optimization for {filename}: {str(e)}")

        # Retry the task if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying image optimization for {filename} (attempt {self.request.retries + 1})"
            )
            raise self.retry(exc=e)

        return {
            "success": False,
            "filename": filename,
            "error": str(e),
            "message": "Failed to optimize and upload image after all retries",
        }


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def optimize_and_upload_image_from_file(
    self,
    temp_file_path: str,
    filename: str,
    model_name: str,
    object_id: str,
    field_name: str,
) -> Dict[str, Any]:
    """
    Asynchronously optimize and upload image from temporary file path (memory-efficient)

    Args:
        temp_file_path: Path to temporary file containing image data
        filename: Original filename
        model_name: Django model name (e.g., 'stay.Photo')
        object_id: ID of the model instance
        field_name: Name of the field to update

    Returns:
        Dictionary with upload result information
    """
    import os

    try:
        logger.info(
            f"Starting async image optimization from file for {filename} (model: {model_name}, id: {object_id})"
        )

        # Check if temp file exists
        if not os.path.exists(temp_file_path):
            raise Exception(f"Temporary file not found: {temp_file_path}")

        # Create ContentFile from temporary file
        with open(temp_file_path, "rb") as temp_file:
            content_file = ContentFile(temp_file.read(), name=filename)

        # Upload to CDN with optimization
        upload_result = CDNStorageService.upload_file_with_path(
            content_file, filename, storage_type="photo"
        )

        if not upload_result:
            raise Exception("Failed to upload to CDN")

        cdn_url, relative_path = upload_result

        # Update the model instance with the relative path (for database storage)
        from django.apps import apps

        try:
            model = apps.get_model(model_name)
            instance = model.objects.get(id=object_id)
            setattr(instance, field_name, relative_path)
            instance.save(update_fields=[field_name])

            logger.info(
                f"Successfully updated {model_name} instance {object_id} with CDN URL: {cdn_url}"
            )

        except Exception as e:
            logger.error(f"Failed to update model instance: {str(e)}")
            # Don't fail the task if we can't update the model, the file is still uploaded

        # Clean up temporary file
        try:
            os.unlink(temp_file_path)
            logger.info(f"Cleaned up temporary file: {temp_file_path}")
        except OSError as e:
            logger.warning(
                f"Failed to clean up temporary file {temp_file_path}: {str(e)}"
            )

        return {
            "success": True,
            "cdn_url": cdn_url,
            "filename": filename,
            "message": "Image optimized and uploaded successfully from file",
        }

    except Exception as e:
        logger.error(
            f"Error in async image optimization from file for {filename}: {str(e)}"
        )

        # Clean up temporary file on error
        try:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                logger.info(f"Cleaned up temporary file after error: {temp_file_path}")
        except OSError:
            pass

        # Retry the task if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying image optimization from file for {filename} (attempt {self.request.retries + 1})"
            )
            raise self.retry(exc=e)

        return {
            "success": False,
            "filename": filename,
            "error": str(e),
            "message": "Failed to optimize and upload image from file after all retries",
        }


@shared_task(bind=True, max_retries=3, default_retry_delay=30)
def batch_optimize_images(self, image_list: list) -> Dict[str, Any]:
    """
    Batch process multiple images for optimization

    Args:
        image_list: List of dictionaries with image information

    Returns:
        Dictionary with batch processing results
    """
    results = {"successful": 0, "failed": 0, "total": len(image_list), "details": []}

    logger.info(f"Starting batch optimization for {len(image_list)} images")

    for image_info in image_list:
        try:
            result = optimize_and_upload_image.delay(
                image_info["image_data"],
                image_info["filename"],
                image_info["model_name"],
                image_info["object_id"],
                image_info["field_name"],
            )

            results["details"].append(
                {
                    "filename": image_info["filename"],
                    "task_id": result.id,
                    "status": "queued",
                }
            )
            results["successful"] += 1

        except Exception as e:
            logger.error(
                f"Failed to queue optimization for {image_info['filename']}: {str(e)}"
            )
            results["details"].append(
                {
                    "filename": image_info["filename"],
                    "status": "failed",
                    "error": str(e),
                }
            )
            results["failed"] += 1

    logger.info(
        f"Batch optimization queued: {results['successful']} successful, {results['failed']} failed"
    )
    return results


@shared_task(bind=True, max_retries=2, default_retry_delay=120)
def cleanup_old_media_files(self, days_old: int = 30) -> Dict[str, Any]:
    """
    Clean up old media files that are no longer referenced

    Args:
        days_old: Remove files older than this many days

    Returns:
        Dictionary with cleanup results

    Note:
        This task operates in two modes based on the ENABLE_MEDIA_FILE_DELETION setting:
        - False (default/safe mode): Only logs what would be deleted, no actual deletion occurs
        - True (deletion mode): Actually deletes old files from storage

        To enable actual deletion, set ENABLE_MEDIA_FILE_DELETION=True in your environment variables.
        This safety mechanism prevents accidental data loss during development or testing.
    """
    try:
        from datetime import timedelta

        import boto3
        from django.conf import settings
        from django.utils import timezone

        logger.info(f"Starting cleanup of media files older than {days_old} days")

        # Connect to S3
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
        )

        cutoff_date = timezone.now() - timedelta(days=days_old)
        deleted_count = 0
        error_count = 0

        # List objects in the media location
        paginator = s3_client.get_paginator("list_objects_v2")
        pages = paginator.paginate(
            Bucket=settings.AWS_STORAGE_BUCKET_NAME, Prefix=f"{settings.AWS_LOCATION}/"
        )

        for page in pages:
            if "Contents" not in page:
                continue

            for obj in page["Contents"]:
                try:
                    # Check if file is older than cutoff date
                    if obj["LastModified"].replace(tzinfo=timezone.utc) < cutoff_date:
                        # Check if file is still referenced in database
                        # This is a simplified check - you might want to implement more thorough checking
                        file_key = obj["Key"]

                        # Check if deletion is enabled via configuration
                        if settings.ENABLE_MEDIA_FILE_DELETION:
                            # Actually delete the file from storage
                            s3_client.delete_object(
                                Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=file_key
                            )
                            deleted_count += 1
                            logger.info(
                                f"Deleted old file: {file_key} (last modified: {obj['LastModified']})"
                            )
                        else:
                            # Safe mode: only log what would be deleted
                            logger.info(
                                f"Would delete old file (safe mode): {file_key} (last modified: {obj['LastModified']})"
                            )

                except Exception as e:
                    logger.error(
                        f"Error processing file {obj.get('Key', 'unknown')}: {str(e)}"
                    )
                    error_count += 1

        result = {
            "success": True,
            "deleted_count": deleted_count,
            "error_count": error_count,
            "deletion_enabled": settings.ENABLE_MEDIA_FILE_DELETION,
            "message": f'Cleanup completed. {deleted_count} files {"deleted" if settings.ENABLE_MEDIA_FILE_DELETION else "would be deleted"}, {error_count} errors. Deletion mode: {"enabled" if settings.ENABLE_MEDIA_FILE_DELETION else "safe mode (disabled)"}',
        }

        logger.info(f"Media cleanup completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in media cleanup task: {str(e)}")

        if self.request.retries < self.max_retries:
            logger.info(f"Retrying media cleanup (attempt {self.request.retries + 1})")
            raise self.retry(exc=e)

        return {
            "success": False,
            "error": str(e),
            "message": "Media cleanup failed after all retries",
        }


@shared_task
def monitor_cdn_health() -> Dict[str, Any]:
    """
    Monitor CDN and storage health

    Returns:
        Dictionary with health monitoring results
    """
    try:
        logger.info("Starting CDN health monitoring")

        health_info = CDNStorageService.check_cdn_health()

        # Log health status
        if health_info["cdn_available"] and health_info["storage_available"]:
            logger.info("CDN and storage are healthy")
        else:
            logger.warning(f"CDN health issues detected: {health_info}")

            # You could implement alerting here (email, Slack, etc.)
            if health_info["errors"]:
                logger.error(f"CDN errors: {', '.join(health_info['errors'])}")

        return health_info

    except Exception as e:
        logger.error(f"Error in CDN health monitoring: {str(e)}")
        return {"cdn_available": False, "storage_available": False, "errors": [str(e)]}
