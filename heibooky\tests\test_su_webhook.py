"""
Test cases for SU Channel Manager Push API webhook integration.

This module contains comprehensive tests for the push-based reservation system,
including webhook endpoint testing, payload validation, and error handling.
"""

import json
from decimal import Decimal
from unittest.mock import patch

from apps.booking.api import process_push_reservation
from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Property
from django.test import Client, TestCase
from django.urls import reverse
from rest_framework import status


class SuWebhookTestCase(TestCase):
    """Test cases for SU webhook endpoint."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.webhook_url = reverse("su-reservation-webhook")

        # Create test property
        self.property = Property.objects.create(
            name="Test Hotel",
            hotel_id="TEST123",
            is_onboarded=True,
            address="Test Address",
            city="Test City",
            country="Italy",
        )

        # Sample valid payload
        self.valid_payload = {
            "id": "RES123456",
            "hotel_id": "TEST123",
            "booked_at": "2024-01-15 10:30:00",
            "modified_at": "2024-01-15 10:30:00",
            "deposit": "100.00",
            "paymenttype": "Hotel Collect",
            "commissionamount": "25.00",
            "paymentdue": "200.00",
            "numberofinfants": "0",
            "numberofadults": "2",
            "numberofchildren": "0",
            "reservation_notif_id": "NOTIF123",
            "customer": {
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "telephone": "+1234567890",
                "address": "123 Test St",
                "city": "Test City",
                "state": "Test State",
                "countrycode": "US",
                "zip": "12345",
                "remarks": "Test remarks",
            },
            "rooms": [
                {
                    "guest_name": "John Doe",
                    "arrival_date": "2024-02-01",
                    "departure_date": "2024-02-05",
                    "totalprice": "500.00",
                    "totaltax": "50.00",
                    "numberofguests": "2",
                    "numberofadults": "2",
                    "numberofchildren": "0",
                    "roomstaystatus": "new",
                    "addons": {},
                    "taxes": {},
                }
            ],
            "affiliation": {"hotel_id": "TEST123", "OTA_Code": "19"},
        }

    def test_webhook_endpoint_exists(self):
        """Test that webhook endpoint is accessible."""
        response = self.client.post(
            self.webhook_url, {}, content_type="application/json"
        )
        # Should not return 404
        self.assertNotEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_valid_payload_processing(self):
        """Test processing of valid reservation payload."""
        with patch("apps.booking.views.reservation_notification") as mock_ack:
            mock_ack.return_value = {"status": "success"}

            response = self.client.post(
                self.webhook_url,
                json.dumps(self.valid_payload),
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            response_data = response.json()
            self.assertEqual(response_data["status"], "success")
            self.assertIn("reservation_id", response_data)

    def test_empty_payload(self):
        """Test handling of empty payload."""
        response = self.client.post(
            self.webhook_url, "", content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response_data = response.json()
        self.assertEqual(response_data["status"], "error")
        self.assertIn("Empty request body", response_data["message"])

    def test_invalid_json_payload(self):
        """Test handling of invalid JSON payload."""
        invalid_json = '{"invalid": json}'

        response = self.client.post(
            self.webhook_url, invalid_json, content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response_data = response.json()
        self.assertEqual(response_data["status"], "error")
        self.assertIn("Invalid JSON payload", response_data["message"])

    def test_missing_required_fields(self):
        """Test handling of payload with missing required fields."""
        incomplete_payload = {
            "id": "RES123456"
            # Missing customer and rooms
        }

        response = self.client.post(
            self.webhook_url,
            json.dumps(incomplete_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response_data = response.json()
        self.assertEqual(response_data["status"], "error")
        self.assertIn("Invalid payload structure", response_data["message"])

    def test_missing_customer_email(self):
        """Test handling of payload with missing customer email."""
        payload = self.valid_payload.copy()
        payload["customer"] = {"first_name": "John", "last_name": "Doe"}

        response = self.client.post(
            self.webhook_url, json.dumps(payload), content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_property_not_found(self):
        """Test handling when property is not found."""
        payload = self.valid_payload.copy()
        payload["hotel_id"] = "NONEXISTENT"
        payload["affiliation"]["hotel_id"] = "NONEXISTENT"

        response = self.client.post(
            self.webhook_url, json.dumps(payload), content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        response_data = response.json()
        self.assertEqual(response_data["status"], "error")

    @patch("apps.booking.views.reservation_notification")
    def test_acknowledgment_failure_handling(self, mock_ack):
        """Test that acknowledgment failures don't fail the request."""
        mock_ack.side_effect = Exception("Acknowledgment failed")

        response = self.client.post(
            self.webhook_url,
            json.dumps(self.valid_payload),
            content_type="application/json",
        )

        # Should still succeed even if acknowledgment fails
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data["status"], "success")

    def test_customer_creation(self):
        """Test that customer is created correctly."""
        with patch("apps.booking.views.reservation_notification"):
            response = self.client.post(
                self.webhook_url,
                json.dumps(self.valid_payload),
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Check customer was created
            customer = Customer.objects.get(email="<EMAIL>")
            self.assertEqual(customer.first_name, "John")
            self.assertEqual(customer.last_name, "Doe")
            self.assertEqual(customer.telephone, "+1234567890")

    def test_reservation_creation(self):
        """Test that reservation is created correctly."""
        with patch("apps.booking.views.reservation_notification"):
            response = self.client.post(
                self.webhook_url,
                json.dumps(self.valid_payload),
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Check reservation was created
            reservation = Reservation.objects.get(id="RES123456")
            self.assertEqual(reservation.guest_name, "John Doe")
            self.assertEqual(reservation.gross_price, Decimal("500.00"))
            self.assertEqual(reservation.payment_type, "Hotel Collect")

    def test_booking_creation(self):
        """Test that booking is created correctly."""
        with patch("apps.booking.views.reservation_notification"):
            response = self.client.post(
                self.webhook_url,
                json.dumps(self.valid_payload),
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Check booking was created
            booking = Booking.objects.get(reservation_data__id="RES123456")
            self.assertEqual(booking.property, self.property)
            self.assertEqual(booking.status, "new")
            self.assertEqual(booking.channel_code, 19)
            self.assertFalse(booking.is_manual)


class ProcessPushReservationTestCase(TestCase):
    """Test cases for process_push_reservation function."""

    def setUp(self):
        """Set up test data."""
        self.property = Property.objects.create(
            name="Test Hotel",
            hotel_id="TEST123",
            is_onboarded=True,
            address="Test Address",
            city="Test City",
            country="Italy",
        )

        self.reservation_data = {
            "id": "RES123456",
            "hotel_id": "TEST123",
            "customer": {
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
            },
            "rooms": [
                {
                    "guest_name": "John Doe",
                    "arrival_date": "2024-02-01",
                    "departure_date": "2024-02-05",
                    "totalprice": "500.00",
                    "roomstaystatus": "new",
                }
            ],
            "affiliation": {"hotel_id": "TEST123", "OTA_Code": "19"},
        }

    def test_successful_processing(self):
        """Test successful reservation processing."""
        result = process_push_reservation(self.reservation_data)

        self.assertEqual(result["status"], "success")
        self.assertIn("reservation_id", result)
        self.assertEqual(result["reservation_id"], "RES123456")

    def test_missing_hotel_id(self):
        """Test handling of missing hotel_id."""
        data = self.reservation_data.copy()
        del data["hotel_id"]
        del data["affiliation"]

        result = process_push_reservation(data)

        self.assertEqual(result["status"], "error")
        self.assertIn("Missing hotel_id", result["message"])

    def test_property_not_found(self):
        """Test handling when property doesn't exist."""
        data = self.reservation_data.copy()
        data["hotel_id"] = "NONEXISTENT"
        data["affiliation"]["hotel_id"] = "NONEXISTENT"

        result = process_push_reservation(data)

        self.assertEqual(result["status"], "error")
        self.assertIn("Property with hotel_id NONEXISTENT not found", result["message"])

    def test_missing_customer_email(self):
        """Test handling of missing customer email."""
        data = self.reservation_data.copy()
        del data["customer"]["email"]

        result = process_push_reservation(data)

        self.assertEqual(result["status"], "error")
        self.assertIn("Missing customer email", result["message"])

    def test_no_rooms_data(self):
        """Test handling of missing rooms data."""
        data = self.reservation_data.copy()
        data["rooms"] = []

        result = process_push_reservation(data)

        self.assertEqual(result["status"], "error")
        self.assertIn("No rooms data found", result["message"])
