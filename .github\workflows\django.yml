name: Heibooky CI/CD Pipeline (Production)

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production

permissions:
  contents: read
  packages: write
  id-token: write
  attestations: write
  security-events: write
  actions: read
  pull-requests: read

env:
  REGISTRY: docker.io
  IMAGE_NAME: davysongs/heibooky

jobs:
  lint:
    name: Run Code Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        python-version: ['3.11']
      fail-fast: false

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_heibooky
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        pip install -r requirements.txt
        pip install black

    - name: Clean Python cache files
      run: |
        find . -type f -name "*.pyc" -delete
        find . -type d -name "__pycache__" -exec rm -rf {} + || true

    - name: Check code formatting
      run: |
        black --check --diff .

  build:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [lint]
    timeout-minutes: 30
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      full-image-name: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}

    env:
      DOCKER_BUILDKIT: 1
      BUILDKIT_PROGRESS: plain

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        driver-opts: |
          network=host
        platforms: linux/amd64,linux/arm64

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Verify Docker Hub authentication
      run: |
        echo "Verifying Docker Hub authentication..."
        docker info | grep -i "registry" || echo "Docker registry info not available"
        
        # Test basic registry connectivity
        echo "Testing Docker Hub connectivity..."
        curl -f -s "https://registry.hub.docker.com/v2/" > /dev/null && \
          echo "✅ Docker Hub is accessible" || \
          echo "⚠️ Docker Hub connectivity test failed"

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-,format=short
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=stable,enable=${{ github.ref == 'refs/heads/main' }}
        labels: |
          org.opencontainers.image.title=Heibooky
          org.opencontainers.image.description=Heibooky Application
          org.opencontainers.image.vendor=Heibooky Team
          org.opencontainers.image.licenses=MIT

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: |
          type=gha,scope=build-${{ github.ref_name }}
          type=gha,scope=build-main
        cache-to: |
          type=gha,mode=max,scope=build-${{ github.ref_name }}
        platforms: linux/amd64
        build-args: |
          BUILDKIT_INLINE_CACHE=1
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          VCS_REF=${{ github.sha }}
          VERSION=${{ steps.meta.outputs.version }}
        provenance: false

    - name: Verify image push
      run: |
        echo "Build completed successfully!"
        echo "Image digest: ${{ steps.build.outputs.digest }}"
        echo "Image tags: ${{ steps.meta.outputs.tags }}"
        
        # Verify the main image exists
        MAIN_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}"
        echo "Verifying image: $MAIN_IMAGE"
        
        # Try to inspect the image manifest
        docker manifest inspect "$MAIN_IMAGE" || echo "Failed to inspect image manifest"

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    timeout-minutes: 20
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: 
      name: production
      url: https://backend.heibooky.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH key
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.AWS_PRIVATE_KEY }}

    - name: Add server to known hosts
      run: |
        ssh-keyscan -H ${{ secrets.AWS_HOST_PRD }} >> ~/.ssh/known_hosts

    - name: Deploy to production server
      env:
        DOPPLER_TOKEN_PRD: ${{ secrets.DOPPLER_TOKEN_PRD }}
        AWS_HOST: ${{ secrets.AWS_HOST_PRD }}
        AWS_USER: ${{ secrets.AWS_USER }}
        IMAGE_NAME: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        ssh -o StrictHostKeyChecking=no ${{ env.AWS_USER }}@${{ env.AWS_HOST }} << ENDSSH
          set -e
          sudo -i
          cd /opt/heibooky
          
          # Check if this is initial deployment
          if ! docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
            echo "Initial deployment detected"
            INITIAL_DEPLOY=true
          else
            echo "Existing deployment detected"
            INITIAL_DEPLOY=false
          fi
          
          # Pull latest code
          git fetch --all
          git checkout main
          git reset --hard origin/main
          
          # Export environment variables for deployment script
          export DOPPLER_TOKEN_PRD="$DOPPLER_TOKEN_PRD"
          export IMAGE_NAME="$IMAGE_NAME"
          export IMAGE_TAG="$IMAGE_TAG"
          
          # Make scripts executable
          chmod +x ./scripts/*.sh
          
          # Run deployment with appropriate flags
          ./scripts/deploy.sh production --force
          
          # Wait for services to stabilize
          echo "Waiting for services to stabilize..."
          sleep 20
          
          # Verify deployment with retries
          for i in {1..5}; do
            if curl -f -s http://localhost:8000/health/ > /dev/null 2>&1; then
              echo "Health check passed on attempt $i"
              break
            elif [ $i -eq 5 ]; then
              echo "Health check failed after 5 attempts"
              # Show logs for debugging
              docker-compose -f docker-compose.prod.yml logs --tail=50 web
              exit 1
            else
              echo "Health check failed on attempt $i, retrying in 10 seconds..."
              sleep 10
            fi
          done
        ENDSSH

  create-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [deploy-production, build]
    if: github.ref == 'refs/heads/main' && needs.deploy-production.result == 'success'
    timeout-minutes: 5
    permissions:
      contents: write  # This is required for creating releases

    steps:
    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
          tag_name: v${{ github.run_number }}
          name: Release v${{ github.run_number }}
          body: |
            ## 🚀 Heibooky Release v${{ github.run_number }}
            
            **Deployment Information:**
            - **Environment:** Production
            - **Branch:** ${{ github.ref_name }}
            - **Commit:** ${{ github.sha }}
            - **Docker Image:** `${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.build.outputs.image-tag }}`
            
            **Changes:**
            ${{ github.event.head_commit.message }}
            
            **Quick Links:**
            - [🌐 Live Application](https://backend.heibooky.com)
            - [📊 Monitoring Dashboard](https://grafana.backend.heibooky.com)
            - [🔍 View Commit](${{ github.event.head_commit.url }})
            
            ---
            *This release was automatically created by the CI/CD pipeline.*
          draft: false
          prerelease: false
          generate_release_notes: true

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    timeout-minutes: 5

    steps:
    - name: Determine deployment status
      id: status
      run: |
        if [[ "${{ needs.deploy-production.result }}" == "success" ]]; then
          echo "environment=production" >> $GITHUB_OUTPUT
          echo "status=success" >> $GITHUB_OUTPUT
          echo "url=https://backend.heibooky.com" >> $GITHUB_OUTPUT
        elif [[ "${{ needs.deploy-production.result }}" == "failure" ]]; then
          echo "environment=production" >> $GITHUB_OUTPUT
          echo "status=failure" >> $GITHUB_OUTPUT
          echo "url=https:// backend.heibooky.com" >> $GITHUB_OUTPUT
        else
          echo "environment=unknown" >> $GITHUB_OUTPUT
          echo "status=skipped" >> $GITHUB_OUTPUT
          echo "url=" >> $GITHUB_OUTPUT
        fi

    - name: Final status check
      run: |
        if [[ "${{ steps.status.outputs.status }}" == "failure" ]]; then
          echo "::error::Deployment failed for ${{ steps.status.outputs.environment }} environment"
          exit 1
        elif [[ "${{ steps.status.outputs.status }}" == "success" ]]; then
          echo "::notice::Deployment successful for ${{ steps.status.outputs.environment }} environment"
        fi