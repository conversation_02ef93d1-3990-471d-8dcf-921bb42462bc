{% extends "emails/base_email.html" %}

{% block title %}
    Promemoria Prenotazione
{% endblock %}

{% block content %}
    <tr>
        <td class="content">
            <div class="main-content">
                <h2 class="reservation-title">{{ reminder_title }}</h2>
                <p class="reservation-subtitle">{{ reminder_subtitle }}</p>

                <div class="reservation-section">
                    <div class="section-header">
                        <span class="section-icon">🏨</span>
                        <h3>Dettagli Prenotazione</h3>
                    </div>
                    <table class="details-table">
                        <tr>
                            <td class="detail-label">ID Prenotazione</td>
                            <td class="detail-value highlight">{{ reservation.id }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">Check-in</td>
                            <td class="detail-value">{{ reservation.checkin_date|date:"d/m/Y" }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">Check-out</td>
                            <td class="detail-value">{{ reservation.checkout_date|date:"d/m/Y" }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">Ospiti</td>
                            <td class="detail-value">
                                <span class="guest-count">{{ reservation.number_of_adults }}</span> Adulti<br>
                                <span class="guest-count">{{ reservation.number_of_children }}</span> Bambini<br>
                                <span class="guest-count">{{ reservation.number_of_infants }}</span> Neonati
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="guest-section">
                    <div class="section-header">
                        <span class="section-icon">👤</span>
                        <h3>Informazioni Ospite</h3>
                    </div>
                    <table class="details-table">
                        <tr>
                            <td class="detail-label">Nome Ospite</td>
                            <td class="detail-value">{{ customer.first_name }} {{ customer.last_name }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">Email</td>
                            <td class="detail-value">{{ customer.email }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">Telefono</td>
                            <td class="detail-value">{{ customer.telephone }}</td>
                        </tr>
                    </table>
                </div>

                <div class="action-prompt">
                    <p>{{ action_message }}</p>
                </div>
            </div>
        </td>
    </tr>
{% endblock %}

{% block style %}
{{ block.super }}
<style>
    .main-content {
        padding: 20px;
    }

    .reservation-title {
        color: #113158;
        font-size: 28px;
        margin-bottom: 10px;
        text-align: center;
    }

    .reservation-subtitle {
        color: #666;
        margin-bottom: 30px;
        text-align: center;
    }

    .reservation-section,
    .price-section,
    .guest-section {
        background: #ffffff;
        border-radius: 8px;
        margin-bottom: 25px;
        box-shadow: 0 2px 4px rgba(17, 49, 88, 0.1);
        overflow: hidden;
    }

    .section-header {
        background: #113158;
        color: white;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .section-icon {
        font-size: 20px;
    }

    .details-table,
    .price-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        padding: 15px;
    }

    .detail-label,
    .price-label {
        color: #666;
        font-weight: 600;
        padding: 12px 20px;
        width: 40%;
    }

    .detail-value,
    .price-value {
        color: #113158;
        padding: 12px 20px;
    }

    .highlight {
        color: #FCB51F;
        font-weight: bold;
    }

    .guest-count {
        color: #FCB51F;
        font-weight: bold;
        font-size: 18px;
    }

    .action-prompt {
        background: #f8f9fa;
        border-left: 4px solid #FCB51F;
        padding: 15px;
        margin: 20px 0;
        color: #113158;
    }

    @media only screen and (max-width: 600px) {
        .detail-label,
        .price-label {
            width: 50%;
        }
        
        .reservation-title {
            font-size: 24px;
        }
    }
</style>
{% endblock %}
