{% extends "base.html" %}
{% load static %}

{% block title %}Esportazione Prenotazioni{% endblock %}

{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'styles/booking_export.css' %}" id="booking-export-css">
{% endblock %}

{% block content %}
<div class="container my-5 booking-export">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header py-3">
                    <h2 class="text-center mb-0">Esportazione Prenotazioni in Excel</h2>
                </div>
                <div class="card-body p-4">
                    {% if error %}
                    <div class="alert alert-danger">{{ error }}</div>
                    {% endif %}

                    {% if download_url %}
                    <div class="alert alert-success">
                        <p class="mb-2"><strong>Ottimo!</strong> Il report è stato generato con successo!</p>
                        <a href="{{ download_url }}" class="btn btn-primary mt-2">Scarica Report Excel</a>
                    </div>
                    {% endif %}                    <form method="post" class="mb-4" id="export-form">
                        {% csrf_token %}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_date" class="form-label fw-bold">Data di Inizio:</label>
                                <input type="date" name="start_date" id="start_date" class="form-control form-control-lg" required>
                            </div>
                            <div class="col-md-6">
                                <label for="end_date" class="form-label fw-bold">Data di Fine:</label>
                                <input type="date" name="end_date" id="end_date" class="form-control form-control-lg" required>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg" id="generate-btn">Genera Report</button>
                        </div>
                    </form>
                    
                    <div id="loading-indicator" class="text-center my-4 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Caricamento...</span>
                        </div>
                        <p class="mt-3 fs-5">Generazione del report Excel in corso, attendere prego...</p>
                    </div>                </div>
                <div class="card-footer text-muted py-3">
                    <small>Questo strumento genererà un report Excel di tutte le prenotazioni per tutte le proprietà nell'intervallo di date selezionato.</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('export-form');
        const generateBtn = document.getElementById('generate-btn');
        const loadingIndicator = document.getElementById('loading-indicator');
        
        // Set default dates
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        document.getElementById('start_date').valueAsDate = firstDayOfMonth;
        document.getElementById('end_date').valueAsDate = lastDayOfMonth;
        
        form.addEventListener('submit', function() {
            // Show loading indicator and disable button
            loadingIndicator.classList.remove('d-none');
            generateBtn.disabled = true;
            generateBtn.innerHTML = 'Generazione in corso...';
        });

        // CSS fallback mechanism
        function checkCSSLoaded() {
            const cssLink = document.getElementById('booking-export-css');
            if (cssLink) {
                cssLink.addEventListener('error', function() {
                    // CSS failed to load, apply inline styles
                    const style = document.createElement('style');
                    style.textContent = `
                        .booking-export .card-header {
                            background-color: #0d6efd !important;
                            color: white !important;
                        }
                        .booking-export .btn-primary {
                            background-color: #0d6efd !important;
                            border-color: #0d6efd !important;
                            transition: all 0.3s !important;
                        }
                        .booking-export .btn-primary:hover {
                            background-color: #0b5ed7 !important;
                            border-color: #0a58ca !important;
                            transform: translateY(-2px) !important;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
                        }
                        .booking-export .card {
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                            border-radius: 8px !important;
                            border: none !important;
                        }
                        .booking-export .form-control:focus {
                            border-color: #0d6efd !important;
                            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
                        }
                        .booking-export .spinner-border {
                            width: 3rem !important;
                            height: 3rem !important;
                        }
                    `;
                    document.head.appendChild(style);
                });
            }
        }

        checkCSSLoaded();
    });
</script>
{% endblock %}
