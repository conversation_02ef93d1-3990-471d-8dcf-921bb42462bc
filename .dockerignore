# Git
.git
.gitignore
.gitattributes
.github

# Documentation
README.md
*.md
docs/
MONITORING_QUICKSTART.md
CI_CD_SETUP_GUIDE.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
heibooky/logs/

# Database
*.db
*.sqlite3

# Media files (these should be served from S3/CDN in production)
heibooky/media/
media/

# Static files (will be collected during build)
heibooky/staticfiles/
staticfiles/

# Test files
.coverage
htmlcov/
.pytest_cache/
.tox/
coverage.xml
*.cover
.hypothesis/

# Backup files
*.bak
*.backup
backups/

# Temporary files
tmp/
temp/
.tmp/

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
scripts/deploy.sh
scripts/health-check.sh

# Monitoring
monitoring/

# Certificates
*.pem
*.key
*.crt
certbot/

# Secrets
secrets/
.env
.env.*

# Nginx
nginx/

# Redis
redis/

# Local development
.local/
local_settings.py
