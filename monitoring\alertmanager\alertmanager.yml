# Alertmanager Configuration
global:
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '${SMTP_FROM}'
  smtp_auth_username: '${SMTP_USERNAME}'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_auth_identity: '${SMTP_FROM}'
  smtp_require_tls: true
  http_config:
    tls_config:
      insecure_skip_verify: false
  resolve_timeout: 5m

templates:
  - '/etc/alertmanager/templates/*.tmpl'
  - './templates/*.tmpl'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default-receiver'
  routes:
    # SLO-based alerts - highest priority
    - match:
        slo: availability
        severity: critical
      receiver: 'slo-critical-alerts'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 5m
      continue: true

    - match:
        slo: latency
        severity: critical
      receiver: 'slo-critical-alerts'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 5m
      continue: true

    # Business impact alerts - immediate escalation
    - match:
        impact: business_critical
      receiver: 'business-critical-alerts'
      group_wait: 0s
      group_interval: 30s
      repeat_interval: 2m
      continue: true

    - match:
        impact: revenue
      receiver: 'revenue-impact-alerts'
      group_wait: 0s
      group_interval: 30s
      repeat_interval: 1m
      continue: true

    # Multi-window alerts - immediate attention
    - match:
        alert_type: multi_window
      receiver: 'multi-window-alerts'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 3m
      continue: true

    # Escalation alerts - management notification
    - match:
        escalation: level_2
      receiver: 'escalation-alerts'
      group_wait: 0s
      group_interval: 2m
      repeat_interval: 10m
      continue: true

    # Security alerts - immediate notification
    - match:
        alert_type: security
      receiver: 'security-alerts'
      group_wait: 30s
      group_interval: 1m
      repeat_interval: 15m
      continue: true

    # Capacity planning alerts
    - match:
        alert_type: capacity
      receiver: 'capacity-alerts'
      group_wait: 5m
      group_interval: 30m
      repeat_interval: 6h

    # Grouped alerts - reduce noise
    - match:
        alert_type: grouped
      receiver: 'grouped-alerts'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 30m

    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      group_interval: 2m
      repeat_interval: 30m
      continue: true

    # Warning alerts - less frequent
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 6h

    # Info alerts - daily digest
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 10m
      group_interval: 1h
      repeat_interval: 24h

receivers:
  - name: 'default-receiver'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true
        http_config:
          tls_config:
            insecure_skip_verify: false
        max_alerts: 0

  - name: 'slo-critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_SLO}'
        from: '${SMTP_FROM}'
        subject: '🚨 CRITICAL SLO VIOLATION: {{ .GroupLabels.alertname }}'
        html: '{{ template "slo-critical.tmpl" . }}'
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/slo-critical'
        send_resolved: true

  - name: 'business-critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_BUSINESS}'
        from: '${SMTP_FROM}'
        subject: '💼 BUSINESS CRITICAL: {{ .GroupLabels.alertname }}'
        html: '{{ template "business-critical.tmpl" . }}'
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'

  - name: 'revenue-impact-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_REVENUE}'
        from: '${SMTP_FROM}'
        subject: '💰 REVENUE IMPACT: {{ .GroupLabels.alertname }}'
        html: '{{ template "revenue-impact.tmpl" . }}'
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'

  - name: 'multi-window-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '⚡ MULTI-WINDOW ALERT: {{ .GroupLabels.alertname }}'
        html: '{{ template "multi-window.tmpl" . }}'

  - name: 'escalation-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_ESCALATION}'
        from: '${SMTP_FROM}'
        subject: '📈 ESCALATED ALERT: {{ .GroupLabels.alertname }}'
        html: '{{ template "escalation.tmpl" . }}'

  - name: 'capacity-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CAPACITY}'
        from: '${SMTP_FROM}'
        subject: '📊 CAPACITY PLANNING: {{ .GroupLabels.alertname }}'
        html: '{{ template "capacity.tmpl" . }}'

  - name: 'grouped-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '🔗 GROUPED ALERTS: Multiple Issues Detected'
        html: '{{ template "grouped.tmpl" . }}'

  - name: 'critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: '{{ template "critical.tmpl" . }}'
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true
        http_config:
          tls_config:
            insecure_skip_verify: false

  - name: 'security-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_SECURITY}'
        from: '${SMTP_FROM}'
        subject: '🔒 SECURITY ALERT: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: '{{ template "security.tmpl" . }}'
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true

  - name: 'warning-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_WARNING}'
        from: '${SMTP_FROM}'
        subject: '⚠️ WARNING: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: '{{ template "warning.tmpl" . }}'

  - name: 'info-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_INFO}'
        from: '${SMTP_FROM}'
        subject: 'ℹ️ INFO: Daily Monitoring Digest - {{ .GroupLabels.cluster }}'
        html: '{{ template "info.tmpl" . }}'

inhibit_rules:
  # SLO-based inhibition - SLO alerts take precedence
  - source_match:
      slo: 'availability'
      severity: 'critical'
    target_match:
      severity: 'critical'
    target_match_re:
      alertname: 'HighErrorRate|DjangoAppDown'
    equal: ['instance']

  - source_match:
      slo: 'latency'
      severity: 'critical'
    target_match:
      severity: 'critical'
    target_match_re:
      alertname: 'HighRequestLatency'
    equal: ['instance']

  # Business impact alerts take precedence over technical alerts
  - source_match:
      impact: 'business_critical'
    target_match:
      severity: 'warning'
    equal: ['instance']

  - source_match:
      impact: 'revenue'
    target_match:
      severity: 'warning'
    equal: ['instance']

  # Multi-window alerts inhibit single-window alerts
  - source_match:
      alert_type: 'multi_window'
    target_match_re:
      alertname: 'SLO.*BurnRate.*'
    equal: ['slo', 'instance']

  # Grouped alerts inhibit individual alerts
  - source_match:
      alert_type: 'grouped'
    target_match:
      severity: 'critical'
    equal: ['cluster']

  # Escalation alerts don't inhibit original alerts (continue: true handles this)

  # Standard severity-based inhibition
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance', 'job']

  # Inhibit info alerts when warning or critical alerts are firing
  - source_match:
      severity: 'warning'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance', 'job']

  - source_match:
      severity: 'critical'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance', 'job']

  # Dependency-aware inhibition
  - source_match:
      alertname: 'DjangoAppDown'
    target_match_re:
      alertname: '.*High.*|.*Slow.*|.*Error.*'
    equal: ['instance']

  # Inhibit downstream alerts when upstream service is down
  - source_match:
      alertname: 'RedisDown'
    target_match_re:
      alertname: '.*Cache.*|.*Session.*'
    equal: ['cluster']

  # Inhibit duplicate alerts from the same instance
  - source_match:
      alertname: 'InstanceDown'
    target_match_re:
      alertname: '.*'
    equal: ['instance']

  # Capacity alerts don't inhibit operational alerts
  - source_match:
      alert_type: 'capacity'
    target_match:
      alert_type: 'capacity'
    equal: ['alertname', 'instance']
