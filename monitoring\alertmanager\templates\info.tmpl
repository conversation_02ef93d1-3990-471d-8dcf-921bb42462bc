<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Info Alert</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .alert { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .details { margin-top: 10px; }
        .timestamp { font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <h2>ℹ️ Information Alert</h2>
    <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
    <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>

    {{ range .Alerts }}
    <div class="alert info">
        <h3>{{ .Annotations.summary }}</h3>
        <p><strong>Status:</strong> {{ .Status }}</p>
        <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
        <div class="details">
            <p>{{ .Annotations.description }}</p>
        </div>
        <p class="timestamp">Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
    </div>
    {{ end }}

    <p><small>This is an automated info alert from Heibooky Monitoring System</small></p>
</body>
</html>
