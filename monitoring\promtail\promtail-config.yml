server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: django-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: heibooky-django
          __path__: /var/log/heibooky/*.log
    pipeline_stages:
      - match:
          selector: '{filename="/var/log/heibooky/structured.log"}'
          stages:
            - json:
                expressions:
                  timestamp: timestamp
                  level: level
                  logger: logger
                  message: message
                  module: module
                  function: function
                  line: line
                  process_id: process_id
                  thread_id: thread_id
            - timestamp:
                source: timestamp
                format: RFC3339
            - labels:
                level:
                logger:
                module:
      - match:
          selector: '{filename="/var/log/heibooky/security.log"}'
          stages:
            - regex:
                expression: '(?P<level>\w+) \[(?P<timestamp>.*?)\] (?P<message>.*?) - Security Context: (?P<security_context>.*)'
            - timestamp:
                source: timestamp
                format: "2006-01-02 15:04:05"
            - labels:
                level:
                security_level: high
      - match:
          selector: '{filename="/var/log/heibooky/performance.log"}'
          stages:
            - regex:
                expression: '(?P<level>\w+) \[(?P<timestamp>.*?)\] (?P<message>.*?) - Duration: (?P<duration>.*?) - Memory: (?P<memory>.*)'
            - timestamp:
                source: timestamp
                format: "2006-01-02 15:04:05"
            - labels:
                level:
                performance_level:
