from allauth.socialaccount.models import SocialApp
from django.conf import settings
from django.contrib.sites.models import Site
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Setup social authentication applications"

    def handle(self, *args, **kwargs):
        try:
            # Ensure the Site object exists
            site, created = Site.objects.get_or_create(
                id=settings.SITE_ID,
                defaults={
                    "domain": "localhost:8000",
                    "name": "Heibooky",
                },
            )
            if created:
                self.stdout.write(self.style.SUCCESS("Created new Site object."))

            # Clean up any existing Google SocialApp
            SocialApp.objects.filter(provider="google").delete()

            # Create new SocialApp
            social_app = SocialApp.objects.create(
                provider="google",
                name="Google OAuth",
                client_id=settings.SOCIALACCOUNT_PROVIDERS["google"]["APP"][
                    "client_id"
                ],
                secret=settings.SOCIALACCOUNT_PROVIDERS["google"]["APP"]["secret"],
            )

            # Associate the SocialApp with the site
            social_app.sites.add(site)

            self.stdout.write(
                self.style.SUCCESS("Successfully set up Google OAuth application")
            )
        except KeyError as e:
            self.stderr.write(
                self.style.ERROR(f"Missing configuration in settings: {e}")
            )
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An error occurred: {e}"))
