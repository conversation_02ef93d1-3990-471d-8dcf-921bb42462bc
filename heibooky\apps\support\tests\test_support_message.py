import tempfile

from apps.support.models import MessageAttachment, SupportMessage
from apps.users.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import Client, TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient


class SupportMessageAPITestCase(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>", name="Test User", password="testpassword123"
        )

        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        # URL for support message endpoint
        self.url = reverse("message-list")

    def test_create_support_message_without_attachment(self):
        """Test creating a support message without any attachments"""
        data = {"message": "This is a test support message", "priority": "medium"}

        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(SupportMessage.objects.count(), 1)
        self.assertEqual(
            SupportMessage.objects.first().message, "This is a test support message"
        )

    def test_create_support_message_with_attachment(self):
        """Test creating a support message with an attachment"""
        # Create a temporary image file
        with tempfile.NamedTemporaryFile(suffix=".jpg") as image_file:
            # Write some dummy image data
            image_file.write(
                b"GIF87a\x01\x00\x01\x00\x80\x01\x00\x00\x00\x00ccc,\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02D\x01\x00;"
            )
            image_file.seek(0)

            # Prepare the data with the file
            data = {
                "message": "This is a test support message with attachment",
                "priority": "high",
                "attachments": SimpleUploadedFile(
                    name="test_image.jpg",
                    content=image_file.read(),
                    content_type="image/jpeg",
                ),
            }

            response = self.client.post(self.url, data, format="multipart")
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertEqual(SupportMessage.objects.count(), 1)
            self.assertEqual(MessageAttachment.objects.count(), 1)

    def test_create_support_message_with_invalid_attachment(self):
        """Test creating a support message with an invalid attachment"""
        # Create an invalid file (not an image)
        invalid_file = SimpleUploadedFile(
            name="test_file.txt",
            content=b"This is not an image file",
            content_type="text/plain",
        )

        data = {
            "message": "This is a test support message with invalid attachment",
            "priority": "medium",
            "attachments": invalid_file,
        }

        response = self.client.post(self.url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(SupportMessage.objects.count(), 0)

    def test_create_support_message_with_empty_message(self):
        """Test creating a support message with an empty message"""
        data = {"message": "", "priority": "low"}

        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(SupportMessage.objects.count(), 0)

    def test_create_support_message_with_binary_data_as_json(self):
        """Test creating a support message with binary data sent as JSON"""
        # This test simulates the error case where binary data is sent with JSON content type
        binary_data = b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82"

        # Use the Django test client directly to bypass DRF's content type handling
        client = Client()
        client.force_login(self.user)

        response = client.post(
            self.url, data=binary_data, content_type="application/json"
        )

        # Should return a 400 error, not a 500
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(SupportMessage.objects.count(), 0)
