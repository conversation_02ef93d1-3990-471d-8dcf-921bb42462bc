#!/bin/bash
set -e

# Heibooky Health Check Script
# This script performs comprehensive health checks on the Heibooky application

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HEALTH_CHECK_URL="${HEALTH_CHECK_URL:-http://localhost}"
TIMEOUT="${TIMEOUT:-30}"
VERBOSE="${VERBOSE:-false}"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Health check functions
check_web_service() {
    log "Checking web service health..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.txt --max-time "$TIMEOUT" "$HEALTH_CHECK_URL/health/" || echo "000")
    
    if [ "$response" = "200" ]; then
        log "✓ Web service is healthy"
        if [ "$VERBOSE" = true ]; then
            info "Response: $(cat /tmp/health_response.txt)"
        fi
        return 0
    else
        error "✗ Web service health check failed (HTTP $response)"
        if [ -f /tmp/health_response.txt ]; then
            error "Response: $(cat /tmp/health_response.txt)"
        fi
        return 1
    fi
}

check_database() {
    log "Checking database connectivity..."
    
    if docker-compose -f docker-compose.prod.yml exec -T web python -c "
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()
from django.db import connection
cursor = connection.cursor()
cursor.execute('SELECT 1')
print('Database connection successful')
" 2>/dev/null; then
        log "✓ Database is accessible"
        return 0
    else
        error "✗ Database connection failed"
        return 1
    fi
}

check_redis() {
    log "Checking Redis connectivity..."
    
    if docker-compose -f docker-compose.prod.yml exec -T web python -c "
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()
from django.core.cache import cache
cache.set('health_check', 'ok', timeout=10)
result = cache.get('health_check')
if result == 'ok':
    cache.delete('health_check')
    print('Redis connection successful')
else:
    raise Exception('Redis test failed')
" 2>/dev/null; then
        log "✓ Redis is accessible"
        return 0
    else
        error "✗ Redis connection failed"
        return 1
    fi
}

check_celery_worker() {
    log "Checking Celery worker status..."
    
    local worker_status=$(docker-compose -f docker-compose.prod.yml exec -T celery-worker celery -A heibooky inspect ping 2>/dev/null || echo "failed")
    
    if [[ "$worker_status" == *"pong"* ]]; then
        log "✓ Celery worker is running"
        return 0
    else
        error "✗ Celery worker is not responding"
        return 1
    fi
}

check_celery_beat() {
    log "Checking Celery beat status..."
    
    if docker-compose -f docker-compose.prod.yml ps celery-beat | grep -q "Up"; then
        log "✓ Celery beat is running"
        return 0
    else
        error "✗ Celery beat is not running"
        return 1
    fi
}

check_nginx() {
    log "Checking Nginx status..."
    
    if docker-compose -f docker-compose.prod.yml ps nginx | grep -q "Up"; then
        log "✓ Nginx is running"
        
        # Check if Nginx is serving requests
        local nginx_response=$(curl -s -w "%{http_code}" -o /dev/null --max-time 10 "$HEALTH_CHECK_URL" || echo "000")
        if [ "$nginx_response" = "200" ] || [ "$nginx_response" = "302" ]; then
            log "✓ Nginx is serving requests"
            return 0
        else
            warn "Nginx is running but not serving requests properly (HTTP $nginx_response)"
            return 1
        fi
    else
        error "✗ Nginx is not running"
        return 1
    fi
}

check_ssl_certificate() {
    log "Checking SSL certificate..."
    
    if [[ "$HEALTH_CHECK_URL" == https* ]]; then
        local hostname=$(echo "$HEALTH_CHECK_URL" | sed 's|https://||' | cut -d'/' -f1)
        
        # Use curl to check SSL certificate with timeout
        # HEAD request; capture only the verification result
        local cert_check_result=$(curl -s --max-time "$TIMEOUT" --connect-timeout 10 -I "$HEALTH_CHECK_URL" \
                                  -o /dev/null -w "SSL_VERIFY_RESULT:%{ssl_verify_result}" 2>/dev/null || echo "failed")
        if [[ "$cert_check_result" != "failed" ]] && [[ "$cert_check_result" == *"SSL_VERIFY_RESULT: 0"* ]]; then
            log "✓ SSL certificate is valid"
            
            # Get additional certificate info if verbose mode is enabled
            if [ "$VERBOSE" = true ]; then
                local cert_details=$(timeout "$TIMEOUT" openssl s_client -servername "$hostname" -connect "$hostname":443 -showcerts </dev/null 2>/dev/null | openssl x509 -noout -dates -subject 2>/dev/null || echo "Certificate details unavailable")
                info "Certificate info: $cert_details"
            fi
            return 0
        else
            error "✗ SSL certificate check failed"
            if [ "$VERBOSE" = true ]; then
                info "Certificate check result: $cert_check_result"
            fi
            return 1
        fi
    else
        info "Skipping SSL check (HTTP URL provided)"
        return 0
    fi
}

check_disk_space() {
    log "Checking disk space..."
    
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -lt 80 ]; then
        log "✓ Disk space is adequate ($disk_usage% used)"
        return 0
    elif [ "$disk_usage" -lt 90 ]; then
        warn "Disk space is getting low ($disk_usage% used)"
        return 0
    else
        error "✗ Disk space is critically low ($disk_usage% used)"
        return 1
    fi
}

check_memory_usage() {
    log "Checking memory usage..."
    
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$memory_usage" -lt 80 ]; then
        log "✓ Memory usage is normal ($memory_usage% used)"
        return 0
    elif [ "$memory_usage" -lt 90 ]; then
        warn "Memory usage is high ($memory_usage% used)"
        return 0
    else
        error "✗ Memory usage is critically high ($memory_usage% used)"
        return 1
    fi
}

check_docker_containers() {
    log "Checking Docker container status..."
    
    local failed_containers=()
    local containers=("web" "celery-worker" "celery-beat" "redis" "nginx")
    
    for container in "${containers[@]}"; do
        if docker-compose -f docker-compose.prod.yml ps "$container" | grep -q "Up"; then
            log "✓ Container $container is running"
        else
            error "✗ Container $container is not running"
            failed_containers+=("$container")
        fi
    done
    
    if [ ${#failed_containers[@]} -eq 0 ]; then
        return 0
    else
        error "Failed containers: ${failed_containers[*]}"
        return 1
    fi
}

# Main health check function
run_health_checks() {
    log "=== Heibooky Health Check Started ==="
    
    local failed_checks=0
    local total_checks=0
    
    # List of health check functions
    local checks=(
        "check_docker_containers"
        "check_web_service"
        "check_database"
        "check_redis"
        "check_celery_worker"
        "check_celery_beat"
        "check_nginx"
        "check_ssl_certificate"
        "check_disk_space"
        "check_memory_usage"
    )
    
    for check in "${checks[@]}"; do
        total_checks=$((total_checks + 1))
        if ! $check; then
            failed_checks=$((failed_checks + 1))
        fi
        echo ""
    done
    
    log "=== Health Check Summary ==="
    log "Total checks: $total_checks"
    log "Passed: $((total_checks - failed_checks))"
    log "Failed: $failed_checks"
    
    if [ $failed_checks -eq 0 ]; then
        log "🎉 All health checks passed!"
        return 0
    else
        error "❌ $failed_checks health check(s) failed"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --url)
            HEALTH_CHECK_URL="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            cat << EOF
Heibooky Health Check Script

Usage: $0 [OPTIONS]

Options:
    --url URL       Health check URL (default: http://localhost)
    --timeout SEC   Timeout for HTTP requests (default: 30)
    --verbose       Enable verbose output
    --help          Show this help message

Examples:
    $0
    $0 --url https://api.example.com --timeout 60 --verbose
EOF
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run health checks
run_health_checks
