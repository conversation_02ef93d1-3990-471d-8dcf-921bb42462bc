# Monitoring Stack Security Configuration
# This file contains security-related configurations and best practices

# Network Security
network_security:
  # Network isolation
  monitoring_network:
    subnet: "172.21.0.0/16"
    isolation: true
    ingress_rules:
      - port: 3000  # Grafana
        protocol: tcp
        source: "172.20.0.0/16"  # Backend network only
      - port: 9090  # Prometheus
        protocol: tcp
        source: "172.21.0.0/16"  # Monitoring network only
      - port: 9093  # Alertmanager
        protocol: tcp
        source: "172.21.0.0/16"  # Monitoring network only
      - port: 3100  # Loki
        protocol: tcp
        source: "172.21.0.0/16"  # Monitoring network only
    
    egress_rules:
      - port: 25    # SMTP
        protocol: tcp
        destination: "0.0.0.0/0"
      - port: 587   # SMTP TLS
        protocol: tcp
        destination: "0.0.0.0/0"
      - port: 443   # HTTPS
        protocol: tcp
        destination: "0.0.0.0/0"

# Authentication and Authorization
auth_config:
  grafana:
    # Strong password requirements
    password_policy:
      min_length: 12
      require_uppercase: true
      require_lowercase: true
      require_numbers: true
      require_symbols: true
      max_age_days: 90
    
    # Session security
    session:
      cookie_secure: true
      cookie_samesite: "strict"
      max_lifetime: "24h"
      idle_timeout: "2h"
    
    # User management
    users:
      allow_sign_up: false
      allow_org_create: false
      default_role: "Viewer"
      auto_assign_org: true
    
    # OAuth/LDAP integration (for production)
    oauth:
      enabled: false  # Enable in production with proper provider
      auto_login: false
      allow_assign_grafana_admin: false
    
    # API security
    api:
      disable_total_stats: true
      disable_sanitize_html: false

  prometheus:
    # Basic auth (if enabled)
    basic_auth:
      enabled: false  # Internal network only
    
    # Web security
    web:
      enable_lifecycle: false  # Disable in production
      enable_admin_api: false  # Disable in production
      max_connections: 512
      read_timeout: "30s"
      
  alertmanager:
    # Web security
    web:
      listen_address: "0.0.0.0:9093"
      external_url: "http://alertmanager:9093"
      route_prefix: "/"

# TLS/SSL Configuration
tls_config:
  grafana:
    enabled: true
    cert_file: "/etc/ssl/certs/grafana.crt"
    key_file: "/etc/ssl/private/grafana.key"
    protocols:
      - "TLSv1.2"
      - "TLSv1.3"
    ciphers:
      - "ECDHE-RSA-AES256-GCM-SHA384"
      - "ECDHE-RSA-AES128-GCM-SHA256"
      - "ECDHE-RSA-AES256-SHA384"
      - "ECDHE-RSA-AES128-SHA256"
    
  prometheus:
    enabled: false  # Internal communication only
    
  alertmanager:
    enabled: false  # Internal communication only
    
  loki:
    enabled: false  # Internal communication only

# Container Security
container_security:
  # Security options for all monitoring containers
  common:
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - "ALL"
    read_only_root_filesystem: true
    user_namespace: true
    
  # Specific configurations
  prometheus:
    user: "nobody:nobody"
    cap_add:
      - "CHOWN"
      - "SETGID"
      - "SETUID"
    tmpfs:
      - "/tmp:noexec,nosuid,size=100m"
      
  grafana:
    user: "472:472"  # grafana user
    cap_add:
      - "CHOWN"
      - "SETGID"
      - "SETUID"
    tmpfs:
      - "/tmp:noexec,nosuid,size=100m"
      - "/var/lib/grafana/plugins:noexec,nosuid,size=50m"
      
  alertmanager:
    user: "nobody:nobody"
    cap_add:
      - "CHOWN"
      - "SETGID"
      - "SETUID"
    tmpfs:
      - "/tmp:noexec,nosuid,size=50m"
      
  loki:
    user: "10001:10001"
    cap_add:
      - "CHOWN"
      - "SETGID"
      - "SETUID"
    tmpfs:
      - "/tmp:noexec,nosuid,size=100m"

# Secrets Management
secrets_management:
  # Environment variables for sensitive data
  environment_variables:
    - "GRAFANA_ADMIN_PASSWORD"
    - "GRAFANA_SECRET_KEY"
    - "SMTP_HOST"
    - "SMTP_PORT"
    - "SMTP_USERNAME"
    - "SMTP_PASSWORD"
    - "SMTP_FROM"
    - "ALERT_EMAIL_CRITICAL"
    - "ALERT_EMAIL_SECURITY"
    - "ALERT_EMAIL_WARNING"
    - "ALERT_EMAIL_INFO"
  
  # Docker secrets
  docker_secrets:
    - "redis_password"
    - "grafana_admin_password"
    - "smtp_credentials"
  
  # File permissions
  file_permissions:
    config_files: "0644"
    secret_files: "0600"
    directories: "0755"

# Logging and Auditing
security_logging:
  # Security events to log
  events:
    - "authentication_failures"
    - "authorization_failures"
    - "configuration_changes"
    - "admin_actions"
    - "suspicious_queries"
    - "rate_limit_violations"
  
  # Log retention
  retention:
    security_logs: "90d"
    audit_logs: "365d"
    access_logs: "30d"
  
  # Log forwarding
  forwarding:
    enabled: true
    destination: "loki:3100"
    format: "json"

# Rate Limiting and DDoS Protection
rate_limiting:
  grafana:
    login_attempts:
      max_attempts: 5
      window: "5m"
      lockout_duration: "15m"
    
    api_requests:
      requests_per_minute: 100
      burst: 200
  
  prometheus:
    query_rate:
      max_concurrent: 20
      timeout: "2m"
  
  alertmanager:
    webhook_rate:
      requests_per_minute: 60
      burst: 100

# Monitoring Security Metrics
security_metrics:
  # Metrics to track
  tracked_metrics:
    - "failed_login_attempts"
    - "suspicious_query_patterns"
    - "configuration_changes"
    - "certificate_expiry"
    - "security_scan_results"
  
  # Alert thresholds
  alert_thresholds:
    failed_logins: 10  # per 5 minutes
    suspicious_queries: 5  # per minute
    config_changes: 1  # immediate alert

# Compliance and Governance
compliance:
  # Data retention policies
  data_retention:
    metrics: "15d"
    logs: "30d"
    alerts: "90d"
    audit_trail: "365d"
  
  # Data privacy
  privacy:
    anonymize_ips: true
    mask_sensitive_data: true
    gdpr_compliance: true
  
  # Access controls
  access_controls:
    role_based: true
    principle_of_least_privilege: true
    regular_access_review: true

# Backup and Recovery Security
backup_security:
  # Encryption
  encryption:
    at_rest: true
    in_transit: true
    key_rotation: "90d"
  
  # Access controls
  access:
    backup_location: "secure_storage"
    access_logging: true
    integrity_checks: true
  
  # Recovery procedures
  recovery:
    documented_procedures: true
    tested_regularly: true
    rto_target: "4h"
    rpo_target: "1h"

# Security Scanning and Vulnerability Management
vulnerability_management:
  # Container scanning
  container_scanning:
    enabled: true
    scan_frequency: "daily"
    severity_threshold: "medium"
  
  # Dependency scanning
  dependency_scanning:
    enabled: true
    scan_frequency: "weekly"
    auto_update: false  # Manual review required
  
  # Configuration scanning
  config_scanning:
    enabled: true
    scan_frequency: "daily"
    compliance_frameworks:
      - "CIS"
      - "NIST"
