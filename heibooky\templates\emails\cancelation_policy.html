<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translations.cancelation_policy_set }}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f4f4;
            color: #113158;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(17, 49, 88, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: #ffffff;
            text-align: center;
            padding: 20px 0;
        }
        .logo-container img {
            width: 80px; 
            height: 80px; 
            border-radius: 50%;
        }
        .content {
            padding: 40px 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .section-title {
            color: #113158;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #FCB51F;
            padding-bottom: 5px;
        }
        .property-info {
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 10px;
            margin-bottom: 5px;
        }
        .label {
            font-weight: bold;
            color: #666;
        }
        .value {
            color: #113158;
        }
        .action-button {
            display: inline-block;
            background-color: #FCB51F;
            color: #113158;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            padding: 20px;
            font-size: 13px;
        }
        .footer a {
            color: #FCB51F;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <table role="presentation" class="email-container" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td class="header">
                <div class="logo-container">
                    <img src="{{ logo_url }}" alt="Heibooky">
                </div>
                <h1>{{ translations.admin_action_required }}</h1>
            </td>
        </tr>

        <tr>
            <td class="content">
                <div class="section">
                    <div class="section-title">{{ translations.property_details}}</div>
                    <div class="property-info">
                        <span class="label">Property Name</span>
                        <span class="value">{{ property.name }}</span>
                    </div>
                    <div class="property-info">
                        <span class="label">Property ID:</span>
                        <span class="value">{{ property.hotel_id }}</span>
                    </div>
                    <div class="property-info">
                        <span class="label">Chain ID:</span>
                        <span class="value">{{ property.chain_id }}</span>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">Requester Details</div>
                    <div class="property-info">
                        <span class="label">User Email:</span>
                        <span class="value">{{ user_email }}</span>
                    </div>
                    <div class="property-info">
                        <span class="label">Policy Type:</span>
                        <span class="value"><b>{{ cancelation_policy }}</b></span>
                    </div>
                </div>

                <div style="text-align: center;">
                    <a href="{{ admin_url }}" class="action-button">
                        {{ translations.view_request }}
                    </a>
                </div>
            </td>
        </tr>
        <tr>
            <td class="footer">
                &copy;2025 Heibooky. {{ translations.all_rights_reserved }}
            </td>
        </tr>
    </table>
</body>
</html>
