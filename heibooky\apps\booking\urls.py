from apps.booking.views import (
    BookingBlockViewSet,
    BookingCancellationRequestView,
    BookingCreateAPIView,
    BookingExportView,
    BookingTestAPIView,
    PropertyBookingListAPIView,
    PropertyBookingsAPIView,
    SuReservationWebhookView,
)
from django.urls import include, path
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r"booking-blocks", BookingBlockViewSet, basename="bookingblock")

urlpatterns = [
    path("", include(router.urls)),
    path(
        "property/", PropertyBookingListAPIView.as_view(), name="property-bookings-list"
    ),
    path(
        "property/<str:property_id>",
        PropertyBookingsAPIView.as_view(),
        name="property-bookings",
    ),
    path("manual-booking/", BookingCreateAPIView.as_view(), name="manual-booking"),
    path(
        "cancellation-request/",
        BookingCancellationRequestView.as_view(),
        name="booking-cancellation-request",
    ),
    path("test/", BookingTestAPIView.as_view(), name="booking-test"),
    path("export/", BookingExportView.as_view(), name="booking-export"),
    path(
        "webhook/reservation-push/",
        SuReservationWebhookView.as_view(),
        name="su-reservation-webhook",
    ),
]
