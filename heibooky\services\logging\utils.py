"""
Utility functions for logging services.
"""


def get_client_ip(request):
    """
    Get the client IP address from the request.

    This function handles various proxy configurations by checking:
    1. HTTP_X_FORWARDED_FOR header (most common proxy header)
    2. HTTP_X_REAL_IP header (nginx proxy)
    3. REMOTE_ADDR (direct connection)

    Args:
        request: Django HttpRequest object

    Returns:
        str: Client IP address or empty string if not available
    """
    if not request:
        return ""

    # Check X-Forwarded-For header (may contain multiple IPs)
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        # Take the first IP in the chain (original client)
        ip = x_forwarded_for.split(",")[0].strip()
        return ip

    # Check X-Real-IP header (single IP from nginx)
    x_real_ip = request.META.get("HTTP_X_REAL_IP")
    if x_real_ip:
        return x_real_ip.strip()

    # Fallback to REMOTE_ADDR (direct connection)
    remote_addr = request.META.get("REMOTE_ADDR", "")
    return remote_addr
