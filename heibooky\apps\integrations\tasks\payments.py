import logging
from datetime import datetime, timedelta
from decimal import Decimal

import stripe
from apps.booking.models import Booking
from apps.integrations.models import Invoice, Payout, StripeCustomer
from apps.stay.models import PropertyOwnership
from celery import shared_task
from django.conf import settings
from django.db.models import Q
from services.email import EmailService

logger = logging.getLogger(__name__)

stripe.api_key = settings.STRIPE_SECRET_KEY


@shared_task(name="apps.integrations.tasks.process_owner_payouts")
def process_owner_payouts():
    """
    Daily task to process payouts to property owners.
    """
    try:
        # Get available balance
        balance = stripe.Balance.retrieve()
        available_amount = (
            Decimal(balance.available[0].amount) / 100
        )  # Convert from cents

        # Get completed bookings pending payout
        pending_bookings = Booking.objects.filter(
            Q(status=Booking.Status.COMPLETED)
            & Q(payment_processed=False)
            & Q(checkout_date__lte=datetime.now().date())
            & Q(is_manual=False)
        )

        # Calculate total required amount
        required_amount = sum(
            booking.reservation_data.total_price for booking in pending_bookings
        )

        # Check if we have sufficient funds
        if available_amount < required_amount:
            # Send insufficient funds notification
            email_service = EmailService()
            email_service.send_insufficient_funds_notification(
                required_amount=required_amount,
                available_amount=available_amount,
                affected_bookings=list(pending_bookings),
            )
            logger.error(
                f"Insufficient funds: Required {required_amount}, Available {available_amount}"
            )
            return

        for booking in pending_bookings:
            try:
                owner = PropertyOwnership.objects.get(property=booking.property).user
                customer = StripeCustomer.objects.get(user=owner)
                payout_amount = booking.reservation_data.total_price

                if available_amount < payout_amount:
                    logger.error(f"Insufficient funds for booking {booking.id}")
                    continue

                # Create transfer to Connect account
                transfer = stripe.Transfer.create(
                    amount=int(payout_amount * 100),  # Convert to cents
                    currency="eur",
                    destination=customer.stripe_customer_id,
                    transfer_group=f"booking_{booking.id}",
                    metadata={
                        "booking_id": str(booking.id),
                        "property_id": str(booking.property.id),
                    },
                )

                # Create payout record
                Payout.objects.create(
                    customer=customer,
                    stripe_payment_intent_id=transfer.id,
                    amount=payout_amount,
                    status="successful",
                    booking=booking,
                )

                # Update booking status
                booking.payment_processed = True
                booking.save()

                available_amount -= payout_amount

            except stripe.error.StripeError as e:
                logger.error(
                    f"Stripe error processing payout for booking {booking.id}: {str(e)}"
                )
                continue
            except Exception as e:
                logger.error(
                    f"Error processing payout for booking {booking.id}: {str(e)}"
                )
                continue

    except Exception as e:
        logger.error(f"Error in process_owner_payouts: {str(e)}")
        raise


@shared_task(name="apps.integrations.tasks.monitor_stripe_transfers")
def monitor_stripe_transfers():
    """
    Task to monitor and reconcile pending transfers.
    """
    try:
        # Check transfers from the last 24 hours
        yesterday = datetime.now() - timedelta(days=1)
        pending_payouts = Payout.objects.filter(
            created_at__gte=yesterday, status="pending"
        )

        for payout in pending_payouts:
            try:
                transfer = stripe.Transfer.retrieve(payout.stripe_payment_intent_id)

                if transfer.status == "failed":
                    payout.status = "failed"
                elif transfer.status == "paid":
                    payout.status = "successful"

                payout.save()

            except stripe.error.StripeError as e:
                logger.error(
                    f"Error checking transfer {payout.stripe_payment_intent_id}: {str(e)}"
                )
                continue

    except Exception as e:
        logger.error(f"Error in monitor_stripe_transfers: {str(e)}")
        raise


@shared_task(name="apps.integrations.tasks.generate_digithera_invoice_task")
def generate_digithera_invoice_task(invoice_id=None):
    """
    Process a specific invoice if invoice_id is provided,
    otherwise process all invoices with pending or failed SDI status
    """
    try:
        if invoice_id:
            invoices = Invoice.objects.filter(id=invoice_id)
        else:
            invoices = Invoice.objects.filter(sdi_status__in=["pending", "failed"])

        for invoice in invoices:
            try:
                success = invoice.generate_digithera_invoice()
                if not success:
                    invoice.processing_errors = {"error": "Digithera upload failed"}
                    invoice.save()
            except Exception as e:
                logger.error(f"Error processing invoice {invoice.id}: {str(e)}")
                invoice.processing_errors = {"error": str(e)}
                invoice.save()
                continue

    except Exception as e:
        logger.error(f"Error in generate_digithera_invoices_task: {str(e)}")
        raise
