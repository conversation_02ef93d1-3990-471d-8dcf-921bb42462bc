import logging

from apps.stay.models import Photo, PropertyMetadata, Room

logger = logging.getLogger(__name__)


def check_property_setup_status(property_obj):
    """
    Check if a property has completed all required setup steps.

    Args:
        property_obj: Property instance to check

    Returns:
        tuple: (is_complete, missing_items, status_data)
            - is_complete: <PERSON><PERSON><PERSON> indicating if setup is complete
            - missing_items: List of setup items that are missing
            - status_data: Dictionary with detailed status for each section
    """
    # Calculate status for each section
    metadata_status = (
        hasattr(property_obj, "metadata")
        and property_obj.metadata.regional_id_code
        and len(property_obj.metadata.regional_id_code.strip()) > 0
    )

    location_status = bool(
        property_obj.location
        and all(
            [
                property_obj.location.street,
                property_obj.location.post_code,
                property_obj.location.city,
                property_obj.location.country,
            ]
        )
    )

    amenities_status = property_obj.amenities.exists()

    rooms_status = Room.objects.filter(
        property=property_obj,
    ).exists()

    photos_count = Photo.objects.filter(property=property_obj).count()
    photos_status = photos_count >= 5

    guest_arrival_status = hasattr(property_obj, "guest_arrival_info")
    if guest_arrival_status:
        guest_info = property_obj.guest_arrival_info
        guest_arrival_status = all(
            [
                guest_info.contact_name,
                guest_info.contact_surname,
                guest_info.email,
                guest_info.phone_number,
            ]
        )

    description_status = bool(
        property_obj.description and len(property_obj.description.strip()) > 0
    )

    # Create status data dictionary similar to check_status
    status_data = {
        "general_info": metadata_status,
        "location": location_status,
        "amenities": amenities_status,
        "rooms": rooms_status,
        "pictures": photos_status,
        "guest_arrival": guest_arrival_status,
        "description": description_status,
    }

    # Compile missing items list based on status checks
    missing_items = []

    if not metadata_status:
        missing_items.append("Informazioni di base sulla proprietà")

    if not location_status:
        missing_items.append("Informazioni sulla posizione")

    if not amenities_status:
        missing_items.append("Servizi offerti")

    if not rooms_status:
        missing_items.append("Configurazione delle camere")

    if not photos_status:
        missing_items.append("Foto della proprietà (minimo 5 foto)")

    if not guest_arrival_status:
        missing_items.append("Informazioni di contatto per gli ospiti")

    if not description_status:
        missing_items.append("Descrizione della proprietà")

    # Property setup is complete if all status checks pass
    is_complete = all(status_data.values())

    return is_complete, missing_items, status_data


def update_property_setup_status(property_obj):
    """
    Update the is_setup_complete field on the property metadata.

    Args:
        property_obj: Property instance to update

    Returns:
        tuple: (is_complete, status_data)
            - is_complete: Boolean indicating if setup is complete
            - status_data: Dictionary with detailed status for each section
    """
    try:
        is_complete, _, status_data = check_property_setup_status(property_obj)

        # Get or create property metadata
        metadata, created = PropertyMetadata.objects.get_or_create(
            property=property_obj
        )

        # Update setup status if it has changed
        if metadata.is_setup_complete != is_complete:
            metadata.is_setup_complete = is_complete
            metadata.save(update_fields=["is_setup_complete"])

            logger.info(
                f"Updated setup status for property {property_obj.id} to {is_complete}"
            )

        return is_complete, status_data

    except Exception as e:
        logger.error(f"Error updating property setup status: {str(e)}", exc_info=True)
        return False, {}
