import io
from unittest.mock import patch

from apps.stay.models import Location, Photo, Property
from apps.stay.tasks import process_property_cover_image
from apps.users.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from PIL import Image


class PropertyCoverImageTest(TestCase):
    """Test the property cover image functionality"""

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", name="Test User"
        )

        # Create a test location
        self.location = Location.objects.create(
            street="Test Street",
            city="Test City",
            post_code="12345",
            country="IT",
            latitude=41.9028,
            longitude=12.4964,
        )

        # Create a test property
        self.property = Property.objects.create(
            name="Test Property", property_type=Property.HOTEL, location=self.location
        )

        # Add the user as staff
        self.property.staffs.add(self.user)

        # Create a test image
        self.test_image = self._create_test_image()

    def tearDown(self):
        """Clean up after tests"""
        # D<PERSON><PERSON>'s TestCase will clean up the files automatically
        # We just need to make sure all objects are deleted
        Photo.objects.all().delete()
        self.property.refresh_from_db()
        if self.property.cover_image:
            self.property.cover_image = None
            self.property.save(update_fields=["cover_image"])

    def _create_test_image(self):
        """Create a test image file"""
        # Create a test image using PIL
        image = Image.new("RGB", (100, 100), color="red")
        image_io = io.BytesIO()
        image.save(image_io, format="JPEG")
        image_io.seek(0)

        return SimpleUploadedFile(
            name="test_image.jpg", content=image_io.read(), content_type="image/jpeg"
        )

    @patch("apps.stay.tasks.process_property_cover_image.delay")
    def test_cover_image_signal_on_create(self, mock_task):
        """Test that the cover image task is called when a photo is created"""
        # Create a photo
        photo = Photo.objects.create(
            property=self.property, image=self._create_test_image()
        )

        # Check that the task was called
        mock_task.assert_called_once()
        args = mock_task.call_args[1]
        self.assertEqual(args["property_id"], str(self.property.id))
        self.assertEqual(args["photo_id"], str(photo.id))
        self.assertEqual(args["action"], "create")

    def test_process_cover_image_task(self):
        """Test the process_cover_image task"""
        # Create a photo
        photo = Photo.objects.create(
            property=self.property, image=self._create_test_image()
        )

        # Initially, the property should not have a cover image
        self.assertFalse(bool(self.property.cover_image))

        # Run the task
        process_property_cover_image(
            property_id=str(self.property.id), photo_id=str(photo.id), action="create"
        )

        # Refresh the property from the database
        self.property.refresh_from_db()

        # Now the property should have a cover image
        self.assertTrue(bool(self.property.cover_image))
        self.assertIsNotNone(self.property.cover_image.name)

    def test_update_cover_image_on_delete(self):
        """Test updating the cover image when the first photo is deleted"""
        # Create two photos
        photo1 = Photo.objects.create(
            property=self.property, image=self._create_test_image()
        )

        # Create a second photo that will be used after the first is deleted
        Photo.objects.create(property=self.property, image=self._create_test_image())

        # Run the task to set the initial cover image
        process_property_cover_image(
            property_id=str(self.property.id), photo_id=str(photo1.id), action="create"
        )

        # Refresh the property from the database
        self.property.refresh_from_db()

        # Store the initial cover image name
        initial_cover_name = self.property.cover_image.name

        # Delete the first photo
        photo1.delete()

        # Run the task to update the cover image
        process_property_cover_image(
            property_id=str(self.property.id), photo_id=str(photo1.id), action="delete"
        )

        # Refresh the property from the database
        self.property.refresh_from_db()

        # The property should still have a cover image, but it should be different
        self.assertTrue(bool(self.property.cover_image))
        self.assertNotEqual(self.property.cover_image.name, initial_cover_name)

    def test_remove_cover_image_when_all_photos_deleted(self):
        """Test removing the cover image when all photos are deleted"""
        # Create a photo
        photo = Photo.objects.create(
            property=self.property, image=self._create_test_image()
        )

        # Run the task to set the initial cover image
        process_property_cover_image(
            property_id=str(self.property.id), photo_id=str(photo.id), action="create"
        )

        # Refresh the property from the database
        self.property.refresh_from_db()

        # The property should have a cover image
        self.assertTrue(bool(self.property.cover_image))

        # Delete the photo
        photo.delete()

        # Run the task to update the cover image
        process_property_cover_image(
            property_id=str(self.property.id), photo_id=str(photo.id), action="delete"
        )

        # Refresh the property from the database
        self.property.refresh_from_db()

        # The property should no longer have a cover image
        self.assertFalse(bool(self.property.cover_image))
