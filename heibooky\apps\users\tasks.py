import json
import logging
from typing import Optional

from celery import shared_task
from celery.exceptions import Retry
from django.utils import timezone
from services.email import AccountEmailService, EmailService, VerificationService

from .models import User
from .utils import get_device_info, get_location_data, should_notify_location_change

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_welcome_email(self, email: str):
    """
    Task to send a welcome email to a new user.
    This task is executed asynchronously with retry logic.
    """
    try:
        # Validate user exists
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            logger.error(f"User with email {email} not found for welcome email")
            return False

        # Initialize email service
        email_service = AccountEmailService()
        success = email_service.send_welcome_email(user)

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"Welcome email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to send welcome email to {email}: {str(e)}", exc_info=True
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60  # 60s, 120s, 240s
            raise self.retry(exc=e, countdown=retry_delay)

        # Final failure - log and don't raise to prevent task failure
        logger.critical(
            f"Failed to send welcome email to {email} after {self.max_retries} retries"
        )
        return False


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_password_reset_email_task(self, email: str, code: str):
    """
    Task to send a password reset email to the user.
    This task is executed asynchronously with retry logic.
    """
    try:
        # Validate user exists
        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            logger.error(f"User with email {email} not found for password reset")
            return False

        # Initialize email service
        email_service = AccountEmailService()
        success = email_service.send_password_reset_email(email, code)

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"Password reset email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to send password reset email to {email}: {str(e)}", exc_info=True
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(exc=e, countdown=retry_delay)

        logger.critical(
            f"Failed to send password reset email to {email} after {self.max_retries} retries"
        )
        return False


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_verification_email_task(self, email: str, code: str):
    """
    Task to send a verification email to the user.
    This task is executed asynchronously with retry logic.
    """
    try:
        # Validate user exists
        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            logger.error(f"User with email {email} not found for verification")
            return False

        # Initialize email service
        email_service = AccountEmailService()
        success = email_service.send_verification_email(email, code)

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"Verification email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to send verification email to {email}: {str(e)}", exc_info=True
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(exc=e, countdown=retry_delay)

        logger.critical(
            f"Failed to send verification email to {email} after {self.max_retries} retries"
        )
        return False


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_account_deletion_email_task(self, email: str, name: Optional[str] = None):
    """
    Task to send account deletion confirmation email.
    This task is executed asynchronously with retry logic.
    """
    try:
        email_service = AccountEmailService()
        success = email_service.send_account_deletion_email(email, name)

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"Account deletion email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to send account deletion email to {email}: {str(e)}", exc_info=True
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(exc=e, countdown=retry_delay)

        logger.critical(
            f"Failed to send account deletion email to {email} after {self.max_retries} retries"
        )
        return False


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_password_changed_email_task(self, email: str, context: dict):
    """
    Task to send password changed notification email.
    This task is executed asynchronously with retry logic.
    """
    try:
        # Validate user exists
        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            logger.error(
                f"User with email {email} not found for password change notification"
            )
            return False

        email_service = AccountEmailService()
        success = email_service.send_password_changed_email(email, context)

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"Password changed email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to send password changed email to {email}: {str(e)}", exc_info=True
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(exc=e, countdown=retry_delay)

        logger.critical(
            f"Failed to send password changed email to {email} after {self.max_retries} retries"
        )
        return False


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_new_login_location_email_task(self, email: str, context: dict):
    """
    Task to send new login location notification email.
    This task is executed asynchronously with retry logic.
    """
    try:
        # Validate user exists
        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            logger.error(
                f"User with email {email} not found for login location notification"
            )
            return False

        email_service = AccountEmailService()
        success = email_service.send_new_login_location_email(email, context)

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"New login location email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to send new login location email to {email}: {str(e)}",
            exc_info=True,
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(exc=e, countdown=retry_delay)

        logger.critical(
            f"Failed to send new login location email to {email} after {self.max_retries} retries"
        )
        return False


# Helper task for generating and sending verification codes
@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def generate_and_send_verification_code(self, email: str, action: str = "verification"):
    """
    Generate verification code and send email asynchronously.

    Args:
        email: User's email address
        action: Type of verification (verification, password_reset, etc.)
    """
    try:
        # Validate user exists for certain actions
        if action in ["verification", "password_reset"]:
            try:
                User.objects.get(email=email)
            except User.DoesNotExist:
                logger.error(f"User with email {email} not found for {action}")
                return False

        # Generate verification code
        verification_service = VerificationService()
        code = verification_service.generate_code()

        # Store the code
        verification_service.store_code(email, code)

        # Send appropriate email based on action
        email_service = AccountEmailService()

        if action == "verification":
            success = email_service.send_verification_email(email, code)
        elif action == "password_reset":
            success = email_service.send_password_reset_email(email, code)
        else:
            raise ValueError(f"Unknown action: {action}")

        if not success:
            raise Exception("Email service returned failure")

        logger.info(f"{action.title()} code sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(
            f"Failed to generate and send {action} code to {email}: {str(e)}",
            exc_info=True,
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(exc=e, countdown=retry_delay)

        logger.critical(
            f"Failed to send {action} code to {email} after {self.max_retries} retries"
        )
        return False


def get_previous_location(profile):
    """Retrieve and parse previous location data"""
    if not profile.last_login_location:
        return None

    loc = profile.last_login_location
    if isinstance(loc, str):
        try:
            return json.loads(loc)
        except json.JSONDecodeError:
            return loc
    return loc


def send_notification(user, location, device, login_time):
    """Handle notification email sending"""
    try:
        email_service = AccountEmailService()
        email_service.send_new_login_location_email(
            user.email,
            {
                "user_name": user.name,
                "login_time": login_time.strftime("%Y-%m-%d %H:%M:%S"),
                "location": location["location_string"],
                "device": device.get("device_string", "Unknown"),
                "ip_address": location["ip_address"],
            },
        )
    except Exception as e:
        logger.error(f"Failed to send notification email: {str(e)}")


def update_profile_location(profile, location):
    """Update profile with new location data"""
    try:
        profile.last_login_location = location
        profile.save(update_fields=["last_login_location"])
    except Exception as e:
        logger.error(f"Failed to update profile location: {str(e)}")


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def login_location_task(self, user_id, ip_address, user_agent, login_time):
    """Enhanced login location processing task"""
    try:
        # Get user and ensure they exist
        try:
            user = User.objects.select_related("profile").get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User {user_id} not found for login location processing")
            return

        # Get location and device info
        login_time = timezone.datetime.fromisoformat(login_time)
        location_string, location_data = get_location_data(ip_address)
        device_info = get_device_info(user_agent)

        # Prepare current location data
        current_location = {
            "location_string": location_string,
            "login_time": login_time.isoformat(),
            "ip_address": ip_address,
            **(location_data or {}),
        }

        # Get previous location and check for changes
        previous_location = user.profile.last_login_location
        location_changed = should_notify_location_change(
            previous_location, current_location
        )

        if location_changed:
            try:
                notification_data = {
                    "user_name": user.name,
                    "login_time": login_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "location": location_string,
                    "device": device_info.get("device_string", "Unknown device"),
                    "previous_location": (
                        previous_location.get("location_string", "Unknown")
                        if isinstance(previous_location, dict)
                        else str(previous_location or "Unknown")
                    ),
                    "ip_address": ip_address,
                }

                # Send email asynchronously
                send_new_login_location_email_task.delay(user.email, notification_data)
                logger.info(f"Login location notification queued for {user.email}")

            except Exception as e:
                logger.error(f"Failed to queue location notification: {str(e)}")
                # Don't retry the whole task for notification failure

        # Update profile location
        try:
            user.profile.last_login_location = current_location
            user.profile.save(update_fields=["last_login_location"])
        except Exception as e:
            logger.error(f"Failed to update login location: {str(e)}")
            raise self.retry(exc=e)

    except Exception as e:
        logger.error(f"Login location task failed: {str(e)}", exc_info=True)
        raise self.retry(exc=e)
