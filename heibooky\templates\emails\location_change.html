<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translations.location_change_notification }}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f4f4;
            color: #113158;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(17, 49, 88, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: #ffffff;
            text-align: center;
            padding: 20px 0;
        }
        .logo-container img {
            width: 80px; 
            height: 80px; 
            border-radius: 50%;
        }
        .content {
            padding: 40px 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .section-title {
            color: #113158;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #FCB51F;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 10px;
            margin-bottom: 5px;
        }
        .label {
            font-weight: bold;
            color: #666;
        }
        .value {
            color: #113158;
        }
        .footer {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            padding: 20px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="email-container" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td class="header">
                <div class="logo-container">
                    <img src="{{ logo_url }}" alt="Heibooky">
                </div>
                <h1>{{ translations.location_change_notification }}</h1>
            </td>
        </tr>

        <tr>
            <td class="content">
                <div class="section">
                    <div class="section-title">{{ translations.location_details }}</div>
                    <div class="info-grid">
                        <span class="label">{{ translations.street }}:</span>
                        <span class="value">{{ location.street }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.post_code }}:</span>
                        <span class="value">{{ location.post_code }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.city }}:</span>
                        <span class="value">{{ location.city }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.country }}:</span>
                        <span class="value">{{ location.country }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.latitude }}:</span>
                        <span class="value">{{ location.latitude }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.longitude }}:</span>
                        <span class="value">{{ location.longitude }}</span>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">{{ translations.affected_properties }}</div>
                    {% for property in properties %}
                    <div class="info-grid">
                        <span class="label">{{ property.name }}</span>
                        <span class="value">ID: {{ property.hotel_id }}</span>
                    </div>
                    {% endfor %}
                </div>

                <div class="section">
                    <div class="section-title">{{ translations.location_changed_by }}</div>
                    <div class="info-grid">
                        <span class="label">{{ translations.user_name }}:</span>
                        <span class="value">{{ user.name|default:translations.no_user_info }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.user_email }}:</span>
                        <span class="value">{{ user.email|default:translations.no_user_info }}</span>
                    </div>
                    <div class="info-grid">
                        <span class="label">{{ translations.changed_at }}:</span>
                        <span class="value">{{ changed_at|date:"d/m/Y H:i" }}</span>
                    </div>
                </div>
            </td>
        </tr>

        <tr>
            <td class="footer">
                &copy; 2025 Heibooky. {{ translations.all_rights_reserved }}
            </td>
        </tr>
    </table>
</body>
</html>
