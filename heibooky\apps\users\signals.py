import logging

from allauth.socialaccount.models import SocialAccount
from apps.users.models import User
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from services.email import AccountEmailService

logger = logging.getLogger(__name__)


@receiver(post_save, sender=SocialAccount)
def update_user_data(sender, instance, created, **kwargs):
    """
    Signal handler to update user data when a social account is created/updated.
    """
    if instance.provider == "google":
        user = instance.user
        extra_data = instance.extra_data

        # Update user data from Google
        update_fields = []
        if not user.name and "name" in extra_data:
            user.name = extra_data["name"]
            update_fields.append("name")

        if not user.is_verified:
            user.is_verified = True
            update_fields.append("is_verified")

        if update_fields:
            user.save(update_fields=update_fields)


@receiver(pre_delete, sender=User)
def send_account_deletion_email(sender, instance, **kwargs):
    """
    Signal to send an email notification to a user when their account is about to be deleted.
    """
    email_service = AccountEmailService()
    try:
        email_service.send_account_deletion_email(
            email=instance.email, name=instance.name
        )
    except Exception as e:
        logger.error(
            f"Failed to send account deletion email for user {instance.email}: {str(e)}"
        )
