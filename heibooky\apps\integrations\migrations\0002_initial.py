# Generated by Django 5.1.2 on 2025-06-28 07:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("booking", "0002_initial"),
        ("integrations", "0001_initial"),
        ("stay", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="invoice",
            name="owner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notifications",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="payout",
            name="booking",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="payouts",
                to="booking.booking",
            ),
        ),
        migrations.AddField(
            model_name="invoice",
            name="payout",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invoice",
                to="integrations.payout",
            ),
        ),
        migrations.AddField(
            model_name="propertyonlinecheckin",
            name="property",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="online_checkin",
                to="stay.property",
                verbose_name="Property",
            ),
        ),
        migrations.AddField(
            model_name="stripecustomer",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="stripe_customer",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="payout",
            name="customer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="payouts",
                to="integrations.stripecustomer",
            ),
        ),
        migrations.AddField(
            model_name="suapiactionlog",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="su_api_logs",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
