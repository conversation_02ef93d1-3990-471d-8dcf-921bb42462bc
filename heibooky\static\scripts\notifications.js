let notifications = [];
let socket = null;

async function loadNotifications() {
    console.log('Loading notifications...');
    const token = localStorage.getItem('access_token');
    try {
        const response = await fetch('/integrations/notifications/', {
            headers: { 'Authorization': `Bear<PERSON> ${token}` }
        });
        if (!response.ok) throw new Error('Failed to load notifications');
        const data = await response.json();
        notifications = Array.isArray(data) ? data : [];
        console.log(`Loaded ${notifications.length} notifications`);
        displayNotifications();
    } catch (error) {
        console.error('Error loading notifications:', error);
        showError('Failed to load notifications');
    }
}

function displayNotifications() {
    console.log('Displaying notifications:', notifications);
    const container = document.getElementById('notifications');

    if (!Array.isArray(notifications)) {
        console.error('Notifications is not an array:', notifications);
        notifications = [];
        showError('Invalid notification data');
        return;
    }

    container.innerHTML = notifications
        .filter(notification => notification && typeof notification === 'object')
        .map(notification => {
            const {
                id = '',
                title = 'Untitled',
                message = 'No message',
                is_read = false,
                created_at = new Date().toISOString(),
                read_at = null
            } = notification || {};

            return `
                <div class="notification ${is_read ? 'read' : 'unread'}"
                        id="notification-${id}">
                    <div class="read-marker">
                        ${is_read ?
                            '<span>✓ Read</span>' :
                            `<button class="mark-as-read" data-id="${id}">Mark as Read</button>`
                        }
                    </div>
                    <h3>${title}</h3>
                    <p>${message}</p>
                    <small>
                        Created: ${new Date(created_at).toLocaleString()}
                        ${read_at ?
                            `<br>Read: ${new Date(read_at).toLocaleString()}` :
                            ''}
                    </small>
                </div>
            `;
        }).join('');

    // Add event listeners for "Mark as Read" buttons
    document.querySelectorAll('.mark-as-read').forEach(button => {
        button.addEventListener('click', function() {
            markAsRead(this.getAttribute('data-id'));
        });
    });
}

function connect() {
    console.log('Initiating WebSocket connection...');
    const token = localStorage.getItem('access_token');
    if (!token) {
        console.error('No authentication token found');
        showError('Authentication required');
        return;
    }

    // Close existing connection if any
    if (socket && socket.readyState !== WebSocket.CLOSED) {
        console.log('Closing existing connection before reconnecting');
        socket.close();
    }

    const wsScheme = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // Use the current domain for WebSocket connection
    // This ensures the connection is made to the same domain the page is served from
    const wsUrl = `${wsScheme}//${window.location.host}/ws/notifications/`;

    // Append token as query parameter
    const wsUrlWithToken = `${wsUrl}?token=${encodeURIComponent(token)}`;

    console.log('Connecting to:', wsUrl);
    updateConnectionStatus('Connecting...', false);

    try {
        socket = new WebSocket(wsUrlWithToken);

        socket.onopen = function() {
            console.log('WebSocket connection established');
            updateConnectionStatus('Connected', true);
            loadNotifications();

            // Send a ping to keep the connection alive
            startPingInterval();
        };

        socket.onmessage = function(e) {
            try {
                const message = JSON.parse(e.data);
                console.log('Received message:', message);
                if (message.type === 'notification' && message.data) {
                    // Add new notification to the beginning of the array
                    notifications.unshift(message.data);
                    displayNotifications();
                } else if (message.type === 'notification_update') {
                    updateNotificationStatus(message.data);
                } else if (message.type === 'pong') {
                    console.log('Received pong from server');
                }
            } catch (error) {
                console.error('Error processing message:', error);
                showError('Error processing notification');
            }
        };

        socket.onclose = function(e) {
            console.log('WebSocket connection closed. Code:', e.code, 'Reason:', e.reason);
            updateConnectionStatus('Disconnected', false);
            stopPingInterval();

            // Attempt to reconnect after a delay if not closed intentionally
            if (e.code !== 1000) {
                setTimeout(() => {
                    console.log('Attempting to reconnect...');
                    connect();
                }, 5000); // Try to reconnect after 5 seconds
            }
        };

        socket.onerror = function(e) {
            console.error('WebSocket error:', e);
            showError('Connection error occurred. Check console for details.');
        };
    } catch (error) {
        console.error('Error creating WebSocket:', error);
        updateConnectionStatus('Connection Failed', false);
        showError('Failed to establish connection');
    }
}

// Keep-alive ping interval
let pingInterval = null;

function startPingInterval() {
    // Clear any existing interval
    stopPingInterval();

    // Send a ping every 30 seconds to keep the connection alive
    pingInterval = setInterval(() => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            console.log('Sending ping to server');
            socket.send(JSON.stringify({ action: 'ping' }));
        }
    }, 30000);
}

function stopPingInterval() {
    if (pingInterval) {
        clearInterval(pingInterval);
        pingInterval = null;
    }
}

function markAsRead(notificationId) {
    console.log(`Marking notification ${notificationId} as read`);
    if (socket?.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({
            action: 'mark_as_read',
            notification_ids: [notificationId]
        }));
    } else {
        console.error('WebSocket not connected');
        showError('Connection lost. Please reconnect.');
    }
}

function updateNotificationStatus(data) {
    console.log('Updating notification status:', data);
    const notification = notifications.find(n => n.id === data.id);
    if (notification) {
        notification.is_read = data.is_read;
        notification.read_at = new Date().toISOString();
        displayNotifications();
    }
}

function updateConnectionStatus(status, isConnected) {
    const statusEl = document.getElementById('status');
    statusEl.textContent = status;
    statusEl.style.color = isConnected ? 'green' : 'red';
}

function showError(message) {
    const errorEl = document.getElementById('error');
    errorEl.textContent = message;
    errorEl.style.display = 'block';
    setTimeout(() => errorEl.style.display = 'none', 5000);
}

// Add event listener after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('connect-button').addEventListener('click', connect);
});
