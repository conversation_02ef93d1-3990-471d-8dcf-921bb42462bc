from apps.billing.models import Billing<PERSON>ddress, BillingProfile, Taxation
from apps.billing.serializers import (
    BillingAddressSerializer,
    BillingProfileSerializer,
    TaxationSerializer,
)
from django.db import IntegrityError
from rest_framework import status, viewsets
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework.views import APIView


class BillingProfileViewSet(viewsets.ModelViewSet):
    serializer_class = BillingProfileSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def get_queryset(self):
        return BillingProfile.objects.filter(owner=self.request.user)

    def perform_create(self, serializer):
        try:
            if BillingProfile.objects.filter(owner=self.request.user).exists():
                raise ValidationError("A billing profile already exists for this user.")
            serializer.save(owner=self.request.user)
        except IntegrityError:
            raise ValidationError(
                "Failed to create billing profile due to data integrity error."
            )
        except Exception as e:
            raise ValidationError(f"Failed to create billing profile: {str(e)}")

    def update(self, request, *args, **kwargs):
        try:
            billing_profile = self.get_queryset().get()
            serializer = self.get_serializer(
                billing_profile, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except BillingProfile.DoesNotExist:
            return Response(
                {"error": "No billing profile found for this user."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BillingAddressViewSet(viewsets.ModelViewSet):
    serializer_class = BillingAddressSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        billing_profile = BillingProfile.objects.filter(owner=self.request.user).first()
        return (
            BillingAddress.objects.filter(billing_profile=billing_profile)
            if billing_profile
            else BillingAddress.objects.none()
        )

    def perform_create(self, serializer):
        try:
            billing_profile = BillingProfile.objects.get(owner=self.request.user)
            if BillingAddress.objects.filter(billing_profile=billing_profile).exists():
                raise ValidationError(
                    "A billing address already exists for this profile."
                )
            serializer.save(billing_profile=billing_profile)
        except BillingProfile.DoesNotExist:
            raise ValidationError("You must create a billing profile first.")
        except IntegrityError:
            raise ValidationError(
                "Failed to create billing address due to data integrity error."
            )


class TaxationViewSet(viewsets.ModelViewSet):
    serializer_class = TaxationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        billing_profile = BillingProfile.objects.filter(owner=self.request.user).first()
        return (
            Taxation.objects.filter(billing_profile=billing_profile)
            if billing_profile
            else Taxation.objects.none()
        )

    def perform_create(self, serializer):
        try:
            billing_profile = BillingProfile.objects.get(owner=self.request.user)
            if Taxation.objects.filter(billing_profile=billing_profile).exists():
                raise ValidationError(
                    "A taxation record already exists for this profile."
                )
            serializer.save(billing_profile=billing_profile)
        except BillingProfile.DoesNotExist:
            raise ValidationError("You must create a billing profile first.")
        except IntegrityError:
            raise ValidationError(
                "Failed to create taxation record due to data integrity error."
            )


class BillingSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        try:
            billing_profile = BillingProfile.objects.get(owner=request.user)
            billing_address = BillingAddress.objects.filter(
                billing_profile=billing_profile
            ).first()
            taxation = Taxation.objects.filter(billing_profile=billing_profile).first()

            billing_summary = {
                "billing_profile": BillingProfileSerializer(billing_profile).data,
                "billing_address": (
                    BillingAddressSerializer(billing_address).data
                    if billing_address
                    else None
                ),
                "taxation": TaxationSerializer(taxation).data if taxation else None,
            }
            return Response(billing_summary)

        except BillingProfile.DoesNotExist:
            return Response(
                {"error": "No billing profile found."}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
