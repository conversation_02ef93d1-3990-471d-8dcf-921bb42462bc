from apps.stay.models import StaffRole
from django.contrib.auth.password_validation import validate_password
from django.core.validators import validate_email
from rest_framework import serializers

from .models import User, UserProfile


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "name",
            "phone",
            "is_admin",
            "is_verified",
            "is_active",
        ]
        read_only_fields = ["id", "is_verified", "is_admin", "is_active"]

    def create(self, validated_data):
        # Convert email to lowercase
        validated_data["email"] = validated_data["email"].lower()

        # Use the custom create_user method from the UserManager
        user = User.objects.create_user(
            email=validated_data["email"],
            name=validated_data["name"],
            phone=validated_data.get("phone"),
        )
        return user

    def validate_phone(self, value):
        """
        Validates and cleans up the phone number by removing spaces.
        """
        return value.replace(" ", "")


class CustomUserDetailsSerializer(serializers.ModelSerializer):
    """
    Custom serializer for the User model that will be used by dj-rest-auth
    """

    class Meta:
        model = User
        fields = ("id", "email", "name", "phone", "is_verified")
        read_only_fields = ("email", "is_verified")


class VerifyEmailSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    verification_code = serializers.CharField(required=True, max_length=4)


class ResendVerificationSerializer(serializers.Serializer):
    """Serializer for resending email verification codes"""

    email = serializers.EmailField(
        required=True,
        validators=[validate_email],
        help_text="Email address to resend verification code to",
    )

    def validate_email(self, value):
        """Normalize email to lowercase"""
        return value.lower().strip()


class SetPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    password_confirm = serializers.CharField(write_only=True, required=True)

    def validate(self, attrs):
        if attrs["password"] != attrs["password_confirm"]:
            raise serializers.ValidationError(
                {"password_confirm": "Passwords do not match."}
            )
        return attrs


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)


class StaffPropertyPermissionSerializer(serializers.ModelSerializer):
    property_name = serializers.CharField(source="property.name")
    permissions = serializers.StringRelatedField(many=True)
    is_active = serializers.SerializerMethodField()
    is_onboarded = serializers.BooleanField(source="property.is_onboarded")

    class Meta:
        model = StaffRole
        fields = [
            "property_id",
            "property_name",
            "permissions",
            "is_active",
            "is_onboarded",
        ]

    def get_is_active(self, obj):
        return obj.property.is_active and obj.is_active


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    staff_properties = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = [
            "user",
            "image",
            "has_billing_profile",
            "is_customer",
            "staff_properties",
        ]
        read_only_fields = ["user", "has_billing_profile", "is_customer"]

    def validate_image(self, value):
        if value:
            # Check file size
            if value.size > 5 * 1024 * 1024:  # 5MB
                raise serializers.ValidationError("Image file size cannot exceed 5MB.")

            # Check file extension
            ext = value.name.split(".")[-1].lower()
            if ext not in UserProfile.ALLOWED_IMAGE_EXTENSIONS:
                raise serializers.ValidationError(
                    f"Only {', '.join(UserProfile.ALLOWED_IMAGE_EXTENSIONS)} files are allowed."
                )

        return value

    def get_staff_properties(self, obj):
        # Get staff roles
        staff_roles = obj.user.property_roles.filter(is_active=True)
        staff_properties = StaffPropertyPermissionSerializer(
            staff_roles, many=True
        ).data

        # Get owned properties
        owned_properties = obj.user.propertyownership_set.filter().select_related(
            "property"
        )

        # Add owned properties to the list
        for ownership in owned_properties:
            staff_properties.append(
                {
                    "property_id": ownership.property.id,
                    "property_name": ownership.property.name,
                    "permissions": ["owner"],
                    "is_active": ownership.property.is_active,
                    "is_onboarded": ownership.property.is_onboarded,
                }
            )

        return staff_properties


class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, validators=[validate_email])


class SetNewPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    verification_code = serializers.CharField(required=True, max_length=4)
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    password_confirm = serializers.CharField(write_only=True, required=True)

    def validate(self, attrs):
        if attrs["password"] != attrs["password_confirm"]:
            raise serializers.ValidationError(
                {"password_confirm": "Passwords do not match."}
            )
        return attrs


class ChangePasswordSerializer(serializers.Serializer):
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    logout_all_sessions = serializers.BooleanField(required=False, default=False)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError(
                {"new_password_confirm": "Passwords do not match."}
            )
        return attrs


class TeamUserSerializer(serializers.ModelSerializer):
    invite_id = serializers.UUIDField(required=True)
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )

    class Meta:
        model = User
        fields = [
            "id",
            "invite_id",
            "email",
            "name",
            "phone",
            "password",
            "is_verified",
            "is_active",
        ]
        read_only_fields = ["id", "is_verified", "is_active"]

    def validate_phone(self, value):
        if value:
            return value.replace(" ", "")
        return value

    def create(self, validated_data):
        validated_data.pop("invite_id")  # Remove invite_id before creating user
        password = validated_data.pop("password")

        # Create user without has_set_password
        user = User.objects.create_user(
            **validated_data, is_verified=True  # Team users are automatically verified
        )

        # Set password and has_set_password flag after creation
        user.set_password(password)
        user.has_set_password = True
        user.save()

        return user
