# Docker Hub Migration Guide

This document outlines the migration from GitHub Container Registry (GHCR) to Docker Hub for the Heibooky project.

## Overview

The Heibooky project has been migrated from GitHub Container Registry (`ghcr.io`) to Docker Hub (`docker.io`) as the primary container registry. This migration provides better compatibility, reliability, and easier access management.

## What Changed

### 1. Container Registry
- **Before**: `ghcr.io/heibooky/backtrack`
- **After**: `davysongs/heibooky`

### 2. Authentication Method
- **Before**: GitHub Token (`GITHUB_TOKEN`)
- **After**: Docker Hub credentials (`DOCKER_USERNAME` and `DOCKER_PASSWORD`)

### 3. Updated Files
- `docker-compose.prod.yml` - Updated image references
- `.github/workflows/django.yml` - Enhanced with retry logic and better error handling
- `scripts/deploy.sh` - Added Docker Hub authentication checks and retry mechanisms
- `CI_CD_SETUP_GUIDE.md` - Updated documentation and secrets checklist

## Required Actions

### 1. GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

```
Settings → Secrets and variables → Actions → Repository secrets
```

**New Required Secrets:**
- `DOCKER_USERNAME`: Your Docker Hub username
- `DOCKER_PASSWORD`: Your Docker Hub access token (recommended) or password

**Existing Secrets (unchanged):**
- `DOPPLER_TOKEN_DEV`
- `DOPPLER_TOKEN_STG` 
- `DOPPLER_TOKEN_PRD`
- `AWS_PRIVATE_KEY`
- `AWS_HOST_STG`
- `AWS_HOST_PRD`
- `AWS_USER`

### 2. Docker Hub Access Token Setup (Recommended)

1. Log in to [Docker Hub](https://hub.docker.com)
2. Go to **Account Settings** → **Security** → **Access Tokens**
3. Click **New Access Token**
4. Provide a description (e.g., "Heibooky CI/CD")
5. Select appropriate permissions:
   - **Read, Write, Delete** for full access
   - **Read, Write** for standard CI/CD operations
6. Copy the generated token
7. Use this token as the `DOCKER_PASSWORD` secret

### 3. Server-Side Updates

If you have existing deployments, update your production servers:

```bash
# SSH into your production server
ssh ubuntu@your-production-server

# Navigate to project directory
cd /opt/heibooky

# Pull the latest code (contains updated docker-compose.prod.yml)
git pull origin main

# Optional: Login to Docker Hub for private repositories
docker login docker.io

# Deploy with updated configuration
./scripts/deploy.sh production
```

## Migration Benefits

### 1. Improved Reliability
- Docker Hub has better uptime and global CDN
- Faster image pulls in most regions
- More robust authentication system

### 2. Enhanced Error Handling
- Added retry logic for image pulls (3 attempts with 10-second delays)
- Better error messages and logging
- Improved verification steps in CI/CD pipeline

### 3. Better Access Management
- Docker Hub access tokens provide granular permissions
- Easier to rotate credentials
- Better audit logging

## Backward Compatibility

### Breaking Changes
- **Image References**: All `ghcr.io/heibooky/backtrack` references changed to `davysongs/heibooky`
- **Authentication**: GitHub tokens no longer work for container registry access

### Non-Breaking Changes
- All existing environment variables and Doppler configurations remain unchanged
- Deployment scripts maintain the same interface and functionality
- Docker Compose commands and workflows remain identical

## Troubleshooting

### 1. Authentication Issues

**Problem**: `docker pull` fails with authentication errors

**Solution**:
```bash
# Check if Docker Hub credentials are configured
docker info | grep Username

# Login to Docker Hub
docker login docker.io
# Enter your Docker Hub username and password/token
```

### 2. Image Pull Failures

**Problem**: Images fail to pull during deployment

**Solutions**:
1. **Check image exists**: Verify the image exists at `davysongs/heibooky:latest`
2. **Network issues**: The deployment script now includes automatic retry logic
3. **Rate limiting**: Docker Hub has rate limits for anonymous pulls. Ensure authentication is configured.

### 3. CI/CD Pipeline Failures

**Problem**: GitHub Actions workflow fails during image push

**Solution**:
1. Verify `DOCKER_USERNAME` and `DOCKER_PASSWORD` secrets are correctly set
2. Check Docker Hub access token permissions
3. Ensure the Docker Hub repository `davysongs/heibooky` exists and is accessible

### 4. Legacy Image References

**Problem**: Old deployments still reference GHCR images

**Solution**:
```bash
# Update docker-compose.prod.yml manually if needed
sed -i 's|ghcr.io/heibooky/backtrack|davysongs/heibooky|g' docker-compose.prod.yml

# Force pull new images
docker-compose -f docker-compose.prod.yml pull --ignore-pull-failures
```

## Rollback Plan

If issues arise, you can temporarily rollback by:

1. **Reverting image references** in `docker-compose.prod.yml`:
   ```yaml
   image: ghcr.io/heibooky/backtrack:${IMAGE_TAG:-latest}
   ```

2. **Using GitHub Token** for authentication (if GHCR images are still available)

3. **Manual deployment** with specific image tags:
   ```bash
   IMAGE_TAG=specific-tag ./scripts/deploy.sh production
   ```

## Monitoring and Validation

### 1. Verify Migration Success

```bash
# Check running containers use new images
docker ps --format "table {{.Names}}\t{{.Image}}"

# Verify image sources
docker images | grep davysongs/heibooky
```

### 2. Monitor Deployment Logs

```bash
# Check deployment logs
tail -f /var/log/heibooky/deploy.log

# Monitor container health
docker-compose -f docker-compose.prod.yml ps
```

### 3. Application Health Checks

- Verify application is accessible at https://backend.heibooky.com
- Check monitoring dashboards for any anomalies
- Validate all services are running correctly

## Support

For issues related to this migration:

1. **Check logs**: Review deployment and application logs first
2. **Verify configuration**: Ensure all secrets and environment variables are correctly set
3. **Test locally**: Use `docker-compose.yml` for local testing
4. **Contact team**: Reach out to the development team with specific error messages

## Container Scaling Fix

### Issue Resolved
Fixed Docker Compose container scaling issue that prevented zero-downtime deployments:

**Problem**: Hardcoded container names in `docker-compose.prod.yml` prevented Docker Compose from creating multiple containers during rolling updates.

**Error**: `WARNING: The "web" service is using the custom container name "heibooky-web". Docker requires each container to have a unique name. Remove the custom name to scale the service`

**Solution**:
1. **Removed hardcoded container names** for scalable services (web, celery-worker, celery-beat)
2. **Kept container names** for singleton services (redis, nginx, certbot, grafana, prometheus)
3. **Simplified rolling update strategy** to use `--force-recreate` instead of complex scaling logic
4. **Updated health checks** to work with dynamic container names

### Files Modified
- `docker-compose.prod.yml`: Removed `container_name` for web, celery-worker, celery-beat
- `scripts/deploy.sh`: Simplified rolling update logic and fixed health checks

## Testing Instructions

### 1. Pre-deployment Validation
```bash
# Validate docker-compose configuration
docker-compose -f docker-compose.prod.yml config

# Check for syntax errors
docker-compose -f docker-compose.prod.yml config --quiet
```

### 2. Test Deployment
```bash
# Test deployment script (dry run)
./scripts/deploy.sh production --force

# Monitor deployment logs
tail -f /var/log/heibooky/deploy.log
```

### 3. Verify Container Health
```bash
# Check running containers (should have dynamic names)
docker-compose -f docker-compose.prod.yml ps

# Verify health status
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Test application endpoints
curl -f http://localhost/health/
curl -f https://backend.heibooky.com/health/
```

### 4. Test Rolling Updates
```bash
# Trigger a rolling update
IMAGE_TAG=latest ./scripts/deploy.sh production

# Verify zero-downtime (application should remain accessible)
while true; do curl -f http://localhost/health/ && echo " - OK" || echo " - FAIL"; sleep 2; done
```

## Next Steps

After successful migration:

1. **Monitor performance** for the first few deployments
2. **Update documentation** if any team-specific procedures change
3. **Consider cleanup** of old GHCR images (if applicable)
4. **Review access permissions** on Docker Hub repository
5. **Test rolling updates** in staging environment first
6. **Validate zero-downtime deployments** work as expected

---

**Migration completed on**: [Date]
**Migrated by**: [Team Member]
**Validated by**: [Team Member]
