from django.urls import path

from .views import (
    AlertWebhookView,
    HealthCheckView,
    LivenessCheckView,
    MetricsDebugView,
    MetricsView,
    ReadinessCheckView,
)

urlpatterns = [
    # Health checks
    path("health/", HealthCheckView.as_view(), name="health-check"),
    path("ready/", ReadinessCheckView.as_view(), name="readiness-check"),
    path("alive/", LivenessCheckView.as_view(), name="liveness-check"),
    # Metrics
    path("metrics/", MetricsView.as_view(), name="prometheus-metrics"),
    path("metrics/debug/", MetricsDebugView.as_view(), name="metrics-debug"),
    # Alerting
    path("webhook/", AlertWebhookView.as_view(), name="alert-webhook"),
]
