import logging
from datetime import timedelta

from apps.booking.models import Booking, Reservation
from celery import shared_task
from django.db import transaction
from django.utils import timezone
from services.notification.handlers import ReservationNotificationHandler

logger = logging.getLogger(__name__)


@shared_task(name="apps.booking.tasks.complete_past_bookings")
def complete_past_bookings():
    """
    Daily task to mark bookings as completed after checkout date.
    Only processes bookings with status 'new' or 'modified'.
    """
    logger.info("Starting complete_past_bookings task")

    try:
        eligible_statuses = [Booking.Status.NEW, Booking.Status.MODIFIED]
        current_date = timezone.now().date()

        # Find bookings that need to be marked as completed
        eligible_bookings = Booking.objects.filter(
            status__in=eligible_statuses, checkout_date__lt=current_date
        ).select_related("property", "customer")

        completed_count = 0
        for booking in eligible_bookings:
            try:
                with transaction.atomic():
                    booking.status = Booking.Status.COMPLETED
                    booking.save()

                    logger.info(
                        f"Marked booking {booking.id} as completed "
                        f"(Property: {booking.property.name}, "
                        f"Customer: {booking.customer.get_full_name()})"
                    )
                    completed_count += 1

            except Exception as e:
                logger.error(f"Error completing booking {booking.id}: {str(e)}")
                continue

        logger.info(
            f"Complete_past_bookings task finished. Completed {completed_count} bookings"
        )
        return {"status": "success", "completed_bookings": completed_count}

    except Exception as e:
        logger.error(f"Error in complete_past_bookings task: {str(e)}")
        raise


@shared_task(name="apps.booking.tasks.send_booking_reminders")
def send_booking_reminders():
    """
    Daily task to send booking reminders.
    - First reminder: 7 days before check-in
    - Second reminder: On the day of check-in
    """
    logger.info("Starting send_booking_reminders task")

    try:
        today = timezone.now().date()
        week_from_today = today + timedelta(days=7)

        # Find reservations for first reminder (7 days before)
        first_reminder_reservations = Reservation.objects.select_related(
            "booking__property", "booking__customer"
        ).filter(
            checkin_date__date=week_from_today,
            sent_first_reminder=False,
            booking__status__in=[Booking.Status.NEW, Booking.Status.MODIFIED],
        )

        # Find reservations for check-in day reminder
        checkin_day_reservations = Reservation.objects.select_related(
            "booking__property", "booking__customer"
        ).filter(
            checkin_date__date=today,
            sent_checkin_day_reminder=False,
            booking__status__in=[Booking.Status.NEW, Booking.Status.MODIFIED],
        )

        first_reminder_count = 0
        checkin_day_count = 0

        # Process 7-day reminders
        for reservation in first_reminder_reservations:
            try:
                with transaction.atomic():
                    booking = reservation.booking
                    handler = ReservationNotificationHandler(
                        booking.property, booking.customer
                    )
                    handler.send_reservation_reminder(
                        reservation, is_first_reminder=True
                    )
                    reservation.sent_first_reminder = True
                    reservation.save(update_fields=["sent_first_reminder"])
                    first_reminder_count += 1

                    logger.info(
                        f"Sent first reminder for reservation {reservation.id} "
                        f"(Property: {booking.property.name}, "
                        f"Guest: {booking.customer.get_full_name()})"
                    )

            except Exception as e:
                logger.error(
                    f"Error sending first reminder for reservation {reservation.id}: {str(e)}",
                    exc_info=True,
                )

        # Process check-in day reminders
        for reservation in checkin_day_reservations:
            try:
                with transaction.atomic():
                    booking = reservation.booking
                    handler = ReservationNotificationHandler(
                        booking.property, booking.customer
                    )
                    handler.send_reservation_reminder(
                        reservation, is_first_reminder=False
                    )
                    reservation.sent_checkin_day_reminder = True
                    reservation.save(update_fields=["sent_checkin_day_reminder"])
                    checkin_day_count += 1

                    logger.info(
                        f"Sent check-in day reminder for reservation {reservation.id} "
                        f"(Property: {booking.property.name}, "
                        f"Guest: {booking.customer.get_full_name()})"
                    )

            except Exception as e:
                logger.error(
                    f"Error sending check-in day reminder for reservation {reservation.id}: {str(e)}",
                    exc_info=True,
                )

        logger.info(
            f"Reminder task completed. Sent {first_reminder_count} first reminders "
            f"and {checkin_day_count} check-in day reminders"
        )

        return {
            "status": "success",
            "first_reminders_sent": first_reminder_count,
            "checkin_day_reminders_sent": checkin_day_count,
        }

    except Exception as e:
        error_msg = f"Fatal error in send_booking_reminders task: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise
