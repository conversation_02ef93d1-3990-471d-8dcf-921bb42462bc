import logging

from apps.stay.models import Property
from django.db import models
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class PropertyOnlineCheckIn(models.Model):
    """
    Model to store online check-in configuration for properties.
    This enables property owners to automatically submit guest data to authorities.
    """

    property = models.OneToOneField(
        Property,
        on_delete=models.CASCADE,
        related_name="online_checkin",
        verbose_name=_("Property"),
    )
    is_enabled = models.BooleanField(
        default=False, verbose_name=_("Online Check-in Enabled")
    )
    istat_enabled = models.BooleanField(
        default=False, verbose_name=_("ISTAT Reporting Enabled")
    )
    alloggati_enabled = models.BooleanField(
        default=False, verbose_name=_("Alloggati Web Reporting Enabled")
    )
    last_sync = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Last Synchronization")
    )
    next_sync = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Next Scheduled Synchronization")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Property Online Check-in")
        verbose_name_plural = _("Property Online Check-ins")
        ordering = ["-updated_at"]

    def __str__(self):
        return f"Online Check-in for {self.property.name}"

    def save(self, *args, **kwargs):
        """Override save to log configuration changes"""
        is_new = self.pk is None

        # Get original values if this is an update
        if not is_new:
            try:
                original = PropertyOnlineCheckIn.objects.get(pk=self.pk)
                # Log changes to enabled status
                if original.is_enabled != self.is_enabled:
                    logger.info(
                        f"Online check-in {'enabled' if self.is_enabled else 'disabled'} "
                        f"for property {self.property.id}"
                    )
                # Log changes to ISTAT status
                if original.istat_enabled != self.istat_enabled:
                    logger.info(
                        f"ISTAT reporting {'enabled' if self.istat_enabled else 'disabled'} "
                        f"for property {self.property.id}"
                    )
                # Log changes to Alloggati status
                if original.alloggati_enabled != self.alloggati_enabled:
                    logger.info(
                        f"Alloggati Web reporting {'enabled' if self.alloggati_enabled else 'disabled'} "
                        f"for property {self.property.id}"
                    )
            except PropertyOnlineCheckIn.DoesNotExist:
                pass

        # Call the original save method
        super().save(*args, **kwargs)

        # Log creation
        if is_new:
            logger.info(
                f"Online check-in configuration created for property {self.property.id}"
            )
