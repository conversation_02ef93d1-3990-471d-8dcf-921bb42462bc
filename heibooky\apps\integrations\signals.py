import logging

import stripe
from apps.integrations.models import Invoice, Payout, StripeCustomer
from django.conf import settings
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from services.email.email_service import EmailService
from services.notification import GeneralNotificationHandler

stripe.api_key = settings.STRIPE_SECRET_KEY

# Set up logging
logger = logging.getLogger(__name__)


@receiver(post_save, sender=StripeCustomer)
def update_user_profile_customer_status(sender, instance, created, **kwargs):
    """
    Update the user's profile is_customer field when a StripeCustomer is created
    """
    try:
        user = instance.user
        if hasattr(user, "profile"):
            user.profile.is_customer = True
            user.profile.save(update_fields=["is_customer"])
            logger.info(f"Updated is_customer to True for user {user.id}")
    except Exception as e:
        logger.error(f"Error updating user profile customer status: {str(e)}")


@receiver(pre_delete, sender=StripeCustomer)
def reset_user_profile_customer_status(sender, instance, **kwargs):
    """
    Reset the user's profile is_customer field when a StripeCustomer is deleted
    """
    try:
        user = instance.user
        if hasattr(user, "profile"):
            user.profile.is_customer = False
            user.profile.save(update_fields=["is_customer"])
            logger.info(f"Reset is_customer to False for user {user.id}")
    except Exception as e:
        logger.error(f"Error resetting user profile customer status: {str(e)}")


@receiver(pre_delete, sender=StripeCustomer)
def delete_stripe_account(sender, instance, **kwargs):
    """
    Deletes the Stripe account associated with the Customer model instance.
    """
    try:
        if instance.stripe_customer_id:
            stripe.Account.delete(instance.stripe_customer_id)
            logger.info(
                f"Stripe account {instance.stripe_customer_id} successfully deleted."
            )
    except stripe.error.StripeError as e:
        # Log Stripe API errors
        logger.error(
            f"Failed to delete Stripe account {instance.stripe_customer_id}: {e}"
        )


@receiver(post_save, sender=Payout)
def create_invoice_on_payout(sender, instance, created, **kwargs):
    """
    Creates invoice and sends notifications when a payout is successful.

    Args:
        sender: The model class (Payout)
        instance: The actual payout instance
        created: Boolean indicating if this is a new instance
        **kwargs: Additional keyword arguments
    """
    if instance.status != "successful":
        return

    try:
        if not hasattr(instance, "invoice"):
            # Create new invoice
            invoice = Invoice.objects.create(
                payout=instance, owner=instance.customer.user
            )

            # Generate PDF invoice
            from services.payment.invoice import generate_pdf_invoice

            if not generate_pdf_invoice(invoice):
                logger.error(f"Failed to generate PDF for invoice {invoice.id}")
                invoice.delete()
                return

            invoice.save()

            # Send email notification
            try:
                email_service = EmailService()
                email_service.send_payout_email(instance, invoice)
                logger.info(f"Payout email sent successfully for invoice {invoice.id}")
            except Exception as e:
                logger.error(f"Failed to send payout email: {str(e)}")

            # Send real-time notification
            try:
                user = instance.customer.user
                title = "Pagamento Effettuato"
                message = (
                    f"Il tuo pagamento di €{instance.amount:.2f} è stato elaborato con successo. "
                    f"Troverai la fattura nella sezione 'Documenti' del tuo profilo."
                )

                handler = GeneralNotificationHandler(
                    users=user, title=title, message=message
                )
                handler.send_notification()
                logger.info(f"Notifications sent successfully for payout {instance.id}")

            except Exception as e:
                logger.error(f"Failed to send notifications: {str(e)}")

            # Queue Digithera invoice generation
            try:
                from apps.integrations.tasks import generate_digithera_invoice_task

                generate_digithera_invoice_task.delay(invoice.id)
                logger.info(
                    f"Queued Digithera invoice generation for invoice {invoice.id}"
                )
            except Exception as e:
                logger.error(f"Error queueing Digithera invoice: {str(e)}")

    except Exception as e:
        logger.error(f"Unexpected error in create_invoice_on_payout: {str(e)}")
        raise
