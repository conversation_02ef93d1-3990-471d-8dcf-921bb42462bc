<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Language" content="it">
    <title>{{ translations.payout_notification }}</title>
    <style>
        /* General Reset */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f4f4;
            color: #113158;
        }
        table {
            border-spacing: 0;
            border-collapse: collapse;
            width: 100%;
        }
        td {
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(17, 49, 88, 0.1);
            overflow: hidden;
            font-size: 16px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: #ffffff;
            text-align: center;
            padding: 40px 0;
        }
        .logo-container img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }
        .content {
            padding: 40px 20px;
            text-align: center;
        }
        .payment-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .amount {
            font-size: 36px;
            font-weight: bold;
            color: #113158;
            margin: 20px 0;
        }
        .action-button {
            display: inline-block;
            background-color: #FCB51F;
            color: #113158;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            margin: 20px 0;
        }
        .footer {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            padding: 20px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <table role="presentation" align="center" class="email-container">
        <tr>
            <td class="header">
                <table align="center">
                    <tr>
                        <td class="logo-container">
                            <img src="{{ logo_url }}" alt="{{ site_name }}">
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td class="content">
                <h1>{{ translations.payout_initiated }}</h1>
                <div class="payment-info">
                    <p><strong>{{ translations.payout_reference }}:</strong> {{ payout.stripe_payment_intent_id }}</p>
                    <p><strong>{{ translations.bank_account }}:</strong> ****{{ payout.customer.stripe_customer_id|slice:"-4:" }}</p>
                    <p><strong>{{ translations.expected_arrival }}:</strong> {{ translations.within_days}}</p>
                </div>

                <p>{{ translations.payout_processing_message }}</p>
                <p>{{ translations.receipt_available }}</p>

                <a href="{{ dashboard_url }}" class="action-button">
                    {{ translations.view_receipts }}
                </a>
            </td>
        </tr>

        <tr>
            <td class="footer">
                <p>© {{ site_name }} {{ translations.all_rights_reserved }}</p>
                <p>{{ translations.questions_email_us }} <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </td>
        </tr>
    </table>
</body>
</html>
