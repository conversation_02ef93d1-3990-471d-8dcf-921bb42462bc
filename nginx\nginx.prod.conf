events {
    worker_connections 1024;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    client_max_body_size 100M;

    # General security settings
    server_tokens off;
    client_body_buffer_size 8k;
    client_header_buffer_size 1k;
    large_client_header_buffers 2 1k;

    # Block common attack patterns
    map $http_user_agent $bad_bot {
        default 0;
        ~*(curl|wget|python|nikto|sqlmap) 1;
        "" 1;
    }

    # Block non-allowed referers
    map $http_referer $bad_referer {
        default 0;
        ~*(ddos\.club|smallcarriage\.de) 1;
    }
    
    # Define allowed origins map for CORS
    map $http_origin $cors_allow_origin {
        default "";
        "https://partner.heibooky.com" $http_origin;
        "https://support.heibooky.com" $http_origin;
    }

    # Optimization settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # SSL settings
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_prefer_server_ciphers on;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;

    # Upstream configuration
    upstream web {
        server web:8000;
        keepalive 32;
    }

    # HTTP server (redirect to HTTPS and handle ACME challenges)
    server {
        listen 80;
        server_name backend.heibooky.com;

        # Block bad bots and referers
        if ($bad_bot) {
            return 444;
        }
        if ($bad_referer) {
            return 444;
        }

        # Block requests with invalid Host headers (allow localhost for health checks)
        if ($host !~* ^(backend\.heibooky\.com|localhost)$) {
            return 444;
        }

        # ACME challenge location for Let's Encrypt
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
            try_files $uri =404;
        }

        # Health check endpoints - bypass HTTPS redirect
        location ~* ^/(health|monitoring/(health|ready|alive))/?$ {
            proxy_pass http://web;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;  # Don't log health checks
        }

        # Redirect all other traffic to HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    # HTTPS server (only if certificates exist)
    server {
        listen 443 ssl;
        http2 on;
        server_name backend.heibooky.com;

        # Block bad bots and referers
        if ($bad_bot) {
            return 444;
        }
        if ($bad_referer) {
            return 444;
        }

        # Block requests with invalid Host headers (allow localhost for health checks)
        if ($host !~* ^(backend\.heibooky\.com|localhost)$) {
            return 444;
        }

        # SSL configuration
        ssl_certificate /etc/letsencrypt/live/backend.heibooky.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/backend.heibooky.com/privkey.pem;

        # Include additional SSL parameters if they exist
        include /etc/letsencrypt/options-ssl-nginx.conf;
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://js.stripe.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; img-src 'self' data: https: blob:; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; connect-src 'self' wss://backend.heibooky.com https://partner.heibooky.com wss://partner.heibooky.com https://support.heibooky.com wss://support.heibooky.com https://api.stripe.com wss: https:; frame-src 'self' https://js.stripe.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; upgrade-insecure-requests;";
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()";

        # Static files - Admin
        location /static/admin/ {
            alias /app/heibooky/staticfiles/admin/;
            expires 30d;
            add_header Cache-Control "public, no-transform";
            access_log off;
        }

        # Static files - General
        location /static/ {
            alias /app/heibooky/staticfiles/;
            expires 30d;
            add_header Cache-Control "public, no-transform";
            access_log off;

            # Ensure CSS files are served with correct MIME type
            location ~* \.css$ {
                add_header Content-Type text/css;
                expires 30d;
                add_header Cache-Control "public, no-transform";
            }
        }

        # Media files
        location /media/ {
            alias /app/heibooky/media/;
            expires 30d;
            add_header Cache-Control "public, no-transform";
            access_log off;
        }

        # Health check endpoints for HTTPS
        location ~* ^/(health|monitoring/(health|ready|alive))/?$ {
            proxy_pass http://web;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;  # Don't log health checks
        }

        # WebSocket configuration
        location /ws/ {
            proxy_pass http://web;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Origin $http_origin;

            # CORS headers for WebSockets
            add_header 'Access-Control-Allow-Origin' $cors_allow_origin always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;

            # WebSocket specific timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s; # Longer timeout for WebSocket connections
        }
        
        # Regular HTTP configuration
        location / {
            proxy_pass http://web;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
    }
}
