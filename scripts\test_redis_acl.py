#!/usr/bin/env python3
"""
Redis ACL Test Script
Tests Redis ACL configuration and user permissions
"""

import os
import sys

import redis


def test_redis_acl():
    """Test Redis ACL configuration"""
    redis_host = os.getenv("REDIS_HOST", "localhost")
    redis_port = int(os.getenv("REDIS_PORT", "6379"))
    redis_db = int(os.getenv("REDIS_DB", "0"))

    print(f"Testing Redis ACL configuration at {redis_host}:{redis_port}")

    try:
        # Create Redis connection
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5,
        )

        # Test basic connection
        print("Testing PING...")
        pong = r.ping()
        print(f"PING response: {pong}")

        # Test ACL commands
        print("Testing ACL LIST...")
        try:
            acl_list = r.execute_command("ACL", "LIST")
            print(f"ACL LIST response: {acl_list}")
        except Exception as e:
            print(f"ACL LIST failed: {e}")

        # Test ACL WHOAMI
        print("Testing ACL WHOAMI...")
        try:
            whoami = r.execute_command("ACL", "WHOAMI")
            print(f"Current user: {whoami}")
        except Exception as e:
            print(f"ACL WHOAMI failed: {e}")

        # Test basic operations with default user
        print("Testing SET/GET operations...")
        r.set("acl_test_key", "acl_test_value")
        value = r.get("acl_test_key")
        print(f"GET acl_test_key: {value}")

        # Test different Redis commands to verify permissions
        print("Testing various Redis commands...")

        # String operations
        r.set("test_string", "hello")
        assert r.get("test_string") == "hello"
        print("✅ String operations work")

        # List operations
        r.lpush("test_list", "item1", "item2")
        assert r.llen("test_list") == 2
        print("✅ List operations work")

        # Hash operations
        r.hset("test_hash", "field1", "value1")
        assert r.hget("test_hash", "field1") == "value1"
        print("✅ Hash operations work")

        # Set operations
        r.sadd("test_set", "member1", "member2")
        assert r.scard("test_set") == 2
        print("✅ Set operations work")

        # Clean up test keys
        print("Cleaning up test keys...")
        r.delete("acl_test_key", "test_string", "test_list", "test_hash", "test_set")

        print("✅ Redis ACL test successful!")
        return True

    except redis.ConnectionError as e:
        print(f"❌ Connection Error: {e}")
        return False
    except redis.TimeoutError as e:
        print(f"❌ Timeout Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False


if __name__ == "__main__":
    success = test_redis_acl()
    sys.exit(0 if success else 1)
