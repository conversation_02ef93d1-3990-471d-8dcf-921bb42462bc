<!DOCTYPE html>
<html lang="it">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Language" content="it">
        <title>{% block title %}{% endblock %}</title>
        {% block style %}
        <style>
            body {
                margin: 0;
                padding: 0;
                font-family: 'Helvetica Neue', Arial, sans-serif;
                background-color: #f4f4f4;
                color: #113158;
            }

            table {
                border-spacing: 0;
                border-collapse: collapse;
                width: 100%;
            }

            td {
                padding: 0;
            }

            .email-container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                border-radius: 16px;
                box-shadow: 0 4px 24px rgba(17, 49, 88, 0.1);
                overflow: hidden;
                font-size: 16px;
                line-height: 1.6;
            }

            .header {
                background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
                color: #ffffff;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .brand-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-end;
                gap: 12px;
                width: 100%;
            }

            .brand-info {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                text-align: right;
            }

            .logo-container {
                width: 48px;
                height: 48px;
                flex-shrink: 0;
            }

            .logo-container img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
                border: 2px solid #FCB51F;
            }

            .brand-name {
                font-size: 20px;
                font-weight: bold;
                color: #ffffff;
                margin: 0;
                letter-spacing: 0.5px;
            }

            .content {
                padding: 40px 20px;
            }

            .title-text {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 16px;
                color: #113158;
            }

            .instructions {
                color: #454545;
                margin: 20px 0;
            }

            .footer {
                background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
                color: rgba(255, 255, 255, 0.9);
                text-align: center;
                padding: 20px;
                font-size: 13px;
                line-height: 1.6;
            }

            .footer a {
                color: #FCB51F;
                text-decoration: none;
                transition: color 0.2s ease;
            }

            .footer a:hover {
                color: #ffc23d;
                text-decoration: underline;
            }

            .divider {
                height: 1px;
                background-color: rgba(252, 189, 76, 0.3);
                margin: 20px 0;
            }

            @media only screen and (max-width: 600px) {
                .header {
                    flex-direction: column;
                    text-align: center;
                }

                .brand-container {
                    flex-direction: column;
                    gap: 8px;
                }

                .brand-name {
                    font-size: 18px;
                }

                .logo-container {
                    width: 40px;
                    height: 40px;
                }

                .title-text {
                    font-size: 20px;
                }
            }
        </style>
        {% endblock %}
    </head>
    <body>
        <table role="presentation" align="center" class="email-container">
            <tr>
                <td class="header">
                    <div class="brand-container">
                        <div class="logo-container">
                            <img src="{{ logo_url }}" alt="{{ site_name }}">
                        </div>
                    </div>
                    <div class="brand-info">
                        <span class="brand-name">Heibooky</span>
                    </div>
                </td>
            </tr>
            {% block content %}{% endblock %}
            <tr>
                <td class="footer">
                    <p>© {{ site_name }} {{ translations.all_rights_reserved }}</p>
                    <div class="divider"></div>
                    <p>{{ translations.questions_email_us }} <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </td>
            </tr>
        </table>
    </body>
</html>
