#!/bin/bash
set -e

# SSL Certificate Renewal Script for Heibooky
# This script renews Let's Encrypt SSL certificates and reloads nginx if needed

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/var/log/heibooky/ssl-renewal.log"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Change to project directory
cd "$PROJECT_DIR"

log "=== SSL Certificate Renewal Started ==="

# Check if certbot container exists
if ! docker-compose -f docker-compose.prod.yml config --services | grep -q certbot; then
    error "Certbot service not found in docker-compose.prod.yml"
    exit 1
fi

# Check if nginx is running
if ! docker-compose -f docker-compose.prod.yml ps nginx | grep -q "Up"; then
    error "Nginx container is not running"
    exit 1
fi

# Attempt to renew certificates
log "Attempting to renew SSL certificates..."
if docker-compose -f docker-compose.prod.yml run --rm certbot renew --quiet --no-self-upgrade; then
    log "Certificate renewal check completed successfully"

    # Check if nginx configuration is valid
    if docker-compose -f docker-compose.prod.yml exec -T nginx nginx -t > /dev/null 2>&1; then
        # Reload nginx to pick up any renewed certificates
        log "Reloading nginx configuration..."
        docker-compose -f docker-compose.prod.yml exec -T nginx nginx -s reload
        log "Nginx reloaded successfully"    else
        error "Nginx configuration test failed, not reloading"
        exit 1
    fi
else
    error "Certificate renewal failed"
    exit 1
fi

log "=== SSL Certificate Renewal Completed ==="