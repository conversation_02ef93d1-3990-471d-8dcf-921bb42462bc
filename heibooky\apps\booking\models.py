import uuid

from django.core.exceptions import ValidationError
from django.db import models
from django.utils.timezone import now
from django.utils.translation import gettext_lazy as _


class Booking(models.Model):
    """Model representing booking details fetched from the Su API."""

    class Status(models.TextChoices):
        NEW = "new", _("New")
        MODIFIED = "modified", _("Modified")
        REQUEST = "request", _("Requested")
        CANCELLED = "cancelled", _("Cancelled")
        COMPLETED = "completed", _("Completed")

    class ChannelChoices(models.IntegerChoices):
        BOOKING_ENGINE = 0, _("Booking Engine")
        STAAH = 1, _("STAAH")
        STAAHMAX = 2, _("STAAH Max")
        EXPEDIA = 9, _("Expedia")
        BOOKIT = 13, _("Bookit")
        BOOKING = 19, _("Booking.com")
        BOOKEASY = 38, _("Bookeasy")
        NEEDITNOW = 39, _("Needitnow")
        NOT1NIGHT = 51, _("Not1night")
        PANPACIFIC = 55, _("Panpacific")
        MITCHELLCORP = 57, _("Mitchellcorp")
        TRAVELOCITYXML = 59, _("Travelocity XML")
        HOSTELSCLUB = 61, _("Hostelsclub")
        LIDO = 64, _("Lido")
        HOTWIREXML = 65, _("Hotwire XML")
        BEDBANK = 66, _("Bedbank")
        RECONLINE = 69, _("Reconline")
        SUNHOTELS = 71, _("Sunhotels")
        HOSTELWORLD = 76, _("Hostelworld")
        JETSTAR = 79, _("Jetstar")
        HOTELBEDSNEW = 82, _("Hotelbeds New")
        JACTRAVEL = 85, _("Jactravel")
        HRS = 87, _("HRS")
        TRAVELREPUBLIC = 90, _("Travel Republic")
        HOTELZON = 96, _("Hotelzon")
        TRAVELGURUXML = 97, _("Travelguru XML")
        WEBROOMS = 103, _("Webrooms")
        GTATRAVEL = 104, _("GTA Travel")
        GOIBIBO = 105, _("Goibibo")
        AOT = 106, _("AOT")
        TIKET = 108, _("Tiket.com")
        HOTELSCOMBINED = 109, _("Hotels Combined")
        PITCHUP = 110, _("Pitchup")
        WEBHOTELIER = 116, _("Webhotelier")
        KLIKHOTEL = 119, _("Klikhotel")
        TABLETHOTELS = 122, _("Tablet Hotels")
        PRESTIGIA = 127, _("Prestigia")
        HOTUSA = 130, _("Hotusa")
        HOTELNETWORK = 133, _("Hotel Network")
        VIAXML = 140, _("Via XML")
        HOTERIP = 148, _("Hoterip")
        CTRIP = 150, _("Ctrip")
        PEGIPEGI = 152, _("Pegipegi")
        FASTBOOKING = 153, _("Fastbooking")
        TOMAS = 160, _("Tomas")
        MRANDMRSSMITH = 162, _("Mr and Mrs Smith")
        METGLOBAL = 165, _("Metglobal")
        TRAVELOKA = 170, _("Traveloka")
        RAKUTEN = 176, _("Rakuten")
        RESERVHOTEL = 177, _("Reservhotel")
        FLIGHTCENTREWHOLESALE = 179, _("Flight Centre Wholesale")
        DESPEGAR = 181, _("Despegar")
        DORMS = 183, _("Dorms")
        BOOKINGDIRECT = 185, _("Booking Direct")
        OSTROVOK = 186, _("Ostrovok")
        AGODA = 189, _("Agoda")
        GRABROOMS = 203, _("Grabrooms")
        TRAVELANIUM = 205, _("Travelanium")
        TOURPLAN = 206, _("Tourplan")
        BOOKINGEYE = 208, _("Bookingeye")
        GETAROOM = 213, _("Getaroom")
        EASEMYTRIP = 217, _("Easemytrip")
        SAWADEEXML = 222, _("Sawadee XML")
        TRAVELCLICK = 223, _("Travelclick")
        HOTELBONANZA = 224, _("Hotel Bonanza")
        ICHRONOZ = 225, _("iChronoz")
        BOOKNPAYXML = 226, _("Booknpay XML")
        SIMPLEBOOKING = 231, _("Simplebooking")
        DOTW = 232, _("DOTW")
        THEBUKING = 230, _("Thebuking")
        ODIGEO = 236, _("Odigeo")
        SILVERDOOR = 239, _("Silverdoor Apartments")
        IESCAPE = 242, _("iEscape")
        AIRBNB = 244, _("Airbnb Content")
        TRAVELSTAYNETWORKXML = 245, _("Travelstay Network XML")
        SPEEDYBOOKER = 250, _("Speedybooker")
        EZTRAVEL = 251, _("EZ Travel")
        VRBO = 253, _("VRBO")
        TBO = 255, _("TBO")
        ONEFINERATE = 266, _("OneFinerate")
        MGHOLIDAYSNEW = 267, _("MG Holidays New")
        SAFFRONSTAYS = 270, _("Saffron Stays")
        REZLIVE = 274, _("RezLive")
        METACHANNEL = 275, _("Metachannel")
        TRAVCOXML = 280, _("Travco XML")
        SIMPLOTEL = 281, _("Simplotel")
        IRCTC = 282, _("IRCTC")
        HAPPYEASYGO = 284, _("HappyEasyGo")
        SITU = 288, _("Situ")
        APARTMENTSONLINE = 290, _("Apartments Online")
        HYPERGUEST = 291, _("Hyperguest")
        TRIPJACK = 292, _("Tripjack")
        BAKUUN = 293, _("Bakuun")
        PELICAN = 294, _("Pelican")
        OMNIHOTELIER = 295, _("Omnihotelier")
        TRIPFACTORY = 296, _("Tripfactory")
        HOBSE = 297, _("Hobse")
        NEORCHA = 299, _("Neorcha")
        FINDBULOUS = 300, _("Findbulous")
        CAMPINGVISION = 301, _("Campingvision")
        BELHOTEL = 302, _("Belhotel")
        HOTTAU = 303, _("Hottau")
        HOO = 304, _("Hoo")
        HALALBOOKING = 305, _("Halalbooking")
        JUMPON = 306, _("Jumpon.online")
        OFFPEAKLUXURY = 310, _("OffPeakLuxury")
        TRAVEPIC = 312, _("Travepic")
        KLIKNBOOK = 313, _("Kliknbook")
        ROBINHOOD = 315, _("Robinhood")
        TXGB = 317, _("TXGB")
        DEDGE = 318, _("Dedge")
        ALTOVITA = 319, _("Altovita")
        DIDATRAVEL = 320, _("Didatravel")
        FPM = 321, _("5pm")
        BREVISTAY = 325, _("Brevistay")
        AIRASIA = 328, _("Airasia")
        BAG2BAG = 329, _("Bag2bag")
        AUBERGENIE = 330, _("Aubergenie")
        VILLAFINDER = 336, _("Villafinder")
        CTRIPNEW = 339, _("Ctrip New")
        ROIBOS = 346, _("Roibos")
        TRUSTEDSTAYS = 347, _("Trustedstays")
        BUSYROOMS = 350, _("Busyrooms")
        CLEARTRIP = 351, _("Cleartrip")
        TRAVELPLUS = 352, _("Travelplus")
        KLOOK = 353, _("Klook")
        THEPERCENTAGE = 354, _("Thepercentage")
        HOTELSINONE = 355, _("Hotelsinone")
        FERATEL = 356, _("Feratel")
        SEERA = 358, _("Seera")
        BOOKMYBOOKING = 361, _("Bookmybooking")
        SPABREAKS = 362, _("Spabreaks")
        CONVERGENT = 364, _("Convergent")
        HOTELINX = 369, _("Hotelinx")
        NUITEE = 370, _("Nuitee")
        OPENGDS = 371, _("Opengds")
        CODEGEN = 373, _("Codegen")
        MOTEELZ = 374, _("Moteelz")
        WOWCHER = 377, _("Wowcher")
        ELONG = 378, _("Elong")
        DARENT = 381, _("Darent")
        ILLUSIONSONLINE = 382, _("Illusionsonline")
        MAX = 5000, _("Max Channel")
        TRIPADVISOR = 5001, _("Tripadvisor")
        GOOGLE = 5002, _("Google Hotel Ads")
        UMI = 5003, _("UMI Digital")
        MYBOOKINGSITE = 5004, _("Mybookingsite.io")
        INSTANT = 5005, _("Instant")
        TRIVAGO = 5006, _("Trivago")

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="bookings"
    )
    customer = models.ForeignKey(
        "Customer", on_delete=models.CASCADE, related_name="bookings"
    )
    reservation_data = models.OneToOneField(
        "Reservation", on_delete=models.CASCADE, related_name="booking"
    )
    is_manual = models.BooleanField(
        default=False, help_text="True if the booking was created manually."
    )
    channel_code = models.IntegerField(
        choices=ChannelChoices.choices,
        help_text="Channel code of the booking.",
        blank=True,
        null=True,
    )
    booking_date = models.DateTimeField(
        auto_now_add=True, help_text="Date and time when the booking was made."
    )
    checkin_date = models.DateField(help_text="Check-in date for the booking.")
    checkout_date = models.DateField(help_text="Check-out date for the booking.")
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.NEW,
        help_text="Current status of the booking.",
    )
    payment_processed = models.BooleanField(
        default=False, help_text="Whether the owner payout has been processed"
    )
    payment_processing_attempts = models.IntegerField(
        default=0, help_text="Number of payment processing attempts"
    )
    last_payment_attempt = models.DateTimeField(null=True, blank=True)

    def clean(self):
        """Check if booking dates fall within any blocked period or overlap with existing bookings."""
        # A manual booking cannot be created on days with an existing block
        # But it can start from the end date of a booking block and end on the start date of a booking block
        blocked_periods = BookingBlock.objects.filter(
            property=self.property,
            start_date__lt=self.checkout_date,  # Block starts before booking ends
            end_date__gt=self.checkin_date,  # Block ends after booking starts
        )

        # Exclude blocks that end exactly on the booking's start date or start exactly on the booking's end date
        blocked_periods = blocked_periods.exclude(
            end_date=self.checkin_date  # Block ends on booking's start date (allowed)
        ).exclude(
            start_date=self.checkout_date  # Block starts on booking's end date (allowed)
        )

        if blocked_periods.exists() and self.is_manual:
            block = blocked_periods.first()
            raise ValidationError(
                f"Booking dates overlap with a blocked period "
                f"(from {block.start_date} to {block.end_date}). "
                f"A manual booking cannot overlap with existing blocks."
            )

        # A manual booking cannot be created on days with an existing booking
        # But it can start from the end date of an existing booking and end on the start date of an existing booking
        if self.is_manual and self.pk is None:  # Only check for new manual bookings
            overlapping_bookings = Booking.objects.filter(
                property=self.property,
                checkin_date__lt=self.checkout_date,  # Existing booking starts before new booking ends
                checkout_date__gt=self.checkin_date,  # Existing booking ends after new booking starts
            )

            # Exclude bookings that end exactly on the new booking's start date or start exactly on the new booking's end date
            overlapping_bookings = overlapping_bookings.exclude(
                checkout_date=self.checkin_date  # Existing booking ends on new booking's start date (allowed)
            ).exclude(
                checkin_date=self.checkout_date  # Existing booking starts on new booking's end date (allowed)
            )

            if overlapping_bookings.exists():
                booking = overlapping_bookings.first()
                raise ValidationError(
                    f"Booking dates overlap with an existing booking "
                    f"(from {booking.checkin_date} to {booking.checkout_date}). "
                    f"A manual booking cannot overlap with existing bookings."
                )

    def __str__(self):
        return f"{self.id} - {self.status} - {self.property.name}"

    class Meta:
        ordering = ["-booking_date"]


class Reservation(models.Model):
    """Model for individual reservations containing room details and pricing."""

    id = models.CharField(
        max_length=100, primary_key=True, help_text="Reservation ID from Su API."
    )
    guest_name = models.CharField(
        max_length=100, blank=True, help_text="Full name of the guest."
    )
    booked_at = models.DateTimeField(
        null=True, blank=True, help_text="Date when the reservation was booked."
    )
    modified_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last modification timestamp for the reservation.",
    )
    checkin_date = models.DateTimeField(
        null=True, blank=True, help_text="Expected arrival date"
    )
    checkout_date = models.DateTimeField(
        null=True, blank=True, help_text="Expected time of departure"
    )
    gross_price = models.DecimalField(
        null=True,
        blank=True,
        max_digits=10,
        decimal_places=2,
        help_text="Gross price of the reservation.",
    )
    total_price = models.DecimalField(
        max_digits=10,
        blank=True,
        null=True,
        decimal_places=2,
        help_text="Total price of the reservation after commission.",
    )
    total_tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Total tax for the reservation.",
    )
    deposit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Deposit amount.",
    )
    cancellation_fee = models.DecimalField(
        null=True,
        blank=True,
        max_digits=10,
        decimal_places=2,
        help_text="Cancellation fee amount.",
    )
    processed_at = models.DateTimeField(
        null=True, blank=True, help_text="Timestamp when the booking was processed."
    )
    reservation_notif_id = models.CharField(
        null=True, blank=True, max_length=100, help_text="Reservation notification ID."
    )
    extra_fees = models.JSONField(default=dict, help_text="Extra fees in JSON format.")
    taxes = models.JSONField(default=dict, help_text="Taxes in JSON format.")
    payment_due = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00, help_text="Total payment due."
    )
    payment_type = models.CharField(max_length=25, default="Hotel Collect")
    commission_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Commission amount on bookings.",
    )
    number_of_guests = models.IntegerField(default=1)
    number_of_adults = models.IntegerField(default=1)
    number_of_infants = models.IntegerField(default=0)
    number_of_children = models.IntegerField(default=0)
    remarks = models.TextField(default=None, null=True, blank=True)
    addons = models.JSONField(
        default=dict, null=True, blank=True, help_text="Addons in JSON format."
    )
    # Reminder tracking fields
    sent_first_reminder = models.BooleanField(
        default=False, help_text="Whether the 7-day reminder has been sent."
    )
    sent_checkin_day_reminder = models.BooleanField(
        default=False, help_text="Whether the check-in day reminder has been sent."
    )

    def __str__(self):
        return f"Reservation for {self.guest_name} - {self.id}"

    class Meta:
        ordering = ["-modified_at" if "-modified_at" else "-booked_at"]


class Customer(models.Model):
    """Model representing customer details for a reservation."""

    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    email = models.EmailField()
    telephone = models.CharField(max_length=20)
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=50, blank=True, null=True)
    state = models.CharField(max_length=50, blank=True, null=True)
    country = models.CharField(max_length=50, default="Italia", blank=True, null=True)
    zip_code = models.CharField(max_length=10, blank=True, null=True)

    def get_full_name(self):
        """Return the customer's full name."""
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        return f"{self.first_name} {self.last_name}"


class BookingBlock(models.Model):
    """Model to block bookings within a specific time period."""

    id = models.BigAutoField(
        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
    )
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="booking_blocks"
    )
    start_date = models.DateField(help_text="Start date of the block period.")
    end_date = models.DateField(help_text="End date of the block period.")
    reason = models.CharField(
        max_length=255, blank=True, help_text="Reason for blocking the booking period."
    )
    is_active = models.BooleanField(
        default=True, help_text="Whether the block is active."
    )

    @classmethod
    def get_overlapping_block(cls, property, start_date, end_date, exclude_id=None):
        """Check for any overlapping blocks."""
        query = cls.objects.filter(
            property=property, start_date__lte=end_date, end_date__gte=start_date
        )
        if exclude_id:
            query = query.exclude(id=exclude_id)
        return query.first()

    def clean(self):
        """Ensure end_date is after start_date and no overlapping blocks exist."""
        if self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date.")
        if self.start_date < now().date():
            raise ValidationError(
                {"start_date": "Start date must be today or in the future."}
            )

        # Check for overlapping blocks
        overlapping_block = self.__class__.get_overlapping_block(
            self.property, self.start_date, self.end_date, self.id
        )
        if overlapping_block:
            raise ValidationError(
                f"A blocking period already exists for these dates "
                f"(from {overlapping_block.start_date} to {overlapping_block.end_date})"
            )

        # Check for overlapping bookings
        # A booking block can start on the end date of a booking and end on the start date of a booking
        overlapping_bookings = Booking.objects.filter(
            property=self.property,
            checkin_date__lt=self.end_date,  # Booking starts before block ends
            checkout_date__gt=self.start_date,  # Booking ends after block starts
        )

        # Exclude bookings that start exactly on the block's end date or end exactly on the block's start date
        overlapping_bookings = overlapping_bookings.exclude(
            checkin_date=self.end_date  # Booking starts on block's end date (allowed)
        ).exclude(
            checkout_date=self.start_date  # Booking ends on block's start date (allowed)
        )

        if overlapping_bookings.exists():
            booking = overlapping_bookings.first()
            raise ValidationError(
                f"A booking already exists for these dates "
                f"(from {booking.checkin_date} to {booking.checkout_date}). "
                f"A booking block cannot overlap with existing bookings."
            )

    def __str__(self):
        return f"Block: {self.property.name} from {self.start_date} to {self.end_date}"
