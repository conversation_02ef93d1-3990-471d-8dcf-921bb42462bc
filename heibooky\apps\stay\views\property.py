import logging
import uuid

from apps.stay.models import *
from apps.stay.serializers import *
from apps.stay.utils import check_status, validate_ownership
from apps.users.permissions import (
    GuestArrivalInfoPermission,
    PropertyConfigPermission,
    PropertyMetadataPermission,
    RoomConfigPermission,
)
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views import View
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from services.email import PropertyEmailService
from services.storage.utils import CDNUtils

logger = logging.getLogger(__name__)


# Property Views
class PropertyViewSet(viewsets.ModelViewSet):
    """
    A viewset for managing property CRUD operations.
    Includes handling for property deactivation and deletion requests.
    """

    serializer_class = PropertySerializer
    permission_classes = [IsAuthenticated, PropertyConfigPermission]
    email_service = PropertyEmailService()

    def get_permissions(self):
        """
        Override to ensure proper permission handling for different actions
        """
        if self.action == "create":
            return [IsAuthenticated()]
        return super().get_permissions()

    def get_queryset(self):
        """
        Override to return only properties owned by the authenticated user.
        """
        try:
            return Property.objects.filter(staffs=self.request.user)
        except Exception as e:
            raise ValidationError({"error": f"Error retrieving properties: {str(e)}"})

    def create(self, request):
        """
        Create a new property and set up related objects.
        """
        try:
            location_data = request.data.get("location")
            relation_type = request.data.get("relation_type")

            # Check for existing property with same location
            if Property.objects.filter(location=location_data).exists():
                existing_property = Property.objects.get(location=location_data)
                return Response(
                    {
                        "error": "A property with the same data already exists.",
                        "existing_property_id": str(existing_property.id),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate data with serializer
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"validation_errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Save the property
            property_instance = serializer.save()

            # Add the user to the property's staff
            property_instance.staffs.add(request.user)  # Create the ownership relation
            PropertyOwnership.objects.create(
                user=request.user,
                property=property_instance,
                relation_type=relation_type,
            )  # Create or get the Property metadata (use get_or_create to avoid duplicate key error)
            PropertyMetadata.objects.get_or_create(property=property_instance)

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            return Response(
                {"validation_error": str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error creating property: {str(e)}", exc_info=True)
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        """
        Override the update method to handle both PUT and PATCH (partial updates).
        """
        try:
            partial = kwargs.pop("partial", False)
            instance = self.get_object()

            if not instance:
                return Response(
                    {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
                )

            serializer = self.get_serializer(
                instance, data=request.data, partial=partial
            )
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            return Response(serializer.data)

        except PermissionDenied:
            return Response(
                {"error": "You don't have permission to update this property"},
                status=status.HTTP_403_FORBIDDEN,
            )
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": f"Error updating property: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def perform_update(self, serializer):
        """
        Handle property updates, including the relationship between the user and the property.
        """
        try:
            relation_type = self.request.data.get("relation_type")
            property_instance = self.get_object()
            if relation_type and not validate_ownership(
                self.request.user, property_instance.id
            ):
                raise PermissionDenied(
                    "Only property owners can update property relation type"
                )
            elif relation_type:
                ownership_serializer = PropertyOwnership.objects.get(
                    property=property_instance, user=self.request.user
                )
                ownership_serializer.relation_type = relation_type
                ownership_serializer.save(update_fields=["relation_type"])

            serializer.save()

        except ValidationError as e:
            raise ValidationError({f"Invalid data: {str(e)}"})
        except Exception as e:
            raise ValidationError({f"Error updating property: {str(e)}"})

    def _generate_admin_url(self, property_id: int) -> str:
        """Generate the admin URL for property deletion"""
        return f"{settings.ADMIN_URL}/stay/property/{property_id}/delete/"

    def _send_deletion_request(self, user, property_instance) -> None:
        """Send the deletion request email using PropertyEmailService"""
        try:
            admin_url = self._generate_admin_url(property_instance.id)
            self.email_service.send_deletion_request(
                user=user, property_obj=property_instance, admin_url=admin_url
            )
            logger.info(
                f"Deletion request email sent for property {property_instance.id} "
                f"by user {user.id}"
            )
        except Exception as e:
            logger.error(
                f"Failed to send deletion request email for property "
                f"{property_instance.id}: {str(e)}",
                exc_info=True,
            )
            raise ValidationError("Failed to send deletion request email")

    def destroy(self, request, *args, **kwargs):
        """
        Override destroy method to deactivate property instead of deleting it
        and send deletion request email to admin. Only property owners can delete properties.
        """
        try:
            property_instance = self.get_object()

            # Check if user has valid ownership
            if not validate_ownership(request.user, property_instance.id):
                raise PermissionDenied(
                    "Only property owners can request property deletion"
                )

            # Send deletion request email
            self._send_deletion_request(request.user, property_instance)

            return Response(
                {
                    "message": "Property deactivated and deletion request sent to admin",
                    "property_id": property_instance.id,
                },
                status=status.HTTP_200_OK,
            )

        except PermissionDenied as e:
            logger.warning(
                f"Permission denied for user {request.user.id} "
                f"attempting to delete property {kwargs.get('pk')}: {str(e)}"
            )
            return Response({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)

        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        except NotFound:
            return Response(
                {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
            )

        except Exception as e:
            logger.error(
                f"Unexpected error during property deletion request: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": "Error processing deletion request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PropertyMetadataViewSet(viewsets.ModelViewSet):
    """
    A viewset for managing property metadata with enhanced error handling.
    """

    queryset = PropertyMetadata.objects.all()
    serializer_class = PropertyMetadataSerializer
    permission_classes = [IsAuthenticated, PropertyMetadataPermission]

    def get_permissions(self):
        """Check permissions and capture any permission errors"""
        permissions = super().get_permissions()
        for permission in permissions:
            if not permission.has_permission(self.request, self):
                if hasattr(self.request, "permission_error"):
                    raise PermissionDenied(self.request.permission_error)
        return permissions

    def list(self, request, *args, **kwargs):
        """
        Retrieve property metadata. Filter by property ID if provided,
        else return metadata for all properties owned by the user.
        """
        property_id = request.query_params.get("property")

        if property_id:
            try:
                uuid.UUID(property_id)  # Validate UUID format
            except ValueError:
                return Response(
                    {"error": "Invalid property ID format. Ensure it is a valid UUID."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user_properties = Property.objects.filter(staffs=request.user)
            if not user_properties.filter(id=property_id).exists():
                raise NotFound("You do not own this property or it does not exist.")

            property_instance = get_object_or_404(Property, id=property_id)
            meta_data = PropertyMetadata.objects.filter(property=property_instance)
            if not meta_data.exists():
                return Response(
                    {"error": "Metadata not found for the specified property."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = self.get_serializer(meta_data, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        # Default behavior: return metadata for all properties owned by the user
        user_properties = Property.objects.filter(staffs=request.user)
        queryset = PropertyMetadata.objects.filter(property__in=user_properties)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def perform_update(self, serializer):
        """
        Ensure the user can only update metadata for properties they own.
        """
        property_instance = self.get_object().property
        if not property_instance.staffs.filter(id=self.request.user.id).exists():
            raise PermissionDenied(
                "You are not authorized to update metadata for this property."
            )
        serializer.save()

    @action(
        detail=True,
        methods=["patch"],
        url_path="property-info",
        url_name="property_info",
    )
    def update_property_info(self, request, pk=None):
        """
        Update the 'Property Information' section.
        """
        metadata_instance = self.get_object()
        data = request.data
        allowed_fields = [
            "regional_id_code",
            "check_in_time",
            "check_out_time",
            "close_out_days",
            "close_out_time",
        ]
        serializer = self.get_serializer(
            metadata_instance,
            data={key: data[key] for key in data if key in allowed_fields},
            partial=True,
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(
        detail=True,
        methods=["patch"],
        url_path="cancelation-policy",
        url_name="cancelation_policy",
    )
    def update_cancelation_policy(self, request, pk=None):
        """
        Update the cancellation policy for a property's metadata.
        """
        metadata_instance = self.get_object()
        cancelation_policy_type = request.data.get("cancelation_policy_type")

        if not cancelation_policy_type:
            return Response(
                {"error": "Cancellation policy type is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if cancelation_policy_type not in dict(
            PropertyMetadata.CANCELATION_POLICY_CHOICES
        ):
            return Response(
                {"error": "Invalid cancellation policy type."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        metadata_instance.cancelation_policy_type = cancelation_policy_type
        metadata_instance.save()

        try:
            self.email_service.send_cancelation_policy_request(
                user=request.user,
                property_obj=metadata_instance.property,
                cancelation_policy_type=metadata_instance.get_cancelation_policy_type_display(),
            )
        except Exception as e:
            logger.error(
                "Failed to send cancellation policy update email",
                exc_info=True,
                extra={"property_id": metadata_instance.property.id, "error": str(e)},
            )

        return Response(
            {"detail": "Cancellation policy updated successfully."},
            status=status.HTTP_200_OK,
        )


class PropertyAmenityAPIView(APIView):
    """
    API view to manage property amenities with efficient storage and error handling.
    """

    permission_classes = [IsAuthenticated, PropertyConfigPermission]

    def get(self, request, *args, **kwargs):
        property_id = request.query_params.get("property")

        if not property_id:
            return Response(
                {"error": "Property ID is required as a query parameter."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Validate UUID format
            try:
                uuid.UUID(str(property_id))
            except ValueError:
                return Response(
                    {"error": "Invalid property ID format. Must be a valid UUID."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if property exists
            try:
                Property.objects.get(id=property_id)
            except Property.DoesNotExist:
                return Response(
                    {"error": "Property not found."}, status=status.HTTP_404_NOT_FOUND
                )

            # Get all amenities for the property
            property_amenities = PropertyAmenity.objects.filter(
                property_id=property_id
            ).select_related("amenity")

            if not property_amenities.exists():
                return Response(
                    {"property": property_id, "amenities": []},
                    status=status.HTTP_200_OK,
                )

            # Serialize the amenities data
            serializer = PropertyAmenitySerializer(property_amenities, many=True)
            return Response(
                {"property": property_id, "amenities": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(
                f"Error retrieving property amenities: {str(e)}", exc_info=True
            )
            return Response(
                {"error": "An error occurred while retrieving property amenities."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def post(self, request, *args, **kwargs):
        """
        Create or update PropertyAmenity instances in bulk for a specific property.
        """
        property_id = request.data.get("property")
        amenities_data = request.data.get("amenities", [])

        if not property_id:
            return Response(
                {"error": "Property ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Validate room_id as a valid UUID
            property_id = uuid.UUID(property_id, version=4)
        except ValueError:
            return Response(
                {"error": "Invalid Property ID format. Please provide a valid UUID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        property = get_object_or_404(Property, id=property_id)

        created_or_updated_amenities = []

        for amenity_data in amenities_data:
            try:
                # Normalize the input data
                name = amenity_data["name"].strip().lower()
                category = amenity_data["category"].strip().lower()

                amenity = Amenity.objects.filter(name=name, category=category).first()

                if not amenity:
                    amenity = Amenity.objects.create(name=name, category=category)

                # Create or update the PropertyAmenity through table entry
                property_amenity, _ = PropertyAmenity.objects.update_or_create(
                    property=property,
                    amenity=amenity,
                    defaults={"is_available": amenity_data.get("is_available", True)},
                )
                created_or_updated_amenities.append(property_amenity)

            except Exception as e:
                return Response(
                    {
                        "error": f"Error processing amenity '{amenity_data.get('name', 'unknown')}': {str(e)}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Serialize the response
        serializer = PropertyAmenitySerializer(created_or_updated_amenities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class RoomViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing and editing Room instances.
    """

    queryset = Room.objects.all()
    serializer_class = RoomSerializer
    permission_classes = [IsAuthenticated, RoomConfigPermission]

    def get_queryset(self):
        """
        Retrieve rooms associated with a specific property when `property` is provided.
        """
        if self.action == "list":
            property_id = self.request.query_params.get("property")

            if not property_id:
                raise ValidationError(
                    {"error": "Property ID is required as a query parameter."}
                )

            # Validate the UUID
            try:
                uuid.UUID(property_id, version=4)
            except ValueError:
                raise ValidationError(
                    {"error": "Invalid Property ID format. Must be a valid UUID."}
                )

            rooms = Room.objects.filter(property=property_id)

            if not rooms.exists():
                raise NotFound(
                    {"error": "No rooms found for the specified property ID."}
                )

            return rooms

        return super().get_queryset()

    def list(self, request, *args, **kwargs):
        """
        Custom list method to handle room listing with property filtering and error handling.
        """
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except ValidationError as e:
            return Response({"error": e.messages}, status=400)
        except NotFound as e:
            return Response({"error": str(e)}, status=status.HTTP_404_NOT_FOUND)


class RoomAmenityViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing, creating, and updating RoomAmenity instances in bulk.
    """

    queryset = RoomAmenity.objects.all()
    serializer_class = RoomAmenitySerializer
    permission_classes = [IsAuthenticated, RoomConfigPermission]

    def get_queryset(self):
        """
        Retrieve all room amenities associated with a specific room.
        """
        room_id = self.request.query_params.get("room")

        if not room_id:
            raise ValidationError(
                {"error": "Room ID is required as a query parameter."}
            )

        try:
            # Validate room_id as a valid UUID
            room_id = uuid.UUID(room_id, version=4)
        except ValueError:
            raise ValidationError(
                {"error": "Invalid Room ID format. Please provide a valid UUID."}
            )

        room_amenities = RoomAmenity.objects.filter(room=room_id)

        if not room_amenities.exists():
            raise NotFound(
                {"error": "No room amenity found for the specified Room ID."}
            )

        return room_amenities

    def list(self, request, *args, **kwargs):
        """
        Custom list method to filter room amenities by room ID with error handling.
        """
        room_id = request.query_params.get("room")

        if not room_id:
            return Response(
                {"error": "Room ID is required as a query parameter."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Validate room_id as a valid UUID
            room_id = uuid.UUID(room_id, version=4)
        except ValueError:
            return Response(
                {"error": "Invalid Room ID format. Please provide a valid UUID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        room_amenities = RoomAmenity.objects.filter(room=room_id)

        if not room_amenities.exists():
            return Response(
                {"error": "No room amenities found for the specified Room ID."},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(room_amenities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        """
        Create or update RoomAmenity instances in bulk for a specific room.
        """
        room_id = request.data.get("room")
        amenities_data = request.data.get("amenities", [])

        if not room_id:
            return Response(
                {"error": "Room ID is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Validate room_id as a valid UUID
            room_id = uuid.UUID(room_id, version=4)
        except ValueError:
            return Response(
                {"error": "Invalid Room ID format. Please provide a valid UUID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        room = get_object_or_404(Room, id=room_id)

        created_or_updated_amenities = []

        for amenity_data in amenities_data:
            try:
                # First get or create the Amenity instance
                amenity, _ = Amenity.objects.get_or_create(
                    name=amenity_data["name"], category=amenity_data["category"]
                )

                # Then create or update the RoomAmenity through table entry
                room_amenity, _ = RoomAmenity.objects.update_or_create(
                    room=room,
                    amenity=amenity,
                    defaults={"is_available": amenity_data.get("is_available", True)},
                )
                created_or_updated_amenities.append(room_amenity)

            except Exception as e:
                return Response(
                    {
                        "error": f"Error processing amenity '{amenity_data.get('name', 'unknown')}': {str(e)}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Serialize the response
        serializer = self.get_serializer(created_or_updated_amenities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


# Photo Views
class PhotoViewSet(viewsets.ModelViewSet):
    """
    Optimized viewset for handling photo uploads with parallel processing.
    """

    queryset = Photo.objects.all()
    serializer_class = PhotoSerializer
    permission_classes = [IsAuthenticated, PropertyConfigPermission]
    MAX_UPLOAD_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_PARALLEL_UPLOADS = 10  # Limit concurrent uploads

    def get_queryset(self):
        """Filter photos by user's properties"""
        try:
            return Photo.objects.filter(property__staffs=self.request.user)
        except Exception as e:
            logger.error(f"Error retrieving photos: {str(e)}")
            raise ValidationError({"error": "Error retrieving photos"})

    def _process_single_image(self, image, property_id, room_id):
        """
        Process a single image upload in a thread-safe manner with enhanced error handling.
        Returns a tuple of (success, result/error)
        """
        try:
            # Log the image details
            logger.info(
                f"Processing image: {image.name}, size: {image.size}, content_type: {getattr(image, 'content_type', 'unknown')}"
            )

            # Process through serializer
            serializer = self.get_serializer(
                data={
                    "property": property_id,
                    "room": room_id,
                    "image": image,
                }
            )

            if not serializer.is_valid():
                # Extract detailed error information
                errors = serializer.errors
                error_detail = None

                # Check for image-specific errors first
                if "image" in errors:
                    if isinstance(errors["image"], list) and errors["image"]:
                        error_detail = errors["image"][0]
                    elif (
                        isinstance(errors["image"], dict) and "error" in errors["image"]
                    ):
                        error_detail = errors["image"]["error"]

                # If no image-specific error found, check for general errors
                if not error_detail:
                    # Try to get the first error from any field
                    for field, field_errors in errors.items():
                        if field_errors:
                            if isinstance(field_errors, list):
                                error_detail = field_errors[0]
                                break
                            elif (
                                isinstance(field_errors, dict)
                                and "error" in field_errors
                            ):
                                error_detail = field_errors["error"]
                                break

                # If still no error detail, use a generic message
                if not error_detail:
                    error_detail = "Validazione dell'immagine fallita"

                logger.warning(
                    f"Image validation failed for {image.name}: {error_detail}"
                )
                return False, {"file": image.name, "error": error_detail}

            # Save the validated image
            saved_image = serializer.save()
            logger.info(
                f"Successfully processed image: {image.name}, saved as ID: {saved_image.id}"
            )
            return True, saved_image

        except serializers.ValidationError as e:
            # Handle validation errors
            error_detail = None

            # Try to extract the error message
            if hasattr(e, "detail"):
                if isinstance(e.detail, dict) and "error" in e.detail:
                    error_detail = e.detail["error"]
                elif isinstance(e.detail, list) and e.detail:
                    error_detail = e.detail[0]
                else:
                    error_detail = str(e.detail)

            if not error_detail:
                error_detail = str(e)

            logger.warning(f"Validation error for {image.name}: {error_detail}")
            return False, {"file": image.name, "error": error_detail}
        except Exception as e:
            # Log the unexpected error with traceback
            logger.error(
                f"Unexpected error processing {image.name}: {str(e)}", exc_info=True
            )
            return False, {"file": image.name, "error": f"Errore imprevisto: {str(e)}"}

    def _validate_upload_limit(self, property_id, num_new_photos):
        """
        Validate that uploading new photos won't exceed the limit
        """
        if property_id:
            property_instance = get_object_or_404(Property, id=property_id)
            current_count = Photo.objects.filter(property=property_instance).count()
            if current_count + num_new_photos > Photo.MAX_PROPERTY_PHOTOS:
                raise ValidationError(
                    f"Cannot upload {num_new_photos} photos. "
                    f"Property has {current_count}/{Photo.MAX_PROPERTY_PHOTOS} photos. "
                    f"Maximum allowed is {Photo.MAX_PROPERTY_PHOTOS}."
                )

    def create(self, request, *args, **kwargs):
        """
        Handle parallel image uploads with enhanced validation, error handling, and async optimization.
        """
        try:
            # Validate base parameters
            property_id = request.data.get("property")
            room_id = request.data.get("room")
            images = request.FILES.getlist("image")
            enable_async = (
                request.data.get("async_optimization", "true").lower() == "true"
            )

            logger.info(
                f"Photo upload request received: property_id={property_id}, room_id={room_id}, image_count={len(images) if images else 0}, async={enable_async}"
            )

            if not property_id and not room_id:
                logger.warning("Upload rejected: Neither property nor room ID provided")
                raise ValidationError("Either property or room ID must be provided")
            if not images:
                logger.warning("Upload rejected: No images provided")
                raise ValidationError("No images provided")

            # Check CDN availability
            if not CDNUtils.check_cdn_availability():
                logger.warning("CDN unavailable, falling back to sync processing")
                enable_async = False

            # Check upload limit before processing
            if property_id:
                try:
                    self._validate_upload_limit(property_id, len(images))
                    logger.info(
                        f"Upload limit validation passed for property {property_id} with {len(images)} images"
                    )
                except ValidationError as e:
                    logger.warning(f"Upload limit validation failed: {str(e)}")
                    raise

            # Process images
            results = []
            errors = []
            successful_uploads = []
            async_tasks = []

            # Process each image
            for i, image in enumerate(images):
                logger.info(f"Processing image {i+1}/{len(images)}: {image.name}")

                # Validate file first
                validation_result = CDNUtils.validate_file_for_upload(
                    image,
                    allowed_types=[
                        "image/jpeg",
                        "image/png",
                        "image/gif",
                        "image/webp",
                    ],
                    max_size=self.MAX_UPLOAD_SIZE,
                )

                if not validation_result["valid"]:
                    errors.append(
                        {
                            "file": image.name,
                            "error": "; ".join(validation_result["errors"]),
                        }
                    )
                    continue

                success, result = self._process_single_image(
                    image=image, property_id=property_id, room_id=room_id
                )

                if success:
                    successful_uploads.append(result)

                    # Queue for async optimization if enabled
                    if enable_async:
                        try:
                            # Use memory-efficient file streaming instead of loading into memory
                            task_id = CDNUtils.upload_image_async_from_file(
                                file_obj=image,
                                filename=image.name,
                                model_name="stay.Photo",
                                object_id=str(result.id),
                                field_name="image",
                            )
                            async_tasks.append(
                                {
                                    "photo_id": str(result.id),
                                    "task_id": task_id,
                                    "filename": image.name,
                                }
                            )
                            logger.info(
                                f"Queued async optimization for image {image.name} (task: {task_id})"
                            )
                        except Exception as async_error:
                            logger.error(
                                f"Failed to queue async optimization for {image.name}: {str(async_error)}"
                            )
                        except Exception as async_error:
                            logger.error(
                                f"Failed to queue async optimization for {image.name}: {str(async_error)}"
                            )
                            # Mark this specific image as not using async processing
                            current_async_enabled = False
                        else:
                            current_async_enabled = enable_async

                        results.append(
                            {
                                "file": image.name,
                                "status": "success",
                                "id": str(result.id),
                                "async_processing": current_async_enabled,
                            }
                        )
                    results.append(
                        {
                            "file": image.name,
                            "status": "success",
                            "id": str(result.id),
                            "async_processing": enable_async,
                        }
                    )
                    logger.info(
                        f"Successfully processed image {i+1}/{len(images)}: {image.name}"
                    )
                else:
                    errors.append(result)
                    logger.warning(
                        f"Failed to process image {i+1}/{len(images)}: {image.name} - {result.get('error', 'Unknown error')}"
                    )

            # If any errors occurred, rollback successful uploads
            if errors:
                logger.warning(
                    f"Rolling back {len(successful_uploads)} successful uploads due to {len(errors)} errors"
                )
                for photo in successful_uploads:
                    try:
                        # Delete from CDN storage if it exists
                        if photo.image and photo.id in [
                            task["photo_id"] for task in async_tasks
                        ]:
                            CDNUtils.delete_file(photo.image.name, "photo")
                        photo.delete()
                        logger.info(f"Rolled back photo ID: {photo.id}")
                    except Exception as delete_error:
                        logger.error(
                            f"Error rolling back photo ID {photo.id}: {str(delete_error)}"
                        )

                # Format error response
                error_response = {
                    "message": "Upload failed due to validation errors",
                    "errors": errors,
                    "failed_files": [error["file"] for error in errors],
                    "error_count": len(errors),
                    "total_files": len(images),
                }
                logger.info(f"Returning error response with {len(errors)} errors")
                return Response(error_response, status=status.HTTP_400_BAD_REQUEST)

            # Return success response
            serializer = self.get_serializer(successful_uploads, many=True)
            success_response = {
                "message": "All files uploaded successfully",
                "data": serializer.data,
                "results": results,
                "success_count": len(successful_uploads),
                "total_files": len(images),
                "async_tasks": async_tasks if enable_async else [],
                "cdn_optimized": enable_async,
            }
            logger.info(f"Successfully uploaded {len(successful_uploads)} images")
            return Response(success_response, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            error_detail = str(e)
            if hasattr(e, "detail"):
                if isinstance(e.detail, dict):
                    error_detail = e.detail
                elif isinstance(e.detail, list) and e.detail:
                    error_detail = e.detail[0]

            logger.warning(f"Validation error during photo upload: {error_detail}")
            return Response(
                {"message": "Validation error", "error": error_detail},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(
                f"Unexpected error during photo upload: {str(e)}", exc_info=True
            )
            return Response(
                {
                    "message": "Si è verificato un errore imprevisto durante il caricamento delle foto",
                    "error": f"Errore: {str(e)}",
                    "error_type": e.__class__.__name__,
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["delete"], url_path="bulk-delete")
    def bulk_delete(self, request):
        """
        Bulk delete photos with validation and error handling
        """
        try:
            photo_ids = request.data.get("ids", [])
            if not photo_ids:
                raise ValidationError("No photo IDs provided")

            # Get photos and check existence
            photos = Photo.objects.filter(id__in=photo_ids)
            if not photos.exists():
                raise NotFound("No photos found with the given IDs")

            found_ids = set(photos.values_list("id", flat=True))
            missing_ids = set(photo_ids) - found_ids

            if missing_ids:
                raise NotFound(f"Photos not found: {list(missing_ids)}")

            # Check permissions for all photos
            for photo in photos:
                self.check_object_permissions(request, photo)

            # Delete photos
            deletion_count = photos.count()
            photos.delete()

            return Response(
                {
                    "message": f"Successfully deleted {deletion_count} photos",
                    "deleted_ids": list(found_ids),
                },
                status=status.HTTP_200_OK,
            )

        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except PermissionDenied as e:
            return Response({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except NotFound as e:
            return Response({"error": str(e)}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error in bulk delete: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"], url_path="task-status/(?P<task_id>[^/.]+)")
    def check_task_status(self, request, task_id=None):
        """
        Check the status of an async image optimization task
        """
        try:
            from celery import current_app

            if not task_id:
                raise ValidationError("Task ID is required")

            # Get task result
            result = current_app.AsyncResult(task_id)

            response_data = {
                "task_id": task_id,
                "status": result.status,
                "ready": result.ready(),
            }

            if result.ready():
                if result.successful():
                    response_data["result"] = result.result
                    response_data["message"] = "Task completed successfully"
                else:
                    response_data["error"] = str(result.result)
                    response_data["message"] = "Task failed"
            else:
                response_data["message"] = "Task is still processing"

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error checking task status {task_id}: {str(e)}")
            return Response(
                {"error": "Failed to check task status", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"], url_path="cdn-health")
    def check_cdn_health(self, request):
        """
        Check CDN and storage health status
        """
        try:
            health_info = CDNUtils.check_cdn_availability()
            detailed_health = None

            # Get detailed health info if user is staff
            if request.user.is_staff:
                from services.storage.storage import CDNStorageService

                detailed_health = CDNStorageService.check_cdn_health()

            response_data = {
                "cdn_available": health_info,
                "timestamp": timezone.now().isoformat(),
            }

            if detailed_health:
                response_data.update(detailed_health)

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error checking CDN health: {str(e)}")
            return Response(
                {
                    "cdn_available": False,
                    "error": str(e),
                    "timestamp": timezone.now().isoformat(),
                },
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )


class GuestArrivalInfoViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing guest arrival information.
    """

    queryset = GuestArrivalInfo.objects.all()
    serializer_class = GuestArrivalInfoSerializer
    permission_classes = [IsAuthenticated, GuestArrivalInfoPermission]

    def list(self, request, *args, **kwargs):
        """
        Handles GET requests to retrieve guest arrival info.
        If a 'property' query parameter is provided, it returns the specific
        guest arrival info for that property; otherwise, it returns all records.
        """
        property_id = request.query_params.get("property")

        if property_id:
            # Validate UUID format
            try:
                uuid.UUID(property_id, version=4)
            except ValueError:
                return Response(
                    {"error": "Invalid UUID format for 'property' parameter."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Fetch property instance and guest info
            property_instance = get_object_or_404(Property, id=property_id)
            try:
                guest_info = GuestArrivalInfo.objects.get(property=property_instance)
            except GuestArrivalInfo.DoesNotExist:
                return Response(
                    {
                        "error": "Guest arrival info not found for the specified property."
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )
            serializer = self.get_serializer(guest_info)
            return Response(serializer.data, status=status.HTTP_200_OK)

        # Default behavior: return all guest arrival info if no property is specified
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        property_id = request.data.get("property")

        # Validate UUID format
        try:
            uuid.UUID(property_id, version=4)
        except ValueError:
            return Response(
                {"error": "Invalid UUID format for 'property' field."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        property_instance = get_object_or_404(Property, id=property_id)

        if GuestArrivalInfo.objects.filter(property=property_instance).exists():
            return Response(
                {"error": "Guest arrival info already exists for this property."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(property=property_instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", True)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)


class PropertySettingsStatusView(View):
    """
    API endpoint to get the completion status of property settings sections.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            property_id = request.query_params.get("property")
            if not property_id:
                return Response(
                    {"error": "Property ID is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            try:
                property_id = uuid.UUID(str(property_id), version=4)
            except (ValueError, AttributeError, TypeError):
                raise ValidationError(
                    {"error": "Invalid Property ID format. Must be a valid UUID."}
                )

            # Get property instance
            try:
                property_instance = Property.objects.get(id=property_id)
            except Property.DoesNotExist:
                return Response(
                    {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
                )

            # Check if user has permission
            if request.user not in property_instance.staffs.all():
                return Response(
                    {"error": "You don't have permission to view this property"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            is_owner = validate_ownership(request.user, property_id)
            status_data = check_status(property_instance, request.user, is_owner)
            return Response(status_data, status=status.HTTP_200_OK)

        except ValidationError as e:
            return Response(e.error_dict, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(
                f"Error getting property settings status: {str(e)}", exc_info=True
            )
            return Response(
                {"error": "An error occurred while fetching property settings status"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
