#!/bin/bash

# Backup and Recovery Script for Heibooky Monitoring Stack
# Provides comprehensive backup and disaster recovery capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_DIR/backups}"
BACKUP_LOG="$PROJECT_DIR/logs/backup.log"
RECOVERY_LOG="$PROJECT_DIR/logs/recovery.log"

# Backup configuration
BACKUP_RETENTION_DAYS=30
FULL_BACKUP_RETENTION_WEEKS=12
COMPRESSION_LEVEL=6
ENCRYPTION_ENABLED=true
ENCRYPTION_KEY_FILE="$PROJECT_DIR/.backup_key"

# Services to backup
SERVICES=(
    "prometheus:/prometheus:prometheus_data"
    "grafana:/var/lib/grafana:grafana_data"
    "loki:/loki:loki_data"
    "alertmanager:/alertmanager:alertmanager_data"
)

# Configuration files to backup
CONFIG_FILES=(
    "monitoring/prometheus"
    "monitoring/grafana"
    "monitoring/loki"
    "monitoring/alertmanager"
    "monitoring/security"
    "monitoring/performance"
    "monitoring/reliability"
    "docker-compose.yml"
    "monitoring/docker-compose.*.yml"
    ".env"
    "monitoring/.env*"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create necessary directories
mkdir -p "$BACKUP_DIR" "$PROJECT_DIR/logs"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$BACKUP_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$BACKUP_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$BACKUP_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$BACKUP_LOG"
}

recovery_log() {
    echo -e "${BLUE}[RECOVERY]${NC} $1" | tee -a "$RECOVERY_LOG"
}

# Function to generate encryption key if not exists
generate_encryption_key() {
    if [ ! -f "$ENCRYPTION_KEY_FILE" ]; then
        log "Generating encryption key"
        openssl rand -base64 32 > "$ENCRYPTION_KEY_FILE"
        chmod 600 "$ENCRYPTION_KEY_FILE"
        success "Encryption key generated"
    fi
}

# Function to encrypt file
encrypt_file() {
    local input_file="$1"
    local output_file="$2"
    
    if [ "$ENCRYPTION_ENABLED" = true ]; then
        if [ -f "$ENCRYPTION_KEY_FILE" ]; then
            openssl enc -aes-256-cbc -salt -in "$input_file" -out "$output_file" -pass file:"$ENCRYPTION_KEY_FILE"
            rm -f "$input_file"
            return 0
        else
            error "Encryption key not found"
            return 1
        fi
    else
        mv "$input_file" "$output_file"
        return 0
    fi
}

# Function to decrypt file
decrypt_file() {
    local input_file="$1"
    local output_file="$2"
    
    if [[ "$input_file" == *.enc ]]; then
        if [ -f "$ENCRYPTION_KEY_FILE" ]; then
            openssl enc -aes-256-cbc -d -in "$input_file" -out "$output_file" -pass file:"$ENCRYPTION_KEY_FILE"
            return 0
        else
            error "Encryption key not found for decryption"
            return 1
        fi
    else
        cp "$input_file" "$output_file"
        return 0
    fi
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running or not accessible"
        return 1
    fi
    return 0
}

# Function to stop monitoring services
stop_services() {
    log "Stopping monitoring services for backup"
    cd "$PROJECT_DIR"
    
    local services_to_stop=()
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r service _ _ <<< "$service_config"
        services_to_stop+=("$service")
    done
    
    if docker-compose stop "${services_to_stop[@]}"; then
        success "Services stopped successfully"
        return 0
    else
        error "Failed to stop services"
        return 1
    fi
}

# Function to start monitoring services
start_services() {
    log "Starting monitoring services after backup/recovery"
    cd "$PROJECT_DIR"
    
    if docker-compose up -d; then
        success "Services started successfully"
        
        # Wait for services to be ready
        log "Waiting for services to be ready..."
        sleep 30
        
        # Check service health
        if command -v "$SCRIPT_DIR/health-monitor.sh" >/dev/null 2>&1; then
            "$SCRIPT_DIR/health-monitor.sh" monitor
        fi
        
        return 0
    else
        error "Failed to start services"
        return 1
    fi
}

# Function to backup service data
backup_service_data() {
    local service="$1"
    local container_path="$2"
    local volume_name="$3"
    local backup_timestamp="$4"
    
    log "Backing up $service data"
    
    local backup_file="$BACKUP_DIR/${service}_data_${backup_timestamp}.tar.gz"
    local temp_backup_file="/tmp/${service}_backup_${backup_timestamp}.tar.gz"
    
    # Create backup using docker run with volume mount
    if docker run --rm \
        -v "${volume_name}:${container_path}:ro" \
        -v "/tmp:/backup" \
        alpine:latest \
        tar -czf "/backup/$(basename "$temp_backup_file")" -C "$container_path" .; then
        
        # Encrypt and move to final location
        if [ "$ENCRYPTION_ENABLED" = true ]; then
            if encrypt_file "$temp_backup_file" "${backup_file}.enc"; then
                success "$service data backup completed (encrypted)"
            else
                error "$service data backup encryption failed"
                return 1
            fi
        else
            mv "$temp_backup_file" "$backup_file"
            success "$service data backup completed"
        fi
        
        return 0
    else
        error "$service data backup failed"
        return 1
    fi
}

# Function to backup configuration files
backup_config_files() {
    local backup_timestamp="$1"
    
    log "Backing up configuration files"
    
    local config_backup_file="$BACKUP_DIR/config_${backup_timestamp}.tar.gz"
    local temp_config_file="/tmp/config_backup_${backup_timestamp}.tar.gz"
    
    cd "$PROJECT_DIR"
    
    # Create list of files to backup
    local files_to_backup=()
    for pattern in "${CONFIG_FILES[@]}"; do
        # Use find to handle patterns and check if files exist
        while IFS= read -r -d '' file; do
            files_to_backup+=("$file")
        done < <(find . -path "./$pattern" -type f -print0 2>/dev/null || true)
    done
    
    if [ ${#files_to_backup[@]} -eq 0 ]; then
        warning "No configuration files found to backup"
        return 1
    fi
    
    # Create tar archive
    if tar -czf "$temp_config_file" "${files_to_backup[@]}"; then
        # Encrypt and move to final location
        if [ "$ENCRYPTION_ENABLED" = true ]; then
            if encrypt_file "$temp_config_file" "${config_backup_file}.enc"; then
                success "Configuration backup completed (encrypted)"
            else
                error "Configuration backup encryption failed"
                return 1
            fi
        else
            mv "$temp_config_file" "$config_backup_file"
            success "Configuration backup completed"
        fi
        
        return 0
    else
        error "Configuration backup failed"
        return 1
    fi
}

# Function to create full backup
create_full_backup() {
    local backup_type="${1:-incremental}"
    local backup_timestamp
    backup_timestamp=$(date +%Y%m%d_%H%M%S)
    
    log "Starting $backup_type backup at $backup_timestamp"
    
    if [ "$ENCRYPTION_ENABLED" = true ]; then
        generate_encryption_key
    fi
    
    # Create backup metadata
    local metadata_file="$BACKUP_DIR/backup_${backup_timestamp}.json"
    cat > "$metadata_file" <<EOF
{
    "timestamp": "$(date -Iseconds)",
    "type": "$backup_type",
    "hostname": "$(hostname)",
    "docker_version": "$(docker --version)",
    "compose_version": "$(docker-compose --version)",
    "services": [$(printf '"%s",' "${SERVICES[@]%:*}" | sed 's/,$//')]
}
EOF
    
    local backup_success=true
    
    # Stop services for consistent backup
    if ! stop_services; then
        error "Failed to stop services, aborting backup"
        return 1
    fi
    
    # Backup service data
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r service container_path volume_name <<< "$service_config"
        
        if ! backup_service_data "$service" "$container_path" "$volume_name" "$backup_timestamp"; then
            backup_success=false
        fi
    done
    
    # Backup configuration files
    if ! backup_config_files "$backup_timestamp"; then
        backup_success=false
    fi
    
    # Start services again
    start_services
    
    if [ "$backup_success" = true ]; then
        success "Full backup completed successfully: $backup_timestamp"
        
        # Create backup manifest
        local manifest_file="$BACKUP_DIR/backup_${backup_timestamp}_manifest.txt"
        ls -la "$BACKUP_DIR"/*"$backup_timestamp"* > "$manifest_file"
        
        # Clean old backups
        cleanup_old_backups
        
        return 0
    else
        error "Backup completed with errors"
        return 1
    fi
}

# Function to restore from backup
restore_from_backup() {
    local backup_timestamp="$1"
    
    if [ -z "$backup_timestamp" ]; then
        error "Backup timestamp is required for restore"
        return 1
    fi
    
    recovery_log "Starting restore from backup: $backup_timestamp"
    
    # Check if backup files exist
    local backup_files=("$BACKUP_DIR"/*"$backup_timestamp"*)
    if [ ${#backup_files[@]} -eq 0 ] || [ ! -f "${backup_files[0]}" ]; then
        error "Backup files not found for timestamp: $backup_timestamp"
        return 1
    fi
    
    # Stop services
    if ! stop_services; then
        error "Failed to stop services, aborting restore"
        return 1
    fi
    
    local restore_success=true
    
    # Restore service data
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r service container_path volume_name <<< "$service_config"
        
        local backup_file="$BACKUP_DIR/${service}_data_${backup_timestamp}.tar.gz"
        if [ "$ENCRYPTION_ENABLED" = true ]; then
            backup_file="${backup_file}.enc"
        fi
        
        if [ -f "$backup_file" ]; then
            recovery_log "Restoring $service data"
            
            local temp_restore_file="/tmp/${service}_restore_${backup_timestamp}.tar.gz"
            
            # Decrypt if necessary
            if ! decrypt_file "$backup_file" "$temp_restore_file"; then
                error "Failed to decrypt $service backup"
                restore_success=false
                continue
            fi
            
            # Remove existing volume data
            docker volume rm "$volume_name" 2>/dev/null || true
            docker volume create "$volume_name"
            
            # Restore data using docker run
            if docker run --rm \
                -v "${volume_name}:${container_path}" \
                -v "/tmp:/backup" \
                alpine:latest \
                sh -c "cd $container_path && tar -xzf /backup/$(basename "$temp_restore_file")"; then
                
                success "$service data restored successfully"
                rm -f "$temp_restore_file"
            else
                error "$service data restore failed"
                restore_success=false
            fi
        else
            warning "$service backup file not found: $backup_file"
            restore_success=false
        fi
    done
    
    # Restore configuration files
    local config_backup_file="$BACKUP_DIR/config_${backup_timestamp}.tar.gz"
    if [ "$ENCRYPTION_ENABLED" = true ]; then
        config_backup_file="${config_backup_file}.enc"
    fi
    
    if [ -f "$config_backup_file" ]; then
        recovery_log "Restoring configuration files"
        
        local temp_config_file="/tmp/config_restore_${backup_timestamp}.tar.gz"
        
        # Decrypt if necessary
        if decrypt_file "$config_backup_file" "$temp_config_file"; then
            cd "$PROJECT_DIR"
            
            if tar -xzf "$temp_config_file"; then
                success "Configuration files restored successfully"
                rm -f "$temp_config_file"
            else
                error "Configuration files restore failed"
                restore_success=false
            fi
        else
            error "Failed to decrypt configuration backup"
            restore_success=false
        fi
    else
        warning "Configuration backup file not found: $config_backup_file"
        restore_success=false
    fi
    
    # Start services
    start_services
    
    if [ "$restore_success" = true ]; then
        success "Restore completed successfully from backup: $backup_timestamp"
        return 0
    else
        error "Restore completed with errors"
        return 1
    fi
}

# Function to list available backups
list_backups() {
    log "Available backups:"
    
    if [ ! -d "$BACKUP_DIR" ] || [ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
        warning "No backups found in $BACKUP_DIR"
        return 1
    fi
    
    # Group backups by timestamp
    local timestamps
    timestamps=$(ls "$BACKUP_DIR" | grep -E '_[0-9]{8}_[0-9]{6}' | sed -E 's/.*_([0-9]{8}_[0-9]{6}).*/\1/' | sort -u)
    
    for timestamp in $timestamps; do
        echo ""
        echo "Backup: $timestamp ($(date -d "${timestamp:0:8} ${timestamp:9:2}:${timestamp:11:2}:${timestamp:13:2}" 2>/dev/null || echo "Invalid date"))"
        
        local backup_files
        backup_files=$(ls "$BACKUP_DIR"/*"$timestamp"* 2>/dev/null || true)
        
        if [ -n "$backup_files" ]; then
            echo "$backup_files" | while read -r file; do
                local size
                size=$(du -h "$file" | cut -f1)
                echo "  - $(basename "$file") ($size)"
            done
        fi
        
        # Show metadata if available
        local metadata_file="$BACKUP_DIR/backup_${timestamp}.json"
        if [ -f "$metadata_file" ]; then
            local backup_type
            backup_type=$(jq -r '.type' "$metadata_file" 2>/dev/null || echo "unknown")
            echo "  Type: $backup_type"
        fi
    done
    
    echo ""
}

# Function to cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups"
    
    # Remove incremental backups older than retention period
    find "$BACKUP_DIR" -name "*_[0-9]*_[0-9]*.tar.gz*" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*_[0-9]*_[0-9]*.json" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*_[0-9]*_[0-9]*_manifest.txt" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    
    # Remove full backups older than weekly retention
    local weeks_in_days=$((FULL_BACKUP_RETENTION_WEEKS * 7))
    find "$BACKUP_DIR" -name "full_*" -mtime +$weeks_in_days -delete 2>/dev/null || true
    
    success "Old backups cleaned up"
}

# Function to verify backup integrity
verify_backup() {
    local backup_timestamp="$1"
    
    if [ -z "$backup_timestamp" ]; then
        error "Backup timestamp is required for verification"
        return 1
    fi
    
    log "Verifying backup integrity: $backup_timestamp"
    
    local verification_success=true
    
    # Check if all expected backup files exist
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r service _ _ <<< "$service_config"
        
        local backup_file="$BACKUP_DIR/${service}_data_${backup_timestamp}.tar.gz"
        if [ "$ENCRYPTION_ENABLED" = true ]; then
            backup_file="${backup_file}.enc"
        fi
        
        if [ -f "$backup_file" ]; then
            # Test archive integrity
            local temp_test_file="/tmp/test_${service}_${backup_timestamp}.tar.gz"
            
            if [ "$ENCRYPTION_ENABLED" = true ]; then
                if ! decrypt_file "$backup_file" "$temp_test_file"; then
                    error "$service backup decryption failed"
                    verification_success=false
                    continue
                fi
            else
                cp "$backup_file" "$temp_test_file"
            fi
            
            if tar -tzf "$temp_test_file" >/dev/null 2>&1; then
                success "$service backup integrity verified"
            else
                error "$service backup is corrupted"
                verification_success=false
            fi
            
            rm -f "$temp_test_file"
        else
            error "$service backup file not found: $backup_file"
            verification_success=false
        fi
    done
    
    # Verify configuration backup
    local config_backup_file="$BACKUP_DIR/config_${backup_timestamp}.tar.gz"
    if [ "$ENCRYPTION_ENABLED" = true ]; then
        config_backup_file="${config_backup_file}.enc"
    fi
    
    if [ -f "$config_backup_file" ]; then
        local temp_config_test="/tmp/test_config_${backup_timestamp}.tar.gz"
        
        if [ "$ENCRYPTION_ENABLED" = true ]; then
            if ! decrypt_file "$config_backup_file" "$temp_config_test"; then
                error "Configuration backup decryption failed"
                verification_success=false
            fi
        else
            cp "$config_backup_file" "$temp_config_test"
        fi
        
        if [ -f "$temp_config_test" ] && tar -tzf "$temp_config_test" >/dev/null 2>&1; then
            success "Configuration backup integrity verified"
        else
            error "Configuration backup is corrupted"
            verification_success=false
        fi
        
        rm -f "$temp_config_test"
    else
        error "Configuration backup file not found: $config_backup_file"
        verification_success=false
    fi
    
    if [ "$verification_success" = true ]; then
        success "Backup verification completed successfully"
        return 0
    else
        error "Backup verification failed"
        return 1
    fi
}

# Main execution
main() {
    if ! check_docker; then
        error "Docker check failed, exiting"
        exit 1
    fi
    
    case "${1:-help}" in
        "backup")
            local backup_type="${2:-incremental}"
            create_full_backup "$backup_type"
            ;;
        "restore")
            local backup_timestamp="$2"
            restore_from_backup "$backup_timestamp"
            ;;
        "list")
            list_backups
            ;;
        "verify")
            local backup_timestamp="$2"
            verify_backup "$backup_timestamp"
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "help"|*)
            echo "Backup and Recovery for Heibooky Monitoring Stack"
            echo ""
            echo "Usage: $0 [command] [options]"
            echo ""
            echo "Commands:"
            echo "  backup [type]     - Create backup (type: incremental|full, default: incremental)"
            echo "  restore <timestamp> - Restore from backup"
            echo "  list              - List available backups"
            echo "  verify <timestamp> - Verify backup integrity"
            echo "  cleanup           - Clean up old backups"
            echo "  help              - Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  BACKUP_DIR - Backup directory (default: ./backups)"
            echo "  BACKUP_RETENTION_DAYS - Retention period in days (default: 30)"
            echo "  ENCRYPTION_ENABLED - Enable backup encryption (default: true)"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
