import logging

import stripe
from apps.billing.models import BillingProfile
from apps.billing.utils import check_billing_complete
from apps.integrations.models import Invoice, StripeCustomer
from apps.integrations.serializers import *
from apps.integrations.utils import prepare_account_params, prepare_account_update_data
from apps.stay.models import PropertyOwnership
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.utils.dateparse import parse_date
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet

logger = logging.getLogger(__name__)

stripe.api_key = settings.STRIPE_SECRET_KEY


class CreateConnectedAccount(APIView):
    permission_classes = [IsAuthenticated]

    def _handle_stripe_error(self, operation, error):
        """Handle Stripe API errors with consistent formatting."""
        return Response(
            {"error": f"{operation} failed: {str(error)}"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    def _handle_general_error(self, error):
        """Handle unexpected errors."""
        return Response(
            {"error": f"An unexpected error occurred: {str(error)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    def _check_prerequisites(self, user):
        """Check if user meets all prerequisites for creating a connected account."""
        if not check_billing_complete(user):
            return Response(
                {"error": "User has no complete billing profile"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if not PropertyOwnership.objects.filter(user=user).exists():
            return Response(
                {
                    "error": "Property ownership is required to create a connected account"
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        return None  # No errors

    def _validate_request(self, request):
        """Validate request data."""
        tos_accepted = request.data.get("tos_accepted", False)
        if not tos_accepted:
            return Response(
                {"error": "Terms of service must be accepted"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return None  # No errors

    def validate_iban(self, iban):
        """Validate IBAN format."""
        if not iban or len(iban) > 34 or len(iban) < 15:
            raise ValueError("Invalid IBAN format")
        return iban.replace(" ", "").upper()

    def validate_business_data(self, billing_profile, billing_address):
        """Validate business data for completeness."""
        if (
            billing_profile.recipient_type == BillingProfile.COMPANY
            and not billing_profile.company_name
        ):
            raise ValueError("Company name is required for business accounts")

        required_fields = {
            "first_name": billing_profile.first_name,
            "last_name": billing_profile.last_name,
            "date_of_birth": billing_profile.date_of_birth,
            "street_number": billing_address.street_number,
            "postcode": billing_address.postcode,
            "city": billing_address.city,
            "country": billing_address.country,
            "phone": self.request.user.phone,
        }

        missing_fields = [
            field for field, value in required_fields.items() if not value
        ]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    def _get_or_create_customer(self, user):
        """Get or create a Stripe customer for the user."""
        return StripeCustomer.objects.get_or_create(user=user)

    def _get_billing_data(self, user):
        """Get and validate billing data for the user."""
        try:
            billing_profile = BillingProfile.objects.get(owner=user)
            billing_address = billing_profile.billing_address
            self.validate_business_data(billing_profile, billing_address)
            validated_iban = self.validate_iban(billing_profile.iban)

            return billing_profile, billing_address, validated_iban

        except ObjectDoesNotExist:
            raise ValueError("Complete billing profile is required")

    def _create_stripe_account(self, user, account_params):
        """Create a new Stripe connected account."""
        try:
            return stripe.Account.create(**account_params)
        except stripe.error.StripeError as e:
            raise ValueError(f"Stripe account creation failed: {str(e)}")

    def _retrieve_stripe_account(self, stripe_customer_id):
        """Retrieve an existing Stripe connected account."""
        try:
            return stripe.Account.retrieve(stripe_customer_id)
        except stripe.error.StripeError as e:
            raise ValueError(f"Failed to retrieve Stripe account: {str(e)}")

    def _update_stripe_account(self, stripe_customer_id, account_update_data):
        """Update a Stripe connected account with new data."""
        try:
            return stripe.Account.modify(stripe_customer_id, **account_update_data)
        except stripe.error.StripeError as e:
            raise ValueError(f"Failed to update Stripe account: {str(e)}")

    def post(self, request):
        """Handle POST request to create or update a Stripe connected account."""
        try:
            user = request.user

            # Check prerequisites
            prerequisite_error = self._check_prerequisites(user)
            if prerequisite_error:
                return prerequisite_error

            # Validate request data
            validation_error = self._validate_request(request)
            if validation_error:
                return validation_error

            # Get or create Stripe customer
            customer, created = self._get_or_create_customer(user)

            try:
                # Get and validate billing data
                billing_profile, billing_address, validated_iban = (
                    self._get_billing_data(user)
                )

            except ValueError as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

            if created:
                # Log new account creation
                logger.info(f"Creating new Stripe account for user {user.id}")
                try:
                    account_params = prepare_account_params(billing_profile, user)
                    account = self._create_stripe_account(user, account_params)
                    customer.stripe_customer_id = account.id
                    customer.save()
                    logger.info(f"Successfully created Stripe account {account.id}")
                except ValueError as e:
                    logger.error(f"Failed to create Stripe account: {str(e)}")
                    return Response(
                        {"error": str(e)}, status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                # Retrieve existing Stripe account
                try:
                    account = self._retrieve_stripe_account(customer.stripe_customer_id)
                except ValueError as e:
                    return Response(
                        {"error": str(e)}, status=status.HTTP_400_BAD_REQUEST
                    )

            # Update Stripe account
            try:
                account_update_data = prepare_account_update_data(
                    billing_profile, billing_address, validated_iban, request
                )
                updated_account = self._update_stripe_account(
                    customer.stripe_customer_id, account_update_data
                )

                return Response(
                    {
                        "message": "Connected account created and updated successfully",
                        "account_id": customer.stripe_customer_id,
                        "status": updated_account.get("payouts_enabled", False),
                    },
                    status=status.HTTP_200_OK,
                )

            except ValueError as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Unexpected error in CreateConnectedAccount: {str(e)}")
            return self._handle_general_error(e)


class PayoutViewSet(ModelViewSet):
    """
    API view to handle Payments with optional filtering by date range.
    """

    queryset = Payout.objects.all()
    serializer_class = PayoutSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Override to filter payments based on start_date and end_date query parameters.
        """
        queryset = super().get_queryset()
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")

        if start_date:
            start_date = parse_date(start_date)
        if end_date:
            end_date = parse_date(end_date)

        if start_date and end_date:
            queryset = queryset.filter(created_at__date__range=[start_date, end_date])
        elif start_date:
            queryset = queryset.filter(created_at__date__gte=start_date)
        elif end_date:
            queryset = queryset.filter(created_at__date__lte=end_date)

        return queryset


class InvoiceView(APIView):
    permission_classes = [IsAuthenticated]
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer

    def get(self, request):
        """
        Handle GET requests to retrieve invoices.
        """
        queryset = self.get_queryset()
        serializer = InvoiceSerializer(queryset, many=True)
        return Response(serializer.data)

    def get_queryset(self):
        """
        Filter invoices based on the authenticated user and an optional date range.
        """
        user = self.request.user
        queryset = Invoice.objects.filter(owner=user)

        # Get query parameters for date range
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")

        if start_date or end_date:
            # Convert string to date objects
            try:
                if start_date:
                    start_date = parse_date(start_date)
                if end_date:
                    end_date = parse_date(end_date)

                # Apply date filters
                if start_date and end_date:
                    queryset = queryset.filter(
                        created_at__date__range=(start_date, end_date)
                    )
                elif start_date:
                    queryset = queryset.filter(created_at__date__gte=start_date)
                elif end_date:
                    queryset = queryset.filter(created_at__date__lte=end_date)
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        return queryset
