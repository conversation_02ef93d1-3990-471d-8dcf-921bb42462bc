#!/bin/bash
# Redis Development Setup Script
# Sets up Redis for local development without authentication

set -e

echo "🚀 Setting up Redis for local development..."

# Check if we're in the correct directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: docker-compose.yml not found. Run this script from the project root."
    exit 1
fi

# Create redis directory if it doesn't exist
mkdir -p redis

echo "📁 Redis directory ready"

# Validate configuration files
echo "🔍 Validating Redis configuration..."

if [ -f "scripts/validate_redis_config.py" ]; then
    python3 scripts/validate_redis_config.py
    if [ $? -ne 0 ]; then
        echo "❌ Configuration validation failed"
        exit 1
    fi
else
    echo "⚠️  Warning: Configuration validator not found, skipping validation"
fi

# Check Docker availability
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Check Docker Compose availability
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    echo "❌ Docker Compose is not available"
    exit 1
fi

echo "🐳 Using Docker Compose command: $COMPOSE_CMD"

# Stop existing Redis container if running
echo "🛑 Stopping existing Redis container..."
$COMPOSE_CMD stop redis || true

# Remove existing Redis container
echo "🗑️  Removing existing Redis container..."
$COMPOSE_CMD rm -f redis || true

# Start Redis service
echo "🚀 Starting Redis service..."
$COMPOSE_CMD up -d redis

# Wait for Redis to be ready
echo "⏳ Waiting for Redis to be ready..."
sleep 10

# Check Redis health
echo "🏥 Checking Redis health..."
$COMPOSE_CMD exec redis redis-cli ping

if [ $? -eq 0 ]; then
    echo "✅ Redis is running and healthy!"
else
    echo "❌ Redis health check failed"
    echo "📋 Redis logs:"
    $COMPOSE_CMD logs redis
    exit 1
fi


echo ""
echo "🎉 Redis setup complete!"
echo ""
echo "📋 Summary:"
echo "  - Redis is running on port 6379"
echo "  - No authentication required"
echo "  - Configuration: redis/redis.conf"
echo "  - ACL file: redis/users.acl"
echo ""
echo "🔧 Useful commands:"
echo "  - View logs: $COMPOSE_CMD logs redis"
echo "  - Connect to CLI: $COMPOSE_CMD exec redis redis-cli"
echo "  - Test connection: python scripts/test_redis_connection.py"
echo "  - Stop Redis: $COMPOSE_CMD stop redis"
