from datetime import date, timedelta
from unittest.mock import MagicMock, patch

from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Property
from apps.stay.models.location import Location
from apps.users.models import User
from django.test import TestCase
from services.email.email_service import EmailService


class OverbookingEmailTestCase(TestCase):
    """Simple test for the overbooking email functionality without complex signals."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", name="Test User"
        )

        self.location = Location.objects.create(
            street="Via Roma 123",
            post_code="00100",
            city="Roma",
            country="Italy",
            latitude=41.9028,
            longitude=12.4964,
        )

        self.property = Property.objects.create(
            name="Hotel Test Roma",
            property_type=Property.HOTEL,
            location=self.location,
            hotel_id="TEST123",
            chain_id="CHAIN123",
        )

        # Create mock bookings
        self.customer1 = Customer.objects.create(
            first_name="<PERSON>",
            last_name="<PERSON>",
            email="<EMAIL>",
            telephone="1234567890",
        )

        self.customer2 = Customer.objects.create(
            first_name="<PERSON>",
            last_name="<PERSON>ian<PERSON>",
            email="<EMAIL>",
            telephone="0987654321",
        )

        # Create reservations
        self.reservation1 = Reservation.objects.create(
            id="EXISTING123",
            guest_name="Mario Rossi",
            total_price=150.00,
            number_of_guests=2,
            number_of_adults=2,
        )

        self.reservation2 = Reservation.objects.create(
            id="NEW456",
            guest_name="Laura Bianchi",
            total_price=200.00,
            number_of_guests=2,
            number_of_adults=2,
        )

        # Create bookings
        today = date.today()
        self.existing_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer1,
            reservation_data=self.reservation1,
            is_manual=False,
            channel_code=Booking.ChannelChoices.BOOKING,
            checkin_date=today + timedelta(days=1),
            checkout_date=today + timedelta(days=3),
            status=Booking.Status.NEW,
        )

        self.new_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer2,
            reservation_data=self.reservation2,
            is_manual=False,
            channel_code=Booking.ChannelChoices.AIRBNB,
            checkin_date=today + timedelta(days=2),
            checkout_date=today + timedelta(days=4),
            status=Booking.Status.NEW,
        )

    @patch("services.email.email_service.EmailService.send_email")
    def test_overbooking_email_service_method(self, mock_send_email):
        """Test the send_overbooking_alert method directly."""
        mock_send_email.return_value = True

        email_service = EmailService()
        result = email_service.send_overbooking_alert(
            property_obj=self.property,
            existing_booking=self.existing_booking,
            new_booking=self.new_booking,
        )

        # Verify the method returns True
        self.assertTrue(result)

        # Verify send_email was called once
        mock_send_email.assert_called_once()

        # Get the call arguments
        args, kwargs = mock_send_email.call_args
        recipient, content = args

        # Verify recipient
        self.assertEqual(recipient.email, "<EMAIL>")

        # Verify content structure
        self.assertEqual(content.template_name, "overbooking_alert")
        self.assertIn("🚨 Overbooking Rilevato", content.subject)
        self.assertEqual(content.context["property"], self.property)
        self.assertEqual(content.context["existing_booking"], self.existing_booking)
        self.assertEqual(content.context["new_booking"], self.new_booking)

        # Verify overlap calculation
        expected_start = max(
            self.existing_booking.checkin_date, self.new_booking.checkin_date
        )
        expected_end = min(
            self.existing_booking.checkout_date, self.new_booking.checkout_date
        )
        self.assertEqual(content.context["overlap_start"], expected_start)
        self.assertEqual(content.context["overlap_end"], expected_end)

        # Verify admin URL is present
        self.assertIn("admin_url", content.context)
        self.assertIn(str(self.property.id), content.context["admin_url"])

    @patch("services.email.email_service.render_to_string")
    @patch("services.email.email_service.EmailMultiAlternatives")
    def test_email_template_rendering(self, mock_email_class, mock_render):
        """Test that the email templates are rendered correctly."""
        mock_render.return_value = "Mock email content"
        mock_email_instance = MagicMock()
        mock_email_class.return_value = mock_email_instance
        mock_email_instance.send.return_value = True

        email_service = EmailService()
        result = email_service.send_overbooking_alert(
            property_obj=self.property,
            existing_booking=self.existing_booking,
            new_booking=self.new_booking,
        )

        # Verify the method returns True
        self.assertTrue(result)

        # Verify render_to_string was called for both templates
        self.assertEqual(mock_render.call_count, 2)

        # Check that both HTML and text templates were rendered
        template_calls = [call[0][0] for call in mock_render.call_args_list]
        self.assertIn("emails/overbooking_alert.html", template_calls)
        self.assertIn("emails/overbooking_alert.txt", template_calls)

        # Verify email was sent
        mock_email_instance.send.assert_called_once()

    def test_context_data_completeness(self):
        """Test that all required context data is provided to the template."""
        email_service = EmailService()

        with patch.object(email_service, "send_email", return_value=True) as mock_send:
            email_service.send_overbooking_alert(
                property_obj=self.property,
                existing_booking=self.existing_booking,
                new_booking=self.new_booking,
            )

            # Verify send_email was called
            mock_send.assert_called_once()

            # Get the call arguments
            args, kwargs = mock_send.call_args
            recipient, content = args

            # Test the raw context that would be passed to _prepare_context
            raw_context = content.context

            # Check some basic required keys are present in raw context
            basic_required_keys = [
                "property",
                "existing_booking",
                "new_booking",
                "overlap_start",
                "overlap_end",
                "admin_url",
                "current_year",
            ]

            for key in basic_required_keys:
                self.assertIn(
                    key,
                    raw_context,
                    f"Required context key '{key}' is missing from raw context",
                )

            # Verify specific values in raw context
            from datetime import datetime

            self.assertEqual(raw_context["current_year"], datetime.now().year)
            self.assertEqual(raw_context["property"], self.property)
            self.assertEqual(raw_context["existing_booking"], self.existing_booking)
            self.assertEqual(raw_context["new_booking"], self.new_booking)

    @patch("services.email.email_service.logger")
    def test_error_handling(self, mock_logger):
        """Test error handling in the overbooking email method."""
        email_service = EmailService()

        # Mock an exception in send_email
        with patch.object(
            email_service, "send_email", side_effect=Exception("Test error")
        ):
            result = email_service.send_overbooking_alert(
                property_obj=self.property,
                existing_booking=self.existing_booking,
                new_booking=self.new_booking,
            )

            # Verify the method returns False on error
            self.assertFalse(result)

            # Verify error was logged
            mock_logger.error.assert_called_once()

            # Check the log message contains relevant information
            log_call = mock_logger.error.call_args
            self.assertIn("Failed to send overbooking alert", log_call[0][0])

    def test_translations_integration(self):
        """Test that Italian translations are properly integrated."""
        from assets.translations import TRANSLATIONS_IT

        # Verify overbooking-specific translations exist
        overbooking_keys = [
            "overbooking_alert",
            "overbooking_detected",
            "overbooking_message",
            "property_details",
            "booking_details",
            "existing_booking",
            "new_booking",
            "overlap_period",
            "view_bookings",
        ]

        for key in overbooking_keys:
            self.assertIn(key, TRANSLATIONS_IT, f"Translation key '{key}' is missing")
            self.assertIsInstance(TRANSLATIONS_IT[key], str)
            self.assertTrue(
                len(TRANSLATIONS_IT[key]) > 0, f"Translation for '{key}' is empty"
            )
