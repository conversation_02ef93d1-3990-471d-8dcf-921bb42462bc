import logging
import uuid
from calendar import monthrange
from datetime import date

from apps.stay.models import Property
from apps.stay.utils import cleaning_staff_check
from apps.users.permissions import RatesPermission, RoomRatesPermission
from django.db import models
from django.db.models import Q
from django.db.models.query import QuerySet
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import RatePlan, RoomRate
from .serializers import (
    CalendarRoomRateSerializer,
    RatePlanSerializer,
    RoomRateSerializer,
)

logger = logging.getLogger(__name__)


def safe_rate_to_int_string(rate) -> str:
    """
    Safely converts a rate value to an integer string.

    Args:
        rate: The rate value to convert (can be int, float, string, or None)

    Returns:
        str: String representation of the rate as an integer, or "0" if conversion fails
    """
    if rate is None:
        return "0"

    try:
        # First convert to float to handle decimal strings, then to int
        return str(int(float(rate)))
    except (ValueError, TypeError, OverflowError):
        logger.warning(
            f"Failed to convert rate value '{rate}' to integer, defaulting to '0'"
        )
        return "0"


class RatePlanViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing RatePlan operations with proper permission handling and validation.
    Supports CRUD operations and custom actions for rate plan management.
    """

    serializer_class = RatePlanSerializer
    permission_classes = [IsAuthenticated, RatesPermission]

    def _validate_property_id(self, property_id: str) -> None:
        """Validate property_id format"""
        try:
            uuid.UUID(str(property_id), version=4)
        except (ValueError, AttributeError, TypeError):
            logger.error(f"Invalid property ID format: {property_id}")
            raise ValidationError(
                {"property": "Invalid property ID format. Must be a valid UUID."}
            )

    def get_queryset(self) -> QuerySet:
        """
        Get filtered queryset based on user permissions and query parameters.
        Returns:
            QuerySet: Filtered RatePlan queryset
        """
        user = self.request.user
        property_id = self.request.query_params.get("property")

        # Start with base optimized queryset
        base_queryset = RatePlan.objects.select_related("property")

        # If no property filter, return all accessible rate plans
        if not property_id:
            return base_queryset.filter(property__staffs=user)

        self._validate_property_id(property_id)
        return base_queryset.filter(property_id=property_id, property__staffs=user)

    def create(self, request, *args, **kwargs):
        """
        Override create method to prevent creation of duplicate RatePlans.
        Returns:
            Response: Serialized RatePlan data with appropriate status code
        Raises:
            ValidationError: If required fields are missing or invalid
        """
        try:
            property_id = request.data.get("property")
            name = request.data.get("name")
            meal_plan = request.data.get("meal_plan")
            close_out_days = request.data.get("close_out_days")
            close_out_time = request.data.get("close_out_time")
            checkin_time = request.data.get("checkin_time")

            # Validate required fields
            if not all([property_id, name]):
                raise ValidationError(
                    {
                        "property": (
                            "Property ID is required" if not property_id else None
                        ),
                        "name": "Name is required" if not name else None,
                    }
                )

            # Validate property ID format
            self._validate_property_id(property_id)

            # Check for duplicate rate plan
            existing_rateplan = RatePlan.objects.filter(
                property_id=property_id,
                name__iexact=name,
                meal_plan=meal_plan,
                close_out_days=close_out_days,
                close_out_time=close_out_time,
                checkin_time=checkin_time,
            ).first()

            if existing_rateplan:
                serializer = self.get_serializer(existing_rateplan)
                return Response(serializer.data, status=status.HTTP_200_OK)

            return super().create(request, *args, **kwargs)

        except ValidationError as e:
            return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating rate plan: {str(e)}")
            return Response(
                {"error": "An error occurred while creating the rate plan"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class RoomRateViewSet(viewsets.ModelViewSet):
    serializer_class = RoomRateSerializer
    permission_classes = [IsAuthenticated, RoomRatesPermission]

    def get_queryset(self):
        property_id = self.request.query_params.get("property")
        user = self.request.user

        # For detail actions (PUT, DELETE, GET single item), don't require property_id
        if self.action in ["retrieve", "update", "partial_update", "destroy"]:
            return RoomRate.objects.filter(
                models.Q(room__property__staffs=user)
            ).distinct()

        if not property_id:
            raise ValidationError({"property": "Property ID is required"})

        try:
            uuid.UUID(str(property_id), version=4)
        except (ValueError, AttributeError, TypeError):
            logger.error(f"Invalid property ID format: {property_id}")
            raise ValidationError(
                {"property": "Invalid property ID format. Must be a valid UUID."}
            )

        return (
            RoomRate.objects.filter(room__property_id=property_id)
            .filter(models.Q(room__property__staffs=user))
            .distinct()
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context

    @action(detail=False, methods=["get"])
    def calendar(self, request):
        try:
            # Parameter validation
            property_id = request.query_params.get("property")
            year = request.query_params.get("year")
            month = request.query_params.get("month")

            errors = {}
            if not property_id:
                errors["property"] = "Property ID is required"
            if not year:
                errors["year"] = "Year is required"
            if not month:
                errors["month"] = "Month is required"

            if errors:
                raise ValidationError(errors)

            try:
                year = int(year)
                month = int(month)
                property_id = uuid.UUID(str(property_id), version=4)
            except (TypeError, ValueError):
                raise ValidationError(
                    {
                        "year": "Year and month must be valid integers",
                        "month": "Year and month must be valid integers",
                        "property": "Invalid property ID format",
                    }
                )

            if not (1 <= month <= 12):
                errors["month"] = "Month must be between 1 and 12"
            if not (2025 <= year <= 2100):
                errors["year"] = "Year must be between 2025 and 2100"
            if errors:
                raise ValidationError(errors)

            # Get property and verify access
            property = (
                Property.objects.filter(id=property_id, staffs=request.user)
                .prefetch_related("rooms")
                .first()
            )

            if not property:
                raise ValidationError(
                    {"property": "Property not found or you don't have access to it."}
                )

            # Check if user is cleaning staff
            is_cleaning_staff = cleaning_staff_check(
                property, request.user
            )  # Get all active rooms for the property with their default room rates (as whole numbers, including None)
            if is_cleaning_staff:
                default_rates = {
                    str(room.id): safe_rate_to_int_string(room.room_rate)
                    for room in property.rooms.filter()
                }
            else:
                default_rates = {
                    str(room.id): safe_rate_to_int_string(room.room_rate)
                    for room in property.rooms.filter()
                }

            # Get calendar range
            _, last_day = monthrange(year, month)
            start_date = date(year, month, 1)
            end_date = date(year, month, last_day)

            calendar_data = []
            if is_cleaning_staff:
                # For cleaning staff, just return room rates (as whole numbers, including None) for all dates
                for day in range(1, last_day + 1):
                    calendar_data.append(
                        {"date": date(year, month, day), "rates": default_rates}
                    )
            else:
                # Get all room rates for the property in one query (as whole numbers, including None)
                room_rates = (
                    RoomRate.objects.filter(
                        Q(end_date__gte=start_date) | Q(end_date__isnull=True),
                        room__property_id=property_id,
                        room__is_active=True,
                        start_date__lte=end_date,
                        is_active=True,
                    )
                    .select_related("room")
                    .order_by("created_at")
                    .values_list("room_id", "rate")
                )

                # Initialize calendar data
                for day in range(1, last_day + 1):
                    current_date = date(year, month, day)
                    rates_by_room = (
                        default_rates.copy()
                    )  # Override with any specific rates for this date
                    for room_id, rate in room_rates:
                        rates_by_room[str(room_id)] = safe_rate_to_int_string(rate)

                    calendar_data.append({"date": current_date, "rates": rates_by_room})

            serializer = CalendarRoomRateSerializer(
                calendar_data, many=True, context={"request": request}
            )
            return Response(serializer.data)

        except ValidationError as e:
            return Response(e.detail, status=400)
        except Exception as e:
            logger.error(f"Calendar API error: {str(e)}")
            raise ValidationError("An error occurred while processing your request")
