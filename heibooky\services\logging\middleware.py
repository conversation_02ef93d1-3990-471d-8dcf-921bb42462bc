import logging
import time

from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class RequestLogMiddleware(MiddlewareMixin):
    """
    Enhanced middleware for logging HTTP requests with timing and performance metrics.
    """

    def process_request(self, request):
        """Log incoming request and start timing."""
        request._start_time = time.time()

        # Log the incoming request
        logger.info(
            f"Request started: {request.method} {request.get_full_path()}",
            extra={
                "request": request,
                "event_type": "request_started",
                "request_size": len(request.body) if hasattr(request, "body") else 0,
            },
        )

    def process_response(self, request, response):
        """Log response with timing information."""
        if hasattr(request, "_start_time"):
            duration = time.time() - request._start_time

            # Determine log level based on response status and duration
            if response.status_code >= 500:
                log_level = logging.ERROR
            elif response.status_code >= 400:
                log_level = logging.WARNING
            elif duration > 2.0:  # Slow requests
                log_level = logging.WARNING
            else:
                log_level = logging.INFO

            # Log the response
            logger.log(
                log_level,
                f"Request completed: {request.method} {request.get_full_path()} "
                f"- Status: {response.status_code} - Duration: {duration:.3f}s",
                extra={
                    "request": request,
                    "event_type": "request_completed",
                    "status_code": response.status_code,
                    "duration": duration,
                    "response_size": (
                        len(response.content) if hasattr(response, "content") else 0
                    ),
                },
            )

        return response

    def process_exception(self, request, exception):
        """Log exceptions that occur during request processing."""
        duration = time.time() - getattr(request, "_start_time", time.time())

        logger.error(
            f"Request failed: {request.method} {request.get_full_path()} "
            f"- Exception: {exception.__class__.__name__}: {str(exception)} "
            f"- Duration: {duration:.3f}s",
            extra={
                "request": request,
                "event_type": "request_exception",
                "exception_type": exception.__class__.__name__,
                "exception_message": str(exception),
                "duration": duration,
            },
            exc_info=True,
        )


class SecurityLogMiddleware(MiddlewareMixin):
    """
    Middleware for logging security-related events.
    """

    def process_request(self, request):
        """Log potential security issues."""

        # Log suspicious requests
        suspicious_patterns = [
            "/admin/",
            "/wp-admin/",
            "/phpmyadmin/",
            ".php",
            "../",
            "script>",
            "SELECT * FROM",
        ]

        path = request.get_full_path().lower()
        user_agent = request.META.get("HTTP_USER_AGENT", "").lower()

        for pattern in suspicious_patterns:
            if pattern in path or pattern in user_agent:
                logger.warning(
                    f"Suspicious request detected: {request.method} {request.get_full_path()}",
                    extra={
                        "request": request,
                        "event_type": "suspicious_request",
                        "pattern_matched": pattern,
                        "security_level": "medium",
                    },
                )
                break

        # Log requests with no user agent (potential bots)
        if not request.META.get("HTTP_USER_AGENT"):
            logger.info(
                f"Request with no user agent: {request.method} {request.get_full_path()}",
                extra={
                    "request": request,
                    "event_type": "no_user_agent",
                    "security_level": "low",
                },
            )

    def process_response(self, request, response):
        """Log authentication and authorization events."""

        # Log failed authentication attempts
        if response.status_code == 401:
            logger.warning(
                f"Authentication failed: {request.method} {request.get_full_path()}",
                extra={
                    "request": request,
                    "event_type": "auth_failed",
                    "security_level": "medium",
                },
            )

        # Log access denied
        elif response.status_code == 403:
            logger.warning(
                f"Access denied: {request.method} {request.get_full_path()}",
                extra={
                    "request": request,
                    "event_type": "access_denied",
                    "security_level": "medium",
                },
            )

        return response


class PerformanceLogMiddleware(MiddlewareMixin):
    """
    Middleware for logging performance metrics.
    """

    def process_request(self, request):
        """Start performance monitoring."""
        import psutil

        process = psutil.Process()

        request._perf_start_time = time.time()
        request._perf_start_memory = process.memory_info().rss
        request._perf_start_cpu = process.cpu_percent()

    def process_response(self, request, response):
        """Log performance metrics."""
        if hasattr(request, "_perf_start_time"):
            import psutil

            process = psutil.Process()

            duration = time.time() - request._perf_start_time
            memory_usage = process.memory_info().rss - getattr(
                request, "_perf_start_memory", 0
            )

            # Log slow requests or high memory usage
            if duration > 1.0 or abs(memory_usage) > 50 * 1024 * 1024:  # 50MB threshold
                logger.warning(
                    f"Performance issue detected: {request.method} {request.get_full_path()} "
                    f"- Duration: {duration:.3f}s - Memory change: {memory_usage / 1024 / 1024:.2f}MB",
                    extra={
                        "request": request,
                        "event_type": "performance_issue",
                        "duration": duration,
                        "memory_usage": memory_usage,
                        "performance_level": "high" if duration > 5.0 else "medium",
                    },
                )

        return response
