#!/bin/bash

# Performance Monitoring Script for Heibooky Monitoring Stack
# This script provides performance analysis, optimization recommendations, and benchmarking

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROMETHEUS_URL="http://localhost:9090"
GRAFANA_URL="http://localhost:3000"
PERFORMANCE_LOG="$PROJECT_DIR/logs/performance.log"
BENCHMARK_RESULTS="$PROJECT_DIR/logs/benchmark_results.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$PERFORMANCE_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$PERFORMANCE_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$PERFORMANCE_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$PERFORMANCE_LOG"
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Function to check if services are running
check_services() {
    log "Checking monitoring services status..."
    
    local services=("prometheus" "grafana" "alertmanager" "loki")
    local all_running=true
    
    for service in "${services[@]}"; do
        if docker-compose -f "$PROJECT_DIR/docker-compose.yml" ps "$service" | grep -q "Up"; then
            success "$service is running"
        else
            error "$service is not running"
            all_running=false
        fi
    done
    
    if [ "$all_running" = false ]; then
        error "Some services are not running. Please start them first."
        exit 1
    fi
}

# Function to get Prometheus metrics
get_prometheus_metric() {
    local query="$1"
    local result
    
    result=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=$query" | jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0")
    echo "$result"
}

# Function to analyze query performance
analyze_query_performance() {
    log "Analyzing Prometheus query performance..."
    
    # Get query statistics
    local slow_queries
    slow_queries=$(curl -s "$PROMETHEUS_URL/api/v1/label/__name__/values" | jq -r '.data[]' | wc -l)
    
    log "Total metrics: $slow_queries"
    
    # Check for slow queries (>1s)
    local query_duration
    query_duration=$(get_prometheus_metric "prometheus_engine_query_duration_seconds{quantile=\"0.95\"}")
    
    if (( $(echo "$query_duration > 1.0" | bc -l) )); then
        warning "95th percentile query duration is ${query_duration}s (>1s threshold)"
        echo "  Recommendation: Consider using recording rules for complex queries"
    else
        success "Query performance is good (95th percentile: ${query_duration}s)"
    fi
    
    # Check memory usage
    local memory_usage
    memory_usage=$(get_prometheus_metric "prometheus_tsdb_head_series")
    
    log "Active time series: $memory_usage"
    
    if (( $(echo "$memory_usage > 1000000" | bc -l) )); then
        warning "High cardinality detected: $memory_usage series"
        echo "  Recommendation: Review metric labels and consider reducing cardinality"
    fi
}

# Function to analyze dashboard performance
analyze_dashboard_performance() {
    log "Analyzing Grafana dashboard performance..."
    
    # Check if Grafana is accessible
    if ! curl -s "$GRAFANA_URL/api/health" > /dev/null; then
        error "Cannot connect to Grafana at $GRAFANA_URL"
        return 1
    fi
    
    success "Grafana is accessible"
    
    # Simulate dashboard load test
    log "Running dashboard load test..."
    
    local start_time=$(date +%s)
    
    # Test dashboard loading (simulate multiple requests)
    for i in {1..10}; do
        curl -s "$GRAFANA_URL/api/health" > /dev/null
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ "$duration" -gt 5 ]; then
        warning "Dashboard load test took ${duration}s (>5s threshold)"
        echo "  Recommendation: Optimize dashboard queries and increase refresh intervals"
    else
        success "Dashboard performance is good (${duration}s for 10 requests)"
    fi
}

# Function to analyze resource usage
analyze_resource_usage() {
    log "Analyzing container resource usage..."
    
    # Get container stats
    local containers=("prometheus" "grafana" "alertmanager" "loki")
    
    for container in "${containers[@]}"; do
        local container_id
        container_id=$(docker-compose -f "$PROJECT_DIR/docker-compose.yml" ps -q "$container" 2>/dev/null || echo "")
        
        if [ -n "$container_id" ]; then
            local stats
            stats=$(docker stats --no-stream --format "{{.CPUPerc}},{{.MemUsage}}" "$container_id" 2>/dev/null || echo "0%,0B / 0B")
            
            local cpu_percent=$(echo "$stats" | cut -d',' -f1 | sed 's/%//')
            local memory_usage=$(echo "$stats" | cut -d',' -f2)
            
            log "$container: CPU: ${cpu_percent}%, Memory: $memory_usage"
            
            # Check for high resource usage
            if (( $(echo "$cpu_percent > 80" | bc -l) )); then
                warning "$container CPU usage is high: ${cpu_percent}%"
            fi
        else
            error "Container $container not found"
        fi
    done
}

# Function to run performance benchmark
run_benchmark() {
    log "Running performance benchmark..."
    
    local benchmark_start=$(date +%s)
    local results="{\"timestamp\": \"$(date -Iseconds)\", \"tests\": []}"
    
    # Test 1: Query response time
    log "Testing query response time..."
    local query_start=$(date +%s%3N)
    get_prometheus_metric "up" > /dev/null
    local query_end=$(date +%s%3N)
    local query_duration=$((query_end - query_start))
    
    results=$(echo "$results" | jq ".tests += [{\"name\": \"query_response_time\", \"duration_ms\": $query_duration, \"threshold_ms\": 100}]")
    
    if [ "$query_duration" -gt 100 ]; then
        warning "Query response time: ${query_duration}ms (>100ms threshold)"
    else
        success "Query response time: ${query_duration}ms"
    fi
    
    # Test 2: Dashboard API response time
    log "Testing dashboard API response time..."
    local api_start=$(date +%s%3N)
    curl -s "$GRAFANA_URL/api/health" > /dev/null
    local api_end=$(date +%s%3N)
    local api_duration=$((api_end - api_start))
    
    results=$(echo "$results" | jq ".tests += [{\"name\": \"dashboard_api_response_time\", \"duration_ms\": $api_duration, \"threshold_ms\": 200}]")
    
    if [ "$api_duration" -gt 200 ]; then
        warning "Dashboard API response time: ${api_duration}ms (>200ms threshold)"
    else
        success "Dashboard API response time: ${api_duration}ms"
    fi
    
    # Test 3: Metrics ingestion rate
    log "Testing metrics ingestion rate..."
    local ingestion_rate
    ingestion_rate=$(get_prometheus_metric "rate(prometheus_tsdb_head_samples_appended_total[1m])")
    
    results=$(echo "$results" | jq ".tests += [{\"name\": \"metrics_ingestion_rate\", \"samples_per_second\": $ingestion_rate, \"threshold\": 1000}]")
    
    if (( $(echo "$ingestion_rate < 1000" | bc -l) )); then
        warning "Low metrics ingestion rate: ${ingestion_rate} samples/sec"
    else
        success "Metrics ingestion rate: ${ingestion_rate} samples/sec"
    fi
    
    # Save benchmark results
    echo "$results" > "$BENCHMARK_RESULTS"
    
    local benchmark_end=$(date +%s)
    local total_duration=$((benchmark_end - benchmark_start))
    
    success "Benchmark completed in ${total_duration}s"
    log "Results saved to: $BENCHMARK_RESULTS"
}

# Function to generate performance report
generate_report() {
    log "Generating performance report..."
    
    local report_file="$PROJECT_DIR/logs/performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Heibooky Monitoring Stack Performance Report"
        echo "============================================="
        echo "Generated: $(date)"
        echo ""
        
        echo "Service Status:"
        echo "---------------"
        docker-compose -f "$PROJECT_DIR/docker-compose.yml" ps
        echo ""
        
        echo "Resource Usage:"
        echo "---------------"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" \
            $(docker-compose -f "$PROJECT_DIR/docker-compose.yml" ps -q 2>/dev/null)
        echo ""
        
        echo "Disk Usage:"
        echo "-----------"
        docker system df
        echo ""
        
        echo "Recent Performance Metrics:"
        echo "---------------------------"
        if [ -f "$BENCHMARK_RESULTS" ]; then
            cat "$BENCHMARK_RESULTS" | jq '.'
        else
            echo "No benchmark results available. Run 'performance-monitor.sh benchmark' first."
        fi
        
    } > "$report_file"
    
    success "Performance report generated: $report_file"
}

# Function to provide optimization recommendations
optimize_recommendations() {
    log "Analyzing system and providing optimization recommendations..."
    
    echo ""
    echo "Performance Optimization Recommendations:"
    echo "========================================="
    
    # Check Prometheus configuration
    if [ -f "$PROJECT_DIR/monitoring/prometheus/prometheus.yml" ]; then
        local scrape_interval
        scrape_interval=$(grep "scrape_interval:" "$PROJECT_DIR/monitoring/prometheus/prometheus.yml" | head -1 | awk '{print $2}')
        
        if [ "$scrape_interval" = "15s" ] || [ "$scrape_interval" = "10s" ]; then
            echo "✓ Prometheus scrape interval is optimized ($scrape_interval)"
        else
            echo "⚠ Consider optimizing Prometheus scrape interval (current: $scrape_interval)"
            echo "  Recommendation: Use 15s for most metrics, 10s for critical application metrics"
        fi
    fi
    
    # Check recording rules
    if [ -f "$PROJECT_DIR/monitoring/prometheus/recording_rules.yml" ]; then
        local rule_count
        rule_count=$(grep -c "record:" "$PROJECT_DIR/monitoring/prometheus/recording_rules.yml" || echo "0")
        
        if [ "$rule_count" -gt 20 ]; then
            echo "✓ Good number of recording rules defined ($rule_count)"
        else
            echo "⚠ Consider adding more recording rules for dashboard performance"
            echo "  Recommendation: Pre-compute frequently used queries"
        fi
    fi
    
    # Check Grafana configuration
    if [ -f "$PROJECT_DIR/monitoring/grafana/grafana.ini" ]; then
        if grep -q "cache_mode = shared" "$PROJECT_DIR/monitoring/grafana/grafana.ini"; then
            echo "✓ Grafana database caching is optimized"
        else
            echo "⚠ Enable Grafana database caching for better performance"
            echo "  Recommendation: Set cache_mode = shared in grafana.ini"
        fi
    fi
    
    echo ""
    echo "General Recommendations:"
    echo "- Use recording rules for complex dashboard queries"
    echo "- Set appropriate refresh intervals on dashboards (30s minimum)"
    echo "- Monitor cardinality growth and optimize labels"
    echo "- Implement proper retention policies"
    echo "- Regular performance testing and monitoring"
    echo "- Consider horizontal scaling for high-load environments"
}

# Main function
main() {
    case "${1:-help}" in
        "analyze")
            check_services
            analyze_query_performance
            analyze_dashboard_performance
            analyze_resource_usage
            ;;
        "benchmark")
            check_services
            run_benchmark
            ;;
        "report")
            generate_report
            ;;
        "optimize")
            optimize_recommendations
            ;;
        "all")
            check_services
            analyze_query_performance
            analyze_dashboard_performance
            analyze_resource_usage
            run_benchmark
            generate_report
            optimize_recommendations
            ;;
        "help"|*)
            echo "Heibooky Monitoring Performance Monitor"
            echo ""
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  analyze    - Analyze current performance metrics"
            echo "  benchmark  - Run performance benchmark tests"
            echo "  report     - Generate comprehensive performance report"
            echo "  optimize   - Provide optimization recommendations"
            echo "  all        - Run all performance analysis tasks"
            echo "  help       - Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 analyze     # Analyze current performance"
            echo "  $0 benchmark   # Run benchmark tests"
            echo "  $0 all         # Complete performance analysis"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
