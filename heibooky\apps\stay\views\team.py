import logging
import uuid
from datetime import timedelta

from apps.stay.models import (
    Property,
    PropertyOwnership,
    PropertyPermission,
    StaffRole,
    TeamInvite,
)
from apps.stay.serializers import TeamInviteSerializer
from apps.stay.tasks import send_invitation_email
from apps.stay.utils import require_property_ownership
from django.db import transaction
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

logger = logging.getLogger(__name__)


class TeamViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def validate_uuid(self, uuid_string, field_name):
        """Validate UUID format"""
        try:
            return uuid.UUID(str(uuid_string), version=4)
        except (ValueError, AttributeError, TypeError):
            raise ValidationError(
                {"error": f"Invalid {field_name} format. Must be a valid UUID."}
            )

    def validate_permissions(self, request_permissions):
        """Validate permissions and return queryset"""
        if not request_permissions or not isinstance(request_permissions, list):
            raise ValidationError({"permissions": "Must be a non-empty list."})

        valid_permissions = set(
            PropertyPermission.objects.values_list("name", flat=True)
        )
        invalid_permissions = set(request_permissions) - valid_permissions

        if invalid_permissions:
            raise ValidationError(
                {
                    "permissions": f"Invalid permissions: {', '.join(invalid_permissions)}"
                }
            )

        # Check cleaning_staff permission combination
        if "cleaning_staff" in request_permissions and len(request_permissions) > 1:
            raise ValidationError(
                {
                    "permissions": "'cleaning_staff' permission cannot be combined with other permissions."
                }
            )

        return PropertyPermission.objects.filter(name__in=request_permissions)

    @require_property_ownership()
    def create(self, request):
        """Send an invitation with specified permissions"""
        try:
            property_id = request.data.get("property_id")
            validated_property_uuid = self.validate_uuid(property_id, "property_id")

            with transaction.atomic():
                property_instance = Property.objects.get(id=validated_property_uuid)
                email = request.data.get("email", "").lower()  # Convert to lowercase
                permissions = request.data.get("permissions", [])

                if not email:
                    raise ValidationError({"email": "This field is required."})

                # Check if user is already a staff member
                if property_instance.staffs.filter(email=email).exists():
                    raise ValidationError(
                        {"email": "This user is already a staff member."}
                    )

                permission_objects = self.validate_permissions(permissions)

                # Check for existing active invite
                existing_invite = TeamInvite.objects.filter(
                    property=property_instance,
                    email=email,
                ).first()

                if existing_invite:
                    if (
                        not existing_invite.accepted
                        and existing_invite.expires_at > timezone.now()
                    ):
                        raise ValidationError(
                            {
                                "email": "An active invitation already exists for this email."
                            }
                        )
                    else:
                        existing_invite.delete()

                invite = TeamInvite.objects.create(
                    property=property_instance,
                    email=email,
                    invited_by=request.user,
                    expires_at=timezone.now() + timedelta(hours=72),
                )
                invite.permissions.set(permission_objects)
                invite.save()

                # Send invitation email
                send_invitation_email.delay(str(invite.id))

                logger.info(f"Team invite created for {email} by {request.user.email}")
                return Response(
                    TeamInviteSerializer(invite).data, status=status.HTTP_201_CREATED
                )

        except Property.DoesNotExist:
            return Response(
                {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating team invite: {str(e)}")
            return Response(
                {"error": "Failed to create invite"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["post"])
    def accept_invite(self, request):
        """Accept an invitation and create staff role with permissions"""
        try:
            invite_id = request.data.get("id")
            validated_invite_uuid = self.validate_uuid(invite_id, "invite_id")

            with transaction.atomic():
                invite = TeamInvite.objects.select_related("property").get(
                    id=validated_invite_uuid
                )

                from apps.stay.utils import approve_invite

                if approve_invite(invite, request.user):
                    return Response(
                        {"message": "Invite accepted successfully"},
                        status=status.HTTP_200_OK,
                    )

        except TeamInvite.DoesNotExist:
            return Response(
                {"error": "Invalid invite"}, status=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error accepting team invite: {str(e)}")
            return Response(
                {"error": "Failed to accept invite"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @require_property_ownership()
    @action(detail=False, methods=["get"])
    def property_staff(self, request):
        """Get all staff members for a property with their roles and permissions"""
        try:
            property_id = request.query_params.get("property_id")
            validated_property_uuid = self.validate_uuid(property_id, "property_id")

            property_instance = Property.objects.get(id=validated_property_uuid)
            staff_roles = StaffRole.objects.filter(
                property=property_instance
            ).select_related("user")

            # Get property owner
            owner_relationship = (
                PropertyOwnership.objects.filter(property=property_instance)
                .select_related("user")
                .first()
            )

            staff_data = []

            # Add owner first if exists
            if owner_relationship:
                staff_data.append(
                    {
                        "user_id": owner_relationship.user.id,
                        "email": owner_relationship.user.email,
                        "name": f"{owner_relationship.user.name}",
                        "role": "owner",
                        "permissions": ["owner"],
                        "created_at": owner_relationship.created_at,
                    }
                )

            # Add other staff members
            for role in staff_roles:
                if role.user.id != owner_relationship.user.id:  # Skip if user is owner
                    staff_data.append(
                        {
                            "user_id": role.user.id,
                            "email": role.user.email,
                            "name": f"{role.user.name}",
                            "role": "staff",
                            "permissions": list(
                                role.permissions.values_list("name", flat=True)
                            ),
                            "created_at": role.created_at,
                        }
                    )

            return Response(staff_data, status=status.HTTP_200_OK)

        except Property.DoesNotExist:
            return Response(
                {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error fetching property staff: {str(e)}")
            return Response(
                {"error": "Failed to fetch staff members"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @require_property_ownership()
    @action(detail=False, methods=["put"])
    def update_permissions(self, request):
        """Update permissions for an existing staff member"""
        try:
            property_id = request.data.get("property_id")
            user_id = request.data.get("user_id")

            validated_property_uuid = self.validate_uuid(property_id, "property_id")
            validated_user_uuid = self.validate_uuid(user_id, "user_id")

            property_instance = Property.objects.get(id=validated_property_uuid)
            staff_role = StaffRole.objects.get(
                property=property_instance, user_id=validated_user_uuid
            )

            # Validate and update permissions
            permissions = request.data.get("permissions", [])
            permission_objects = self.validate_permissions(permissions)
            staff_role.permissions.set(permission_objects)

            return Response(
                {"message": "Permissions updated successfully"},
                status=status.HTTP_200_OK,
            )
        except Property.DoesNotExist:
            return Response(
                {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except StaffRole.DoesNotExist:
            return Response(
                {"error": "Staff member not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error updating permissions: {str(e)}")
            return Response(
                {"error": "Failed to update permissions"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @require_property_ownership()
    @action(detail=False, methods=["delete"])
    def remove_staff(self, request):
        """Remove a staff member from the property"""
        try:
            property_id = request.data.get("property_id")
            user_id = request.data.get("user_id")

            validated_property_uuid = self.validate_uuid(property_id, "property_id")
            validated_user_uuid = self.validate_uuid(user_id, "user_id")

            property_instance = Property.objects.get(id=validated_property_uuid)
            staff_role = StaffRole.objects.get(
                property=property_instance, user_id=validated_user_uuid
            )

            # Remove user from property staff
            property_instance.staffs.remove(staff_role.user)
            staff_role.delete()

            return Response(
                {"message": "Staff member removed successfully"},
                status=status.HTTP_200_OK,
            )
        except Property.DoesNotExist:
            return Response(
                {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except StaffRole.DoesNotExist:
            return Response(
                {"error": "Staff member not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error removing staff member: {str(e)}")
            return Response(
                {"error": "Failed to remove staff member"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @require_property_ownership()
    @action(detail=False, methods=["get"])
    def property_invites(self, request):
        """Get all active invites for a property"""
        try:
            property_id = request.query_params.get("property_id")
            validated_property_uuid = self.validate_uuid(property_id, "property_id")

            property_instance = Property.objects.get(id=validated_property_uuid)
            invites = TeamInvite.objects.filter(
                property=property_instance,
                accepted=False,
                expires_at__gt=timezone.now(),
            ).select_related("invited_by")

            invite_data = [
                {
                    "id": invite.id,
                    "email": invite.email,
                    "invited_by": invite.invited_by.email,
                    "created_at": invite.created_at,
                    "expires_at": invite.expires_at,
                    "permissions": list(
                        invite.permissions.values_list("name", flat=True)
                    ),
                }
                for invite in invites
            ]

            return Response(invite_data, status=status.HTTP_200_OK)

        except Property.DoesNotExist:
            return Response(
                {"error": "Property not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error fetching property invites: {str(e)}")
            return Response(
                {"error": "Failed to fetch invites"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class VerifyTeamInviteView(APIView):
    permission_classes = []

    def validate_uuid(self, uuid_string, field_name):
        """Validate UUID format"""
        try:
            return uuid.UUID(str(uuid_string), version=4)
        except (ValueError, AttributeError, TypeError):
            raise ValidationError(
                {"error": f"Invalid {field_name} format. Must be a valid UUID."}
            )

    def get(self, request):
        """Verify if an invite is valid and not expired"""
        invite_id = request.query_params.get("id")
        if not invite_id:
            return Response(
                {"error": "Invite ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            validated_invite_uuid = self.validate_uuid(invite_id, "invite_id")

            invite = TeamInvite.objects.get(id=validated_invite_uuid)

            if invite.accepted:
                return Response(
                    {"error": "Invite has already been accepted"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if invite.is_expired():
                return Response(
                    {"error": "Invite has expired"}, status=status.HTTP_400_BAD_REQUEST
                )

            serializer = TeamInviteSerializer(invite)
            return Response(serializer.data)

        except ValidationError as e:
            return Response({"errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except TeamInvite.DoesNotExist:
            return Response(
                {"error": "Invalid invite"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error verifying team invite: {str(e)}")
            return Response(
                {"error": "Failed to verify invite"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
