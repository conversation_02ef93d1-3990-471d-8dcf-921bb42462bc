#!/bin/bash

# Monitoring Dashboard Script for Heibooky
# This script provides a real-time dashboard of application health and metrics

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
REFRESH_INTERVAL=5
BASE_URL="${BASE_URL:-http://localhost}"

# Clear screen function
clear_screen() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                           HEIBOOKY MONITORING DASHBOARD                      ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo -e "${BLUE}Base URL: $BASE_URL${NC}"
    echo -e "${BLUE}Refresh: ${REFRESH_INTERVAL}s${NC}"
    echo -e "${BLUE}Time: $(date)${NC}"
    echo
}

# Get health status
get_health_status() {
    local endpoint="$1"
    local response=$(curl -s --connect-timeout 5 "$BASE_URL$endpoint" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$response" | jq . >/dev/null 2>&1; then
        echo "$response"
    else
        echo '{"status": "error", "error": "Connection failed or invalid JSON"}'
    fi
}

# Display health check section
display_health_checks() {
    echo -e "${YELLOW}═══ HEALTH CHECKS ═══${NC}"
    
    # Root health check
    local health=$(get_health_status "/health")
    local status=$(echo "$health" | jq -r '.status // "unknown"')
    
    case $status in
        "healthy")
            echo -e "Root Health:      ${GREEN}✅ HEALTHY${NC}"
            ;;
        "degraded")
            echo -e "Root Health:      ${YELLOW}⚠️  DEGRADED${NC}"
            ;;
        "unhealthy")
            echo -e "Root Health:      ${RED}❌ UNHEALTHY${NC}"
            ;;
        *)
            echo -e "Root Health:      ${RED}❌ ERROR${NC}"
            ;;
    esac
    
    # Individual checks
    if echo "$health" | jq -e '.checks' >/dev/null 2>&1; then
        echo "$health" | jq -r '.checks | to_entries[] | "\(.key): \(.value)"' | while read -r line; do
            local check_name=$(echo "$line" | cut -d: -f1)
            local check_status=$(echo "$line" | cut -d: -f2- | xargs)
            
            if [[ "$check_status" == "healthy" ]]; then
                echo -e "  $check_name: ${GREEN}✅ HEALTHY${NC}"
            else
                echo -e "  $check_name: ${RED}❌ $check_status${NC}"
            fi
        done
    fi
    
    echo
}

# Display container status
display_container_status() {
    echo -e "${YELLOW}═══ CONTAINER STATUS ═══${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ -f "docker-compose.prod.yml" ]; then
            docker-compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.State}}\t{{.Status}}" | while read -r line; do
                if [[ "$line" == *"Up"* ]]; then
                    echo -e "${GREEN}$line${NC}"
                elif [[ "$line" == *"Exit"* ]] || [[ "$line" == *"Down"* ]]; then
                    echo -e "${RED}$line${NC}"
                else
                    echo -e "${BLUE}$line${NC}"
                fi
            done
        else
            echo -e "${RED}docker-compose.prod.yml not found${NC}"
        fi
    else
        echo -e "${RED}docker-compose not available${NC}"
    fi
    
    echo
}

# Display system metrics
display_system_metrics() {
    echo -e "${YELLOW}═══ SYSTEM METRICS ═══${NC}"
    
    # CPU and Memory
    if command -v top &> /dev/null; then
        local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
        local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        
        echo -e "CPU Usage:        ${BLUE}${cpu_usage}%${NC}"
        echo -e "Memory Usage:     ${BLUE}${mem_usage}%${NC}"
    fi
    
    # Disk usage
    if command -v df &> /dev/null; then
        local disk_usage=$(df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1)
        echo -e "Disk Usage:       ${BLUE}${disk_usage}%${NC}"
    fi
    
    # Load average
    if [ -f /proc/loadavg ]; then
        local load_avg=$(cat /proc/loadavg | awk '{print $1, $2, $3}')
        echo -e "Load Average:     ${BLUE}$load_avg${NC}"
    fi
    
    echo
}

# Display application logs (last few lines)
display_recent_logs() {
    echo -e "${YELLOW}═══ RECENT LOGS ═══${NC}"
    
    if command -v docker-compose &> /dev/null && [ -f "docker-compose.prod.yml" ]; then
        # Show last 5 lines from web container
        echo -e "${CYAN}Web Container:${NC}"
        docker-compose -f docker-compose.prod.yml logs --tail=3 web 2>/dev/null | tail -3
        
        echo
        
        # Show last 5 lines from nginx container
        echo -e "${CYAN}Nginx Container:${NC}"
        docker-compose -f docker-compose.prod.yml logs --tail=3 nginx 2>/dev/null | tail -3
    else
        echo -e "${RED}Cannot access container logs${NC}"
    fi
    
    echo
}

# Display metrics summary
display_metrics_summary() {
    echo -e "${YELLOW}═══ METRICS SUMMARY ═══${NC}"
    
    local metrics_debug=$(get_health_status "/monitoring/metrics/debug")
    
    if echo "$metrics_debug" | jq -e '.health' >/dev/null 2>&1; then
        local collection_status=$(echo "$metrics_debug" | jq -r '.collection_status // "unknown"')
        echo -e "Collection Status: ${GREEN}$collection_status${NC}"
        
        # Show timestamp
        local timestamp=$(echo "$metrics_debug" | jq -r '.health.timestamp // "unknown"')
        if [ "$timestamp" != "unknown" ] && [ "$timestamp" != "null" ]; then
            local readable_time=$(date -d "@$timestamp" 2>/dev/null || echo "Invalid timestamp")
            echo -e "Last Update:      ${BLUE}$readable_time${NC}"
        fi
    else
        echo -e "Collection Status: ${RED}ERROR${NC}"
    fi
    
    echo
}

# Main dashboard loop
main() {
    # Check dependencies
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}Error: jq is required but not installed${NC}"
        echo "Please install jq: sudo apt-get install jq"
        exit 1
    fi
    
    # Main loop
    while true; do
        clear_screen
        display_health_checks
        display_container_status
        display_system_metrics
        display_recent_logs
        display_metrics_summary
        
        echo -e "${CYAN}Press Ctrl+C to exit${NC}"
        sleep $REFRESH_INTERVAL
    done
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --url)
            BASE_URL="$2"
            shift 2
            ;;
        --interval)
            REFRESH_INTERVAL="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --url URL          Base URL to monitor (default: http://localhost)"
            echo "  --interval SECONDS Refresh interval in seconds (default: 5)"
            echo "  --help             Show this help message"
            echo
            echo "Examples:"
            echo "  $0"
            echo "  $0 --url https://backend.heibooky.com --interval 10"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

# Run main function
main
