#!/bin/bash
set -e

# SSL Certificate Initialization Script for Heibooky
# This script sets up Let's Encrypt SSL certificates for production deployment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DOMAIN="${CERTBOT_DOMAIN:-backend.heibooky.com}"
EMAIL="${CERTBOT_EMAIL:-}"
STAGING="${CERTBOT_STAGING:-0}"
RSA_KEY_SIZE=4096
DATA_PATH="./certbot"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
        exit 1
    fi
    
    if [ ! -f "docker-compose.prod.yml" ]; then
        error "docker-compose.prod.yml not found"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Create necessary directories
setup_directories() {
    log "Setting up SSL directories..."
    
    mkdir -p "$DATA_PATH/conf"
    mkdir -p "$DATA_PATH/www"
    mkdir -p "$DATA_PATH/logs"
    mkdir -p "nginx/logs"
    
    log "Directories created successfully"
}

# Download recommended TLS parameters
download_tls_params() {
    log "Downloading recommended TLS parameters..."
    
    if [ ! -e "$DATA_PATH/conf/options-ssl-nginx.conf" ] || [ ! -e "$DATA_PATH/conf/ssl-dhparams.pem" ]; then
        info "Downloading options-ssl-nginx.conf..."
        curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot-nginx/certbot_nginx/_internal/tls_configs/options-ssl-nginx.conf > "$DATA_PATH/conf/options-ssl-nginx.conf"
        
        info "Downloading ssl-dhparams.pem..."
        curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot/certbot/ssl-dhparams.pem > "$DATA_PATH/conf/ssl-dhparams.pem"
        
        log "TLS parameters downloaded successfully"
    else
        info "TLS parameters already exist, skipping download"
    fi
}

# Create dummy certificate for initial nginx startup
create_dummy_certificate() {
    log "Creating dummy certificate for $DOMAIN..."
    
    local cert_path="/etc/letsencrypt/live/$DOMAIN"
    mkdir -p "$DATA_PATH/conf/live/$DOMAIN"
    
    # Create dummy certificate
    docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
        openssl req -x509 -nodes -newkey rsa:$RSA_KEY_SIZE -days 1 \
        -keyout '$cert_path/privkey.pem' \
        -out '$cert_path/fullchain.pem' \
        -subj '/CN=localhost'" certbot
    
    log "Dummy certificate created successfully"
}

# Start nginx with dummy certificate
start_nginx() {
    log "Starting nginx with dummy certificate..."
    
    # Use the production nginx configuration
    cp nginx/nginx.prod.conf nginx/nginx.conf
    
    # Start nginx service only
    docker-compose -f docker-compose.prod.yml up -d nginx
    
    # Wait for nginx to be ready
    # Wait for nginx to be ready with retries
    local max_attempts=30
    local attempt=0

    while [ $attempt -lt $max_attempts ]; do
        if docker-compose -f docker-compose.prod.yml ps nginx | grep -q "Up"; then
            log "Nginx is ready"
            break
        fi

        if [ $attempt -eq $((max_attempts - 1)) ]; then
            error "Nginx failed to start after $max_attempts attempts"
            docker-compose -f docker-compose.prod.yml logs nginx
            exit 1
        fi

        attempt=$((attempt + 1))
        info "Waiting for nginx to start... (attempt $attempt/$max_attempts)"
        sleep 2
    done
    log "Nginx started successfully"
}

# Remove dummy certificate
remove_dummy_certificate() {
    log "Removing dummy certificate for $DOMAIN..."
    
    docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
        rm -Rf /etc/letsencrypt/live/$DOMAIN && \
        rm -Rf /etc/letsencrypt/archive/$DOMAIN && \
        rm -Rf /etc/letsencrypt/renewal/$DOMAIN.conf" certbot
    
    log "Dummy certificate removed successfully"
}

# Request real Let's Encrypt certificate
request_certificate() {
    log "Requesting Let's Encrypt certificate for $DOMAIN..."
    
    # Prepare email argument
    local email_arg=""
    case "$EMAIL" in
        "") email_arg="--register-unsafely-without-email" ;;
        *) email_arg="--email $EMAIL" ;;
    esac
    
    # Prepare staging argument
    local staging_arg=""
    if [ $STAGING != "0" ]; then 
        staging_arg="--staging"
        warn "Using Let's Encrypt staging environment"
    fi
    
    # Request certificate
    docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
        certbot certonly --webroot -w /var/www/certbot \
        $staging_arg \
        $email_arg \
        -d $DOMAIN \
        --rsa-key-size $RSA_KEY_SIZE \
        --agree-tos \
        --force-renewal \
        --non-interactive" certbot
    
    if [ $? -eq 0 ]; then
        log "Certificate obtained successfully"
    else
        error "Failed to obtain certificate"
        docker-compose -f docker-compose.prod.yml logs certbot
        exit 1
    fi
}

# Reload nginx with real certificate
reload_nginx() {
    log "Reloading nginx with real certificate..."
    
    # Test nginx configuration
    if docker-compose -f docker-compose.prod.yml exec nginx nginx -t; then
        # Reload nginx
        docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload
        log "Nginx reloaded successfully"
    else
        error "Nginx configuration test failed"
        docker-compose -f docker-compose.prod.yml logs nginx
        exit 1
    fi
}

# Main execution
main() {
    log "=== SSL Certificate Initialization Started ==="
    log "Domain: $DOMAIN"
    log "Email: $EMAIL"
    log "Staging: $STAGING"
    
    # Change to project directory
    cd "$PROJECT_DIR"
    
    # Check if certificate already exists
    if [ -d "$DATA_PATH/conf/live/$DOMAIN" ]; then
        warn "Existing certificate found for $DOMAIN"
        read -p "Continue and replace existing certificate? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "SSL initialization cancelled"
            exit 0
        fi
    fi
    
    # Execute initialization steps
    check_prerequisites
    setup_directories
    download_tls_params
    create_dummy_certificate
    start_nginx
    remove_dummy_certificate
    request_certificate
    reload_nginx
    verify_certificate
    
    log "=== SSL Certificate Initialization Completed Successfully ==="
    log "Your domain $DOMAIN should now be accessible via HTTPS"
    log "Certificate will auto-renew. Set up a cron job to run: docker-compose -f docker-compose.prod.yml run --rm certbot renew"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --email)
            EMAIL="$2"
            shift 2
            ;;
        --staging)
            STAGING=1
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --domain DOMAIN    Domain name for SSL certificate (default: backend.heibooky.com)"
            echo "  --email EMAIL      Email for Let's Encrypt registration (default: <EMAIL>)"
            echo "  --staging          Use Let's Encrypt staging environment for testing"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
