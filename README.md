# Heibooky

Heibooky is a Django-based web application that integrates with Airbnb and Booking.com to automate property creation, booking management, and invoicing. The application calculates commissions, VAT, and other fees for each booking and generates invoices for both tourists and property owners.

## Features

- **User Management**: Custom user model with registration, login, and profile management.
- **Booking Management**: Monitors bookings from different OTAs including Airbnb and Booking using SU-API, calculates commissions, VAT, and payout.
- **Invoice Generation**: Automates invoice creation for property owners payments.
- **API Integrations**: Integrates with SU API for property  management and Stripe for payment 

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/heibooky.git
   cd heibooky
   ```

2. Install the required packages:

   ```bash
   pip install -r requirements.txt
   ```

3. Run migrations:

   ```bash
   python manage.py migrate
   ```

4. Create a superuser:

   ```bash
   python manage.py createsuperuser
   ```

5. Start the development server:

   ```bash
   python manage.py runserver
   ```

## Usage

- Access the admin panel at `http://localhost:8000/admin/` to manage users and bookings.
- The API endpoints for booking management, invoice generation, and user authentication are available via RESTful APIs.
