setup:
  project: heibooky-backend
  config:
    development: dev
    staging: stg
    production: prd

# Environment-specific configurations
environments:
  development:
    project: heibooky-backend
    config: dev
    
  staging:
    project: heibooky-backend
    config: stg
    
  production:
    project: heibooky-backend
    config: prd

# Default environment for local development
default_environment: development

# CLI configuration
cli:
  # Automatically install CLI if not present
  auto_install: true
  
  # Update check frequency (in hours)
  update_check_frequency: 24
  
  # Default output format (json, table, csv)
  output_format: table

# Service token configuration for CI/CD
service_tokens:
  # GitHub Actions will use these service tokens
  github_actions:
    development: DOPPLER_TOKEN_DEV
    staging: DOPPLER_TOKEN_STG
    production: DOPPLER_TOKEN_PRD

# Enclave configuration for secure secret injection
enclave:
  # Enable enclave for production deployments
  enabled: true
  
  # Enclave configuration for different environments
  configs:
    development:
      enabled: false
    staging:
      enabled: true
    production:
      enabled: true

# Secret synchronization settings
sync:
  # Automatically sync secrets on config changes
  auto_sync: true
  
  # Sync targets (can be AWS Secrets Manager, etc.)
  targets:
    aws_secrets_manager:
      enabled: false
      region: us-east-1
    
    github_actions:
      enabled: true
      repository: Heibooky/BackTrack

# Logging configuration
logging:
  level: info
  format: json
  
# Cache configuration
cache:
  # Cache duration for secrets (in seconds)
  ttl: 300
  
  # Cache location
  directory: ~/.doppler/cache
