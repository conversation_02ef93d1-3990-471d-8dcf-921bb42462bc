import logging

from apps.users.models import UserProfile
from django.core.management.base import BaseCommand

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Updates UserProfile has_billing_profile and is_customer flags based on existing records"

    def handle(self, *args, **options):
        self.stdout.write("Starting UserProfile flags update...")

        # Get all user profiles
        profiles = UserProfile.objects.all()
        total_profiles = profiles.count()
        updated_billing = 0
        updated_customer = 0

        for profile in profiles:
            user = profile.user

            # Check for BillingProfile
            has_billing = False
            try:
                if hasattr(user, "billingprofile"):
                    has_billing = True
            except Exception as e:
                logger.error(
                    f"Error checking billing profile for user {user.id}: {str(e)}"
                )

            # Check for StripeCustomer
            is_customer = False
            try:
                if hasattr(user, "stripe_customer"):
                    is_customer = True
            except Exception as e:
                logger.error(
                    f"Error checking stripe customer for user {user.id}: {str(e)}"
                )

            # Update profile if needed
            update_fields = []
            if profile.has_billing_profile != has_billing:
                profile.has_billing_profile = has_billing
                update_fields.append("has_billing_profile")
                updated_billing += 1

            if profile.is_customer != is_customer:
                profile.is_customer = is_customer
                update_fields.append("is_customer")
                updated_customer += 1

            if update_fields:
                profile.save(update_fields=update_fields)

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully updated {total_profiles} profiles: "
                f"{updated_billing} billing profiles, {updated_customer} customer profiles"
            )
        )
