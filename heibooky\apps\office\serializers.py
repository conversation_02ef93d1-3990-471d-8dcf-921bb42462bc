from apps.stay.models import Property
from rest_framework import serializers


class PropertyStatisticsSerializer(serializers.ModelSerializer):
    total_bookings = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    average_stay_duration = serializers.FloatField()
    occupancy_rate = serializers.FloatField()
    average_daily_rate = serializers.DecimalField(max_digits=10, decimal_places=2)
    revenue_per_available_room = serializers.DecimalField(
        max_digits=10, decimal_places=2
    )

    class Meta:
        model = Property
        fields = [
            "id",
            "name",
            "total_bookings",
            "total_revenue",
            "average_stay_duration",
            "occupancy_rate",
            "average_daily_rate",
            "revenue_per_available_room",
        ]
