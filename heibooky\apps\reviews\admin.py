from django.contrib import admin

from .models import Review


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = (
        "property",
        "reviewer_role",
        "submitted_at",
        "channel_id",
        "is_hidden",
    )
    list_filter = (
        "reviewer_role",
        "channel_id",
        "is_hidden",
        "submitted_at",
    )
    search_fields = (
        "property__name",
        "public_review",
        "private_feedback",
        "reviewer_id",
        "external_review_id",
    )
    readonly_fields = (
        "id",
        "created_at",
        "updated_at",
        "responses",
    )
    fieldsets = (
        (
            "Review Information",
            {
                "fields": (
                    "id",
                    "external_review_id",
                    "property",
                    "booking",
                    "listing_id",
                )
            },
        ),
        (
            "Review Content",
            {
                "fields": (
                    "public_review",
                    "private_feedback",
                    "ratings",
                    "responses",
                )
            },
        ),
        (
            "Reviewer Details",
            {
                "fields": (
                    "reviewer_id",
                    "reviewer_role",
                    "reviewee_id",
                    "reviewee_role",
                )
            },
        ),
        (
            "Metadata",
            {
                "fields": (
                    "channel_id",
                    "thread_id",
                    "is_hidden",
                    "is_read",
                    "submitted_at",
                    "expires_at",
                    "created_at",
                    "updated_at",
                )
            },
        ),
    )
