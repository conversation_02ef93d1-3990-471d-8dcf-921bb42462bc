#!/bin/bash

# Heibooky Monitoring Stack Management Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEIBOOKY MONITORING]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start monitoring stack
start_monitoring() {
    print_header "Starting Heibooky Monitoring Stack..."
    
    check_docker
    
    cd "$PROJECT_DIR"
    
    # Start only monitoring services
    print_status "Starting Prometheus..."
    docker-compose up -d prometheus
    
    print_status "Starting Grafana..."
    docker-compose up -d grafana
    
    print_status "Starting Alertmanager..."
    docker-compose up -d alertmanager
    
    print_status "Starting exporters..."
    docker-compose up -d redis-exporter node-exporter cadvisor
    
    print_status "Starting log aggregation..."
    docker-compose up -d loki promtail
    
    echo ""
    print_header "Monitoring Stack Started Successfully!"
    echo ""
    print_status "Service URLs:"
    echo "  • Grafana: http://localhost:3000 (admin/admin123)"
    echo "  • Prometheus: http://localhost:9090"
    echo "  • Alertmanager: http://localhost:9093"
    echo "  • cAdvisor: http://localhost:8080"
    echo ""
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    check_services
}

# Function to stop monitoring stack
stop_monitoring() {
    print_header "Stopping Heibooky Monitoring Stack..."
    
    cd "$PROJECT_DIR"
    
    docker-compose stop prometheus grafana alertmanager redis-exporter node-exporter cadvisor loki promtail
    
    print_status "Monitoring stack stopped."
}

# Function to restart monitoring stack
restart_monitoring() {
    print_header "Restarting Heibooky Monitoring Stack..."
    stop_monitoring
    sleep 5
    start_monitoring
}

# Function to check service status
check_services() {
    print_header "Checking Service Status..."
    
    services=("prometheus:9090" "grafana:3000" "alertmanager:9093" "loki:3100")
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if curl -s "http://localhost:$port" > /dev/null 2>&1; then
            print_status "$name is running ✓"
        else
            print_warning "$name is not responding on port $port"
        fi
    done
}

# Function to view logs
view_logs() {
    local service=${1:-"all"}
    
    print_header "Viewing logs for: $service"
    
    cd "$PROJECT_DIR"
    
    if [ "$service" = "all" ]; then
        docker-compose logs -f prometheus grafana alertmanager
    else
        docker-compose logs -f "$service"
    fi
}

# Function to backup monitoring data
backup_data() {
    print_header "Backing up monitoring data..."
    
    local backup_dir="backups/monitoring/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    cd "$PROJECT_DIR"
    
    # Backup Grafana data
    print_status "Backing up Grafana data..."
    docker run --rm -v "$(pwd)_grafana_data:/data" -v "$(pwd)/$backup_dir:/backup" alpine tar czf /backup/grafana_data.tar.gz -C /data .
    
    # Backup Prometheus data
    print_status "Backing up Prometheus data..."
    docker run --rm -v "$(pwd)_prometheus_data:/data" -v "$(pwd)/$backup_dir:/backup" alpine tar czf /backup/prometheus_data.tar.gz -C /data .
    
    # Backup configuration files
    print_status "Backing up configuration files..."
    tar czf "$backup_dir/monitoring_configs.tar.gz" monitoring/
    
    print_status "Backup completed: $backup_dir"
}

# Function to update monitoring stack
update_stack() {
    print_header "Updating Heibooky Monitoring Stack..."
    
    cd "$PROJECT_DIR"
    
    print_status "Pulling latest images..."
    docker-compose pull prometheus grafana alertmanager redis-exporter node-exporter cadvisor loki promtail
    
    print_status "Restarting services with new images..."
    restart_monitoring
}

# Function to show resource usage
show_resources() {
    print_header "Monitoring Stack Resource Usage"
    
    cd "$PROJECT_DIR"
    
    echo ""
    print_status "Container Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" \
        $(docker-compose ps -q prometheus grafana alertmanager redis-exporter node-exporter cadvisor loki promtail 2>/dev/null)
    
    echo ""
    print_status "Volume Usage:"
    docker system df -v | grep -E "(prometheus_data|grafana_data|alertmanager_data|loki_data)"
}

# Function to generate monitoring report
generate_report() {
    print_header "Generating Monitoring Health Report..."
    
    local report_file="monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Heibooky Monitoring Health Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo ""
        
        echo "Service Status:"
        echo "---------------"
        check_services 2>&1
        echo ""
        
        echo "Resource Usage:"
        echo "---------------"
        show_resources 2>&1
        echo ""
        
        echo "Recent Alerts (if any):"
        echo "----------------------"
        curl -s "http://localhost:9093/api/v1/alerts" | jq -r '.data[] | select(.status.state == "active") | "\(.labels.alertname): \(.annotations.summary)"' 2>/dev/null || echo "No active alerts or Alertmanager not accessible"
        echo ""
        
        echo "Disk Usage:"
        echo "-----------"
        df -h | grep -E "(prometheus|grafana|loki)"
        echo ""
        
    } > "$report_file"
    
    print_status "Report generated: $report_file"
}

# Main script logic
case "${1:-}" in
    "start")
        start_monitoring
        ;;
    "stop")
        stop_monitoring
        ;;
    "restart")
        restart_monitoring
        ;;
    "status")
        check_services
        ;;
    "logs")
        view_logs "${2:-all}"
        ;;
    "backup")
        backup_data
        ;;
    "update")
        update_stack
        ;;
    "resources")
        show_resources
        ;;
    "report")
        generate_report
        ;;
    "help"|"-h"|"--help")
        echo "Heibooky Monitoring Stack Management"
        echo ""
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  start      Start the monitoring stack"
        echo "  stop       Stop the monitoring stack"
        echo "  restart    Restart the monitoring stack"
        echo "  status     Check service status"
        echo "  logs       View logs (optionally specify service name)"
        echo "  backup     Backup monitoring data"
        echo "  update     Update monitoring stack images"
        echo "  resources  Show resource usage"
        echo "  report     Generate health report"
        echo "  help       Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 start"
        echo "  $0 logs grafana"
        echo "  $0 backup"
        ;;
    *)
        print_error "Unknown command: ${1:-}"
        echo "Use '$0 help' for usage information."
        exit 1
        ;;
esac
