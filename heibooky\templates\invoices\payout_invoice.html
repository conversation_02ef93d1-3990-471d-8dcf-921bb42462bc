{% load currency_filters %}
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <title>{{ translations.receipt_title }} #{{ payout.id|slice:":8" }}</title>
    <style type="text/css">
        @page {
            size: a4 portrait;
            margin: 1cm;
            @frame footer {
                -pdf-frame-content: footerContent;
                bottom: 0.5cm;
                margin-left: 1cm;
                margin-right: 1cm;
                height: 1cm;
            }
        }
        
        body {
            font-family: Helvetica, Arial, sans-serif;
            line-height: 1.5;
            color: #113158;
            margin: 0;
            padding: 0;
            font-size: 12px;
        }
        
        .invoice-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }
        
        .invoice-header {
            background-color: #113158;
            color: white;
            padding: 20px;
            border-bottom: 4px solid #FCB51F;
        }
        
        .logo-container {
            display: table;
            margin: 0 auto 15px auto;
            width: 200px;
            height: 50px;
        }
        
        .logo-wrapper {
            display: table-cell;
            vertical-align: middle;
            text-align: center;
        }
        
        .logo-wrapper img {
            width: 200px;
            height: auto;
            object-fit: contain;
            border: none;
            box-shadow: none;
            background: transparent;
        }
        
        .invoice-body {
            padding: 20px;
            background-color: #ffffff;
        }
        
        h2, h3 {
            color: #113158;
            margin-top: 0;
            border-bottom: 2px solid #FCB51F;
            padding-bottom: 5px;
            display: inline-block;
        }
        
        .receipt-info {
            display: table;
            width: 100%;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #FCB51F;
        }
        
        .recipient-section {
            display: table-cell;
            width: 100%;
        }
        
        .masked-iban {
            font-family: monospace;
            letter-spacing: 2px;
            background-color: #f0f0f0;
            padding: 5px;
            border-radius: 3px;
            display: inline-block;
        }
        
        .payment-details {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
            border-top: 3px solid #FCB51F;
        }
        
        .booking-info {
            margin: 15px 0;
            padding: 15px;
            background-color: #f4f4f4;
            border-left: 4px solid #113158;
            border-right: 1px solid #ddd;
            border-top: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
            border-radius: 0 4px 4px 0;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .invoice-table th {
            background-color: #113158;
            color: white;
            padding: 10px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #113158;
        }
        
        .invoice-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            border-left: 1px solid #eeeeee;
            border-right: 1px solid #eeeeee;
        }
        
        .invoice-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .amount-section {
            margin-top: 20px;
            border-top: 3px solid #FCB51F;
            padding-top: 15px;
        }
        
        .total-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .total-table td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
            color: #113158;
        }
        
        .legal-statement {
            margin-top: 30px;
            padding: 15px;
            font-size: 10px;
            color: #666;
            background-color: #f9f9f9;
            border-top: 1px solid #eee;
            border-left: 4px solid #FCB51F;
            border-radius: 0 4px 4px 0;
        }
        
        .legal-statement h4 {
            color: #113158;
            margin: 0 0 10px 0;
        }
        
        .legal-statement ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .legal-statement li {
            margin-bottom: 5px;
        }
        
        .footer {
            margin-top: 30px;
            padding: 15px;
            text-align: center;
            font-size: 10px;
            color: white;
            background-color: #113158;
            border-top: 3px solid #FCB51F;
        }
        
        .accent-text {
            color: #FCB51F;
            font-weight: bold;
        }
        
        .separator {
            height: 2px;
            background-color: #FCB51F;
            width: 100%;
            margin: 20px 0;
        }
        
        #footerContent {
            border-top: 1px solid #eee;
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div class="logo-container">
                <div class="logo-wrapper">
                    <img src="{{ company_logo|default:'logo.png' }}" alt="{{ company_name }}">
                </div>
            </div>
            <h2>{{ translations.receipt_title }}</h2>
            <p><span class="accent-text">{{ translations.receipt_number }}:</span> {{ invoice.progressive_number }}</p>
            <p><span class="accent-text">{{ translations.payment_date }}:</span> {{ payout.created_at|date:"d/m/Y H:i" }}</p>
        </div>
        
        <div class="invoice-body">
            <div class="receipt-info">
                <div class="recipient-section">
                    <h3>{{ translations.recipient_details }}</h3>
                    <p><strong>{{ billing.get_full_name }}</strong></p>
                    <p class="masked-iban">IBAN: {{ billing.iban|slice:":4" }}•••••••{{ billing.iban|slice:"-4:" }}</p>
                </div>
            </div>

            {% if payout.booking %}
            <div class="booking-info">
                <h3>{{ translations.booking_details }}</h3>
                <div class="separator"></div>
                <table class="invoice-table">
                    <tr>
                        <td width="30%"><strong>{{ translations.property_details }}:</strong></td>
                        <td>{{ payout.booking.property.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>{{ translations.booking_period }}:</strong></td>
                        <td>{{ payout.booking.checkin_date|date:"d/m/Y" }} - {{ payout.booking.checkout_date|date:"d/m/Y" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{{ translations.guest_details }}:</strong></td>
                        <td>{{ payout.booking.reservation_data.guest_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>{{ translations.payment_reference }}:</strong></td>
                        <td>{{ payout.stripe_payment_intent_id|slice:":12" }}</td>
                    </tr>
                </table>
            </div>
            {% endif %}

            <div class="payment-details">
                <h3>{{ translations.payment_details }}</h3>
                <div class="separator"></div>
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>{{ translations.payment_method }}</th>
                            <th>{{ translations.payment_amount }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ translations.bank_transfer }}</td>
                            <td><strong class="accent-text">€{{ payout.amount }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="legal-statement">
                <h4>Riepilogo elaborazione e distribuzione dei pagamenti</h4>
                <ul>
                    <li>I pagamenti verranno inviati al tuo conto bancario registrato entro 14 giorni di calendario dal checkout come ospite riuscito</li>
                    <li>Verifica che i tuoi dati bancari siano corretti; informazioni imprecise potrebbero ritardare il pagamento</li>
                    <li>Riceverai una ricevuta elettronica una volta erogati i fondi</li>
                    <li>Modifiche o cancellazioni dopo l'avvio del pagamento potrebbero non essere possibili</li>
                    <li>Contattare l'assistenza se il pagamento non viene ricevuto entro 14 giorni dal pagamento</li>
                    <li>Gli utenti sono responsabili del monitoraggio dei loro conti per i pagamenti in arrivo</li>
                    <li>HeiBooky non è responsabile per ritardi al di fuori del nostro controllo, comprese informazioni bancarie errate</li>
                </ul>
                <p>Per domande o dubbi, contatta il nostro team di supporto con il tuo ID utente e i dettagli del pagamento.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>{{ translations.this_is_receipt }}</p>
            <p>{{ translations.payment_processed_by }} <span class="accent-text">HeiBooky</span></p>
            <p>{{ company_name }} - VAT: {{ company_vat }}</p>
        </div>
    </div>
    
    <div id="footerContent">
        <p style="text-align: center; font-size: 9px; color: #666;">
            Page <pdf:pagenumber> of <pdf:pagecount>
        </p>
    </div>
</body>
</html>