from unittest.mock import MagicMock, patch

from apps.integrations.models import Notification
from django.contrib.auth import get_user_model
from django.test import TestCase
from services.notification.handlers import GeneralNotificationHandler

User = get_user_model()


class GeneralNotificationHandlerTest(TestCase):
    """Test cases for the GeneralNotificationHandler class"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email="<EMAIL>", name="Test User", password="testpassword"
        )

    def test_create_db_notification(self):
        """Test that database notifications are created correctly"""
        # Create a notification handler
        handler = GeneralNotificationHandler(
            users=self.user,
            title="Test Notification",
            message="This is a test notification",
        )

        # Send notification (database only)
        handler.send_notification(channels=["database"])

        # Check that a notification was created
        notifications = Notification.objects.filter(user=self.user)
        self.assertEqual(notifications.count(), 1)
        self.assertEqual(notifications[0].title, "Test Notification")
        self.assertEqual(notifications[0].message, "This is a test notification")

    @patch("services.notification.handlers.async_to_sync")
    def test_send_websocket_notification(self, mock_async_to_sync):
        """Test that websocket notifications are sent correctly"""
        # Create a mock channel layer
        mock_channel_layer = MagicMock()

        # Create a notification handler with the mock channel layer
        handler = GeneralNotificationHandler(
            users=self.user,
            title="Test Notification",
            message="This is a test notification",
        )
        handler.channel_layer = mock_channel_layer

        # Send notification (websocket only)
        handler.send_notification(channels=["websocket"])

        # Check that the channel layer was called correctly
        mock_async_to_sync.assert_called_once()

    @patch("services.email.email_service.EmailService.send_email")
    def test_send_email_notification(self, mock_send_email):
        """Test that email notifications are sent correctly"""
        # Create a mock email service
        mock_email_service = MagicMock()
        mock_email_service.send_email = mock_send_email

        # Create a notification handler with the mock email service
        handler = GeneralNotificationHandler(
            users=self.user,
            title="Test Notification",
            message="This is a test notification",
            email_service=mock_email_service,
            email_template="test_template",
        )

        # Send notification (email only)
        handler.send_notification(channels=["email"])

        # Check that the email service was called
        mock_send_email.assert_called_once()

    def test_multiple_users(self):
        """Test that notifications are sent to multiple users"""
        # Create another user
        user2 = User.objects.create_user(
            email="<EMAIL>", name="Test User 2", password="testpassword"
        )

        # Create a notification handler with multiple users
        handler = GeneralNotificationHandler(
            users=[self.user, user2],
            title="Test Notification",
            message="This is a test notification",
        )

        # Send notification (database only)
        handler.send_notification(channels=["database"])

        # Check that notifications were created for both users
        self.assertEqual(Notification.objects.filter(user=self.user).count(), 1)
        self.assertEqual(Notification.objects.filter(user=user2).count(), 1)
