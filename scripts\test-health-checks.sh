#!/bin/bash

# Health Check Testing Script for Heibooky
# This script tests all health check endpoints to ensure they're working correctly

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="${BASE_URL:-http://localhost}"
TIMEOUT=10

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Test a health check endpoint
test_endpoint() {
    local endpoint="$1"
    local expected_status="${2:-200}"
    local description="$3"
    
    info "Testing $description: $BASE_URL$endpoint"
    
    # Test with curl
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" --connect-timeout $TIMEOUT "$BASE_URL$endpoint" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        http_status=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
        body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
        
        if [ "$http_status" = "$expected_status" ]; then
            log "✅ $description: HTTP $http_status (Expected: $expected_status)"
            
            # Try to parse JSON response
            if echo "$body" | jq . >/dev/null 2>&1; then
                status_field=$(echo "$body" | jq -r '.status // "unknown"')
                info "   Status: $status_field"
                
                # Show health checks if available
                if echo "$body" | jq -e '.checks' >/dev/null 2>&1; then
                    echo "$body" | jq -r '.checks | to_entries[] | "   \(.key): \(.value)"'
                fi
            else
                info "   Response: $body"
            fi
        else
            error "❌ $description: HTTP $http_status (Expected: $expected_status)"
            info "   Response: $body"
            return 1
        fi
    else
        error "❌ $description: Connection failed"
        return 1
    fi
    
    echo
    return 0
}

# Test endpoint with and without trailing slash
test_endpoint_variants() {
    local base_endpoint="$1"
    local expected_status="${2:-200}"
    local description="$3"
    
    # Test without trailing slash
    test_endpoint "$base_endpoint" "$expected_status" "$description (no trailing slash)"
    
    # Test with trailing slash
    test_endpoint "$base_endpoint/" "$expected_status" "$description (with trailing slash)"
}

# Main testing function
main() {
    log "=== Health Check Testing Started ==="
    log "Base URL: $BASE_URL"
    log "Timeout: ${TIMEOUT}s"
    echo
    
    # Check if jq is available for JSON parsing
    if ! command -v jq &> /dev/null; then
        warn "jq not found - JSON responses will not be parsed"
    fi
    
    # Test all health check endpoints
    local failed_tests=0
    
    # Root health check
    test_endpoint_variants "/health" 200 "Root Health Check"
    [ $? -ne 0 ] && ((failed_tests++))
    
    # Monitoring health checks
    test_endpoint_variants "/monitoring/health" 200 "Monitoring Health Check"
    [ $? -ne 0 ] && ((failed_tests++))
    
    test_endpoint_variants "/monitoring/ready" 200 "Readiness Check"
    [ $? -ne 0 ] && ((failed_tests++))
    
    test_endpoint_variants "/monitoring/alive" 200 "Liveness Check"
    [ $? -ne 0 ] && ((failed_tests++))
    
    # Metrics endpoint
    test_endpoint "/monitoring/metrics" 200 "Prometheus Metrics"
    [ $? -ne 0 ] && ((failed_tests++))
    
    # Metrics debug endpoint
    test_endpoint "/monitoring/metrics/debug" 200 "Metrics Debug"
    [ $? -ne 0 ] && ((failed_tests++))
    
    # WebSocket health check (if available)
    test_endpoint "/support/websocket/health" 200 "WebSocket Health Check"
    # Don't count this as failure since it might not be available
    
    # Summary
    log "=== Health Check Testing Completed ==="
    if [ $failed_tests -eq 0 ]; then
        log "✅ All health checks passed!"
    else
        error "❌ $failed_tests health check(s) failed"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --url)
            BASE_URL="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --url URL          Base URL to test (default: http://localhost)"
            echo "  --timeout SECONDS  Request timeout in seconds (default: 10)"
            echo "  --help             Show this help message"
            echo
            echo "Examples:"
            echo "  $0"
            echo "  $0 --url https://backend.heibooky.com"
            echo "  $0 --url http://localhost:8000 --timeout 5"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
