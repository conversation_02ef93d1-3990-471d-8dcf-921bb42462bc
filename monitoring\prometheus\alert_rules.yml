# Alert Rules for Heibooky Application
groups:
  # SLO-Based Alerting Rules
  - name: slo_alerts
    rules:
      # Availability SLO (99.9% target)
      - alert: SLOAvailabilityBurnRateCritical
        expr: |
          (
            sum(rate(django_http_requests_total{status=~"5.."}[1h])) /
            sum(rate(django_http_requests_total[1h]))
          ) > (14.4 * (1 - 0.999))
        for: 2m
        labels:
          severity: critical
          slo: availability
          burn_rate: fast
        annotations:
          summary: "Critical availability SLO burn rate detected"
          description: "Availability SLO is burning at {{ $value | humanizePercentage }} rate, consuming 2% of monthly error budget in 1 hour"
          runbook_url: "https://docs.heibooky.com/runbooks/slo-availability-critical"

      - alert: SLOAvailabilityBurnRateWarning
        expr: |
          (
            sum(rate(django_http_requests_total{status=~"5.."}[6h])) /
            sum(rate(django_http_requests_total[6h]))
          ) > (6 * (1 - 0.999))
        for: 15m
        labels:
          severity: warning
          slo: availability
          burn_rate: slow
        annotations:
          summary: "Warning availability SLO burn rate detected"
          description: "Availability SLO is burning at {{ $value | humanizePercentage }} rate, consuming 5% of monthly error budget in 6 hours"
          runbook_url: "https://docs.heibooky.com/runbooks/slo-availability-warning"

      # Latency SLO (95% of requests < 500ms)
      - alert: SLOLatencyBurnRateCritical
        expr: |
          (
            1 - (
              sum(rate(django_http_request_duration_seconds_bucket{le="0.5"}[1h])) /
              sum(rate(django_http_request_duration_seconds_count[1h]))
            )
          ) > (14.4 * (1 - 0.95))
        for: 2m
        labels:
          severity: critical
          slo: latency
          burn_rate: fast
        annotations:
          summary: "Critical latency SLO burn rate detected"
          description: "Latency SLO is burning at {{ $value | humanizePercentage }} rate, consuming 2% of monthly error budget in 1 hour"
          runbook_url: "https://docs.heibooky.com/runbooks/slo-latency-critical"

      - alert: SLOLatencyBurnRateWarning
        expr: |
          (
            1 - (
              sum(rate(django_http_request_duration_seconds_bucket{le="0.5"}[6h])) /
              sum(rate(django_http_request_duration_seconds_count[6h]))
            )
          ) > (6 * (1 - 0.95))
        for: 15m
        labels:
          severity: warning
          slo: latency
          burn_rate: slow
        annotations:
          summary: "Warning latency SLO burn rate detected"
          description: "Latency SLO is burning at {{ $value | humanizePercentage }} rate, consuming 5% of monthly error budget in 6 hours"
          runbook_url: "https://docs.heibooky.com/runbooks/slo-latency-warning"

      # Error Budget Exhaustion Alerts
      - alert: SLOErrorBudgetExhausted
        expr: |
          (
            1 - (
              sum(rate(django_http_requests_total{status!~"5.."}[30d])) /
              sum(rate(django_http_requests_total[30d]))
            )
          ) < 0.001  # Less than 0.1% error budget remaining
        for: 5m
        labels:
          severity: critical
          slo: error_budget
        annotations:
          summary: "SLO error budget exhausted"
          description: "Monthly error budget is exhausted with {{ $value | humanizePercentage }} remaining"
          runbook_url: "https://docs.heibooky.com/runbooks/slo-error-budget-exhausted"

      - alert: SLOErrorBudgetLow
        expr: |
          (
            1 - (
              sum(rate(django_http_requests_total{status!~"5.."}[30d])) /
              sum(rate(django_http_requests_total[30d]))
            )
          ) < 0.01  # Less than 1% error budget remaining
        for: 10m
        labels:
          severity: warning
          slo: error_budget
        annotations:
          summary: "SLO error budget running low"
          description: "Monthly error budget is running low with {{ $value | humanizePercentage }} remaining"
          runbook_url: "https://docs.heibooky.com/runbooks/slo-error-budget-low"

  # Multi-Window Alerting Strategy
  - name: multi_window_alerts
    rules:
      # Fast burn rate (2% budget in 1 hour) + slow burn rate (5% budget in 6 hours)
      - alert: AvailabilityMultiWindowBurn
        expr: |
          (
            (
              sum(rate(django_http_requests_total{status=~"5.."}[1h])) /
              sum(rate(django_http_requests_total[1h]))
            ) > (14.4 * (1 - 0.999))
          )
          and
          (
            (
              sum(rate(django_http_requests_total{status=~"5.."}[5m])) /
              sum(rate(django_http_requests_total[5m]))
            ) > (14.4 * (1 - 0.999))
          )
        for: 2m
        labels:
          severity: critical
          alert_type: multi_window
          slo: availability
        annotations:
          summary: "Multi-window availability SLO violation"
          description: "Both short-term (5m) and long-term (1h) error rates exceed SLO thresholds"
          runbook_url: "https://docs.heibooky.com/runbooks/multi-window-availability"

      # Latency multi-window alerting
      - alert: LatencyMultiWindowBurn
        expr: |
          (
            (
              1 - (
                sum(rate(django_http_request_duration_seconds_bucket{le="0.5"}[1h])) /
                sum(rate(django_http_request_duration_seconds_count[1h]))
              )
            ) > (14.4 * (1 - 0.95))
          )
          and
          (
            (
              1 - (
                sum(rate(django_http_request_duration_seconds_bucket{le="0.5"}[5m])) /
                sum(rate(django_http_request_duration_seconds_count[5m]))
              )
            ) > (14.4 * (1 - 0.95))
          )
        for: 2m
        labels:
          severity: critical
          alert_type: multi_window
          slo: latency
        annotations:
          summary: "Multi-window latency SLO violation"
          description: "Both short-term (5m) and long-term (1h) latency exceed SLO thresholds"
          runbook_url: "https://docs.heibooky.com/runbooks/multi-window-latency"

  # Business Impact Alerts
  - name: business_impact_alerts
    rules:
      - alert: CriticalBusinessFunctionDown
        expr: |
          sum(rate(django_http_requests_total{handler=~".*booking.*|.*payment.*|.*login.*", status=~"5.."}[5m])) /
          sum(rate(django_http_requests_total{handler=~".*booking.*|.*payment.*|.*login.*"}[5m])) > 0.05
        for: 1m
        labels:
          severity: critical
          impact: business_critical
        annotations:
          summary: "Critical business function experiencing high error rate"
          description: "Critical business functions (booking/payment/login) have {{ $value | humanizePercentage }} error rate"
          runbook_url: "https://docs.heibooky.com/runbooks/business-function-down"

      - alert: RevenueImpactingErrors
        expr: |
          sum(rate(django_http_requests_total{handler=~".*payment.*", status=~"5.."}[5m])) > 0.1
        for: 30s
        labels:
          severity: critical
          impact: revenue
        annotations:
          summary: "Revenue-impacting payment errors detected"
          description: "Payment system experiencing {{ $value }} errors per second"
          runbook_url: "https://docs.heibooky.com/runbooks/payment-errors"

  # Alert Escalation and Fatigue Reduction
  - name: escalation_alerts
    rules:
      # Escalation based on alert duration
      - alert: PersistentHighErrorRate
        expr: |
          ALERTS{alertname="HighErrorRate", alertstate="firing"} > 0
        for: 15m
        labels:
          severity: critical
          escalation: level_2
          original_alert: HighErrorRate
        annotations:
          summary: "Escalated: High error rate persisting for 15+ minutes"
          description: "High error rate alert has been firing for over 15 minutes, escalating to level 2"
          runbook_url: "https://docs.heibooky.com/runbooks/escalation-high-error-rate"

      - alert: PersistentServiceDown
        expr: |
          ALERTS{alertname=~".*Down", alertstate="firing"} > 0
        for: 10m
        labels:
          severity: critical
          escalation: level_2
          original_alert: "{{ $labels.alertname }}"
        annotations:
          summary: "Escalated: Service down for 10+ minutes"
          description: "Service down alert {{ $labels.alertname }} has been firing for over 10 minutes"
          runbook_url: "https://docs.heibooky.com/runbooks/escalation-service-down"

      # Alert grouping to reduce fatigue
      - alert: MultipleServiceIssues
        expr: |
          count(ALERTS{alertstate="firing", severity="critical"}) > 3
        for: 2m
        labels:
          severity: critical
          alert_type: grouped
        annotations:
          summary: "Multiple critical alerts firing simultaneously"
          description: "{{ $value }} critical alerts are currently firing, indicating potential systemic issue"
          runbook_url: "https://docs.heibooky.com/runbooks/multiple-service-issues"

      # Dependency-aware alerting
      - alert: DownstreamServiceImpact
        expr: |
          (up{job="django-app"} == 0) and (up{job="redis"} == 1)
        for: 1m
        labels:
          severity: warning
          alert_type: dependency
        annotations:
          summary: "Django app down but dependencies are healthy"
          description: "Django application is down but Redis is healthy, indicating application-specific issue"
          runbook_url: "https://docs.heibooky.com/runbooks/downstream-impact"

  # Capacity and Performance Alerts
  - name: capacity_alerts
    rules:
      - alert: CapacityPlanningRequired
        expr: |
          predict_linear(
            100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[1h])) * 100)[7d:1h],
            7*24*3600
          ) > 80
        for: 30m
        labels:
          severity: warning
          alert_type: capacity
        annotations:
          summary: "Capacity planning required - CPU usage trending high"
          description: "CPU usage is predicted to exceed 80% within 7 days based on current trends"
          runbook_url: "https://docs.heibooky.com/runbooks/capacity-planning-cpu"

      - alert: DiskSpaceProjection
        expr: |
          predict_linear(
            (1 - (node_filesystem_free_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100[7d:1h],
            30*24*3600
          ) > 90
        for: 1h
        labels:
          severity: warning
          alert_type: capacity
        annotations:
          summary: "Disk space will be critical within 30 days"
          description: "Disk usage is projected to exceed 90% within 30 days on {{ $labels.instance }}"
          runbook_url: "https://docs.heibooky.com/runbooks/capacity-planning-disk"

      - alert: DatabaseConnectionPoolSaturation
        expr: |
          (django_db_connections_active / django_db_connections_max) > 0.8
        for: 5m
        labels:
          severity: warning
          alert_type: capacity
        annotations:
          summary: "Database connection pool approaching saturation"
          description: "Database connection pool is {{ $value | humanizePercentage }} full"
          runbook_url: "https://docs.heibooky.com/runbooks/db-connection-pool"

  # Security and Compliance Alerts
  - name: security_alerts
    rules:
      - alert: SuspiciousLoginActivity
        expr: |
          sum(rate(django_http_requests_total{handler=~".*login.*", status="401"}[5m])) > 5
        for: 2m
        labels:
          severity: warning
          alert_type: security
        annotations:
          summary: "Suspicious login activity detected"
          description: "High rate of failed login attempts: {{ $value }} per second"
          runbook_url: "https://docs.heibooky.com/runbooks/suspicious-login"

      - alert: UnauthorizedAPIAccess
        expr: |
          sum(rate(django_http_requests_total{status="403"}[5m])) > 2
        for: 1m
        labels:
          severity: warning
          alert_type: security
        annotations:
          summary: "High rate of unauthorized API access attempts"
          description: "{{ $value }} unauthorized access attempts per second"
          runbook_url: "https://docs.heibooky.com/runbooks/unauthorized-access"

      - alert: SecurityHeadersMissing
        expr: |
          sum(rate(django_http_requests_total{security_headers="missing"}[5m])) > 0
        for: 5m
        labels:
          severity: warning
          alert_type: security
        annotations:
          summary: "Security headers missing from responses"
          description: "Responses are missing required security headers"
          runbook_url: "https://docs.heibooky.com/runbooks/security-headers"

  - name: django_alerts
    rules:
      - alert: DjangoAppDown
        expr: up{job="django-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Django application is down"
          description: "Django application has been down for more than 1 minute"

      - alert: HighRequestLatency
        expr: django_http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request latency detected"
          description: "95th percentile latency is above 2 seconds for 5 minutes"

      - alert: HighErrorRate
        expr: rate(django_http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 10% for 2 minutes"

  # Reliability and Error Handling Alerts
  - name: monitoring_reliability
    rules:
      - alert: MonitoringServiceDown
        expr: up{job=~"prometheus|grafana|alertmanager|loki"} == 0
        for: 1m
        labels:
          severity: critical
          component: monitoring
        annotations:
          summary: "Monitoring service {{ $labels.job }} is down"
          description: "Monitoring service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute"
          runbook_url: "https://docs.heibooky.com/runbooks/monitoring-service-down"

      - alert: PrometheusTargetDown
        expr: up == 0
        for: 5m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus target {{ $labels.instance }} is down"
          description: "Prometheus target {{ $labels.instance }} for job {{ $labels.job }} has been down for more than 5 minutes"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-target-down"

      - alert: PrometheusConfigReloadFailed
        expr: prometheus_config_last_reload_successful == 0
        for: 0m
        labels:
          severity: critical
          component: monitoring
        annotations:
          summary: "Prometheus configuration reload failed"
          description: "Prometheus configuration reload has failed on {{ $labels.instance }}"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-config-reload-failed"

      - alert: PrometheusRuleEvaluationFailures
        expr: increase(prometheus_rule_evaluation_failures_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus rule evaluation failures"
          description: "Prometheus has {{ $value }} rule evaluation failures in the last 5 minutes on {{ $labels.instance }}"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-rule-evaluation-failures"

      - alert: PrometheusNotificationQueueRunningFull
        expr: predict_linear(prometheus_notifications_queue_length[5m], 60 * 30) > min_over_time(prometheus_notifications_queue_capacity[5m])
        for: 0m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus notification queue running full"
          description: "Prometheus notification queue is running full on {{ $labels.instance }}"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-notification-queue-full"

      - alert: PrometheusErrorSendingAlertsToSomeAlertmanagers
        expr: (rate(prometheus_notifications_errors_total[5m]) / rate(prometheus_notifications_sent_total[5m])) > 0.01
        for: 5m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus error sending alerts to some alertmanagers"
          description: "{{ printf \"%.1f\" $value }}% errors while sending alerts from Prometheus {{ $labels.instance }} to Alertmanager {{ $labels.alertmanager }}"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-alertmanager-errors"

      - alert: PrometheusNotConnectedToAlertmanagers
        expr: prometheus_notifications_alertmanagers_discovered < 1
        for: 0m
        labels:
          severity: critical
          component: monitoring
        annotations:
          summary: "Prometheus not connected to alertmanagers"
          description: "Prometheus {{ $labels.instance }} is not connected to any alertmanagers"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-no-alertmanagers"

      - alert: PrometheusTSDBReloadsFailing
        expr: increase(prometheus_tsdb_reloads_failures_total[3h]) > 0
        for: 0m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus TSDB reloads failing"
          description: "Prometheus {{ $labels.instance }} has detected {{ $value }} TSDB reload failures over the last 3h"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-tsdb-reload-failures"

      - alert: PrometheusTSDBCompactionsFailing
        expr: increase(prometheus_tsdb_compactions_failed_total[3h]) > 0
        for: 0m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus TSDB compactions failing"
          description: "Prometheus {{ $labels.instance }} has detected {{ $value }} TSDB compaction failures over the last 3h"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-tsdb-compaction-failures"

      - alert: PrometheusNotIngestingSamples
        expr: rate(prometheus_tsdb_head_samples_appended_total[5m]) <= 0
        for: 10m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus not ingesting samples"
          description: "Prometheus {{ $labels.instance }} is not ingesting samples"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-not-ingesting-samples"

      - alert: PrometheusDuplicateSamples
        expr: increase(prometheus_tsdb_head_samples_appended_total[5m]) - increase(prometheus_tsdb_head_samples_appended_total[5m] offset 5m) > 0
        for: 0m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus duplicate samples"
          description: "Prometheus {{ $labels.instance }} has ingested {{ $value }} duplicate samples in the last 5 minutes"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-duplicate-samples"

      - alert: PrometheusOutOfOrderSamples
        expr: increase(prometheus_tsdb_out_of_order_samples_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          component: monitoring
        annotations:
          summary: "Prometheus out of order samples"
          description: "Prometheus {{ $labels.instance }} has ingested {{ $value }} out-of-order samples in the last 5 minutes"
          runbook_url: "https://docs.heibooky.com/runbooks/prometheus-out-of-order-samples"

  # Grafana Reliability Alerts
  - name: grafana_reliability
    rules:
      - alert: GrafanaDown
        expr: up{job="grafana"} == 0
        for: 1m
        labels:
          severity: critical
          component: grafana
        annotations:
          summary: "Grafana is down"
          description: "Grafana has been down for more than 1 minute"
          runbook_url: "https://docs.heibooky.com/runbooks/grafana-down"

      - alert: GrafanaHighMemoryUsage
        expr: (process_resident_memory_bytes{job="grafana"} / 1024 / 1024) > 1000
        for: 5m
        labels:
          severity: warning
          component: grafana
        annotations:
          summary: "Grafana high memory usage"
          description: "Grafana memory usage is {{ $value }}MB, which is above 1GB threshold"
          runbook_url: "https://docs.heibooky.com/runbooks/grafana-high-memory"

      - alert: GrafanaDatabaseConnectionFailures
        expr: increase(grafana_database_conn_open_failed_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          component: grafana
        annotations:
          summary: "Grafana database connection failures"
          description: "Grafana has {{ $value }} database connection failures in the last 5 minutes"
          runbook_url: "https://docs.heibooky.com/runbooks/grafana-db-connection-failures"

      - alert: GrafanaHighDashboardLoadTime
        expr: histogram_quantile(0.95, rate(grafana_page_response_time_milliseconds_bucket[5m])) > 5000
        for: 5m
        labels:
          severity: warning
          component: grafana
        annotations:
          summary: "Grafana high dashboard load time"
          description: "95th percentile dashboard load time is {{ $value }}ms, which is above 5 seconds"
          runbook_url: "https://docs.heibooky.com/runbooks/grafana-slow-dashboards"

      - alert: GrafanaAlertingExecutionErrors
        expr: increase(grafana_alerting_rule_evaluation_failures_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          component: grafana
        annotations:
          summary: "Grafana alerting execution errors"
          description: "Grafana has {{ $value }} alerting execution errors in the last 5 minutes"
          runbook_url: "https://docs.heibooky.com/runbooks/grafana-alerting-errors"

  # Loki Reliability Alerts
  - name: loki_reliability
    rules:
      - alert: LokiDown
        expr: up{job="loki"} == 0
        for: 1m
        labels:
          severity: critical
          component: loki
        annotations:
          summary: "Loki is down"
          description: "Loki has been down for more than 1 minute"
          runbook_url: "https://docs.heibooky.com/runbooks/loki-down"

      - alert: LokiRequestErrors
        expr: (rate(loki_request_duration_seconds_count{status_code=~"5.."}[5m]) / rate(loki_request_duration_seconds_count[5m])) > 0.1
        for: 5m
        labels:
          severity: warning
          component: loki
        annotations:
          summary: "Loki request errors"
          description: "Loki error rate is {{ printf \"%.2f\" $value }}%, which is above 10%"
          runbook_url: "https://docs.heibooky.com/runbooks/loki-request-errors"

      - alert: LokiHighIngestionRate
        expr: rate(loki_ingester_received_chunks[5m]) > 1000
        for: 5m
        labels:
          severity: warning
          component: loki
        annotations:
          summary: "Loki high ingestion rate"
          description: "Loki ingestion rate is {{ $value }} chunks/sec, which is above 1000"
          runbook_url: "https://docs.heibooky.com/runbooks/loki-high-ingestion"

      - alert: LokiIngesterUnhealthy
        expr: loki_ingester_flush_queue_length > 100
        for: 5m
        labels:
          severity: warning
          component: loki
        annotations:
          summary: "Loki ingester unhealthy"
          description: "Loki ingester flush queue length is {{ $value }}, which is above 100"
          runbook_url: "https://docs.heibooky.com/runbooks/loki-ingester-unhealthy"

  # Alertmanager Reliability Alerts
  - name: alertmanager_reliability
    rules:
      - alert: AlertmanagerDown
        expr: up{job="alertmanager"} == 0
        for: 1m
        labels:
          severity: critical
          component: alertmanager
        annotations:
          summary: "Alertmanager is down"
          description: "Alertmanager has been down for more than 1 minute"
          runbook_url: "https://docs.heibooky.com/runbooks/alertmanager-down"

      - alert: AlertmanagerConfigurationReloadFailure
        expr: alertmanager_config_last_reload_successful == 0
        for: 0m
        labels:
          severity: critical
          component: alertmanager
        annotations:
          summary: "Alertmanager configuration reload failure"
          description: "Alertmanager configuration reload has failed"
          runbook_url: "https://docs.heibooky.com/runbooks/alertmanager-config-reload-failed"

      - alert: AlertmanagerNotificationFailing
        expr: rate(alertmanager_notifications_failed_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          component: alertmanager
        annotations:
          summary: "Alertmanager notification failing"
          description: "Alertmanager has {{ $value }} failed notifications per second"
          runbook_url: "https://docs.heibooky.com/runbooks/alertmanager-notification-failures"

      - alert: AlertmanagerClusterFailedPeers
        expr: alertmanager_cluster_failed_peers > 0
        for: 5m
        labels:
          severity: warning
          component: alertmanager
        annotations:
          summary: "Alertmanager cluster failed peers"
          description: "Alertmanager has {{ $value }} failed peers in cluster"
          runbook_url: "https://docs.heibooky.com/runbooks/alertmanager-cluster-failed-peers"

      - alert: HighMemoryUsage
        expr: django_process_memory_usage_bytes / 1024 / 1024 / 1024 > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Application memory usage is above 1GB"

  - name: redis_alerts
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 30 seconds"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_config_maxmemory_bytes > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is above 90%"

      - alert: RedisConnectionSpike
        expr: rate(redis_connected_clients[5m]) > 100
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection spike"
          description: "Sudden increase in Redis connections"

  - name: celery_alerts
    rules:
      - alert: CeleryWorkerDown
        expr: celery_workers_total == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "No Celery workers available"
          description: "All Celery workers are down"

      - alert: CeleryQueueBacklog
        expr: celery_queue_length > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Celery queue backlog"
          description: "Celery queue has more than 100 pending tasks"

      - alert: CeleryTaskFailureRate
        expr: rate(celery_task_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High Celery task failure rate"
          description: "Celery task failure rate is above 10%"

  - name: system_alerts
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% for 5 minutes"

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage"
          description: "Disk usage is above 85%"

      - alert: LowDiskSpace
        expr: node_filesystem_free_bytes / 1024 / 1024 / 1024 < 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Less than 5GB of disk space remaining"
