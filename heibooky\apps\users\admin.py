from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import Group
from django.utils.translation import gettext_lazy as _

from .forms import UserChangeForm, UserCreationForm
from .models import User, UserProfile


class UserProfileInline(admin.TabularInline):
    """
    Inline for the UserProfile model to be displayed in the User admin page
    in a tabular format, improving the admin layout for related user information.
    """

    model = UserProfile
    fields = ("image", "last_login_location", "is_customer", "has_billing_profile")
    extra = 0
    readonly_fields = (
        "user",
        "last_login_location",
        "is_customer",
        "has_billing_profile",
    )
    can_delete = False


class UserAdmin(BaseUserAdmin):
    """
    Custom admin configuration for the User model, improving usability
    with field grouping and inline configuration.
    """

    form = UserChangeForm
    add_form = UserCreationForm
    inlines = (UserProfileInline,)

    list_display = (
        "email",
        "name",
        "is_verified",
        "is_admin",
        "is_staff",
        "is_active",
        "created_at",
    )
    list_filter = ("is_verified", "is_admin", "is_staff", "is_active", "created_at")
    search_fields = ("email", "name", "phone")
    ordering = ("-created_at",)

    # Group fields into logical sections
    fieldsets = (
        (None, {"fields": ("email", "password")}),
        (_("Personal Info"), {"fields": ("name", "phone")}),
        (
            _("Permissions"),
            {
                "fields": (
                    "is_active",
                    "has_set_password",
                    "is_verified",
                    "is_admin",
                    "is_staff",
                    "is_superuser",
                    "user_permissions",
                ),
                "description": _("Manage the user permissions and status here."),
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "created_at", "updated_at")}),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "name",
                    "phone",
                    "password1",
                    "password2",
                    "is_verified",
                ),
            },
        ),
    )

    # Configure user permissions to improve filtering experience
    filter_horizontal = ("user_permissions",)

    # Mark specific fields as read-only
    readonly_fields = ("created_at", "updated_at", "last_login")

    def save_model(self, request, obj, form, change):
        """
        Override the save_model method to create a UserProfile
        for every new user created through the admin.
        """
        super().save_model(request, obj, form, change)

        # Automatically create UserProfile if it doesn't exist on user creation
        if not change:  # User is being created
            UserProfile.objects.get_or_create(user=obj)

    # Custom actions if needed for batch processing
    actions = ["mark_as_verified"]

    def mark_as_verified(self, request, queryset):
        """
        Custom action to mark selected users as verified.
        """
        queryset.update(is_verified=True)
        self.message_user(request, _("Selected users have been marked as verified."))

    mark_as_verified.short_description = "Mark selected users as verified"


# Register the UserAdmin class with the User model
admin.site.register(User, UserAdmin)
admin.site.register(UserProfile)
admin.site.unregister(Group)
