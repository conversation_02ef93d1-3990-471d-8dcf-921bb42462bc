import logging

from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.dispatch import receiver

from .metrics import CELERY_TASKS

logger = logging.getLogger(__name__)


@receiver(user_logged_in)
def user_logged_in_handler(sender, request, user, **kwargs):
    """Track user login events."""
    logger.info(
        f"User logged in: {user.email} from IP: {request.META.get('REMOTE_ADDR', 'unknown')}"
    )


@receiver(user_logged_out)
def user_logged_out_handler(sender, request, user, **kwargs):
    """Track user logout events."""
    if user:
        logger.info(f"User logged out: {user.email}")


def track_celery_task(task_name, state):
    """Track Celery task execution."""
    try:
        CELERY_TASKS.labels(task_name=task_name, state=state).inc()
    except Exception as e:
        logger.error(f"Error tracking Celery task: {e}")
