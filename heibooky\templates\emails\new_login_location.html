<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Language" content="it">
    <title>Nuovo accesso rilevato</title>
    <style>
                body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f4f4;
            color: #113158;
        }
        table {
            border-spacing: 0;
            border-collapse: collapse;
            width: 100%;
        }
        td {
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(17, 49, 88, 0.1);
            overflow: hidden;
            font-size: 16px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: #ffffff;
            text-align: center;
            padding: 40px 0;
        }
        .logo-container img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }
        .content {
            padding: 40px 20px;
            text-align: center;
        }
        .title-text {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .instructions {
            color: #454545;
            margin: 20px 0;
        }
        .reset-code-container {
            margin: 20px 0;
        }
        .reset-code {
            font-size: 36px;
            font-weight: bold;
            letter-spacing: 6px;
            background-color: #ffffff;
            color: #113158;
            border: 2px solid #FCB51F;
            border-radius: 8px;
            padding: 12px 20px;
            display: inline-block;
            font-family: 'Courier New', monospace;
        }
        .expiry-notice {
            font-size: 14px;
            color: #666666;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #FCB51F;
            padding: 12px;
            margin: 20px 0;
        }
        .footer {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            padding: 20px;
            font-size: 13px;
            line-height: 1.6;
        }
        .footer a {
            color: #FCB51F;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .divider {
            height: 1px;
            background-color: rgba(252, 189, 76, 0.3);
            margin: 20px 0;
        }
        @media only screen and (max-width: 600px) {
            .title-text {
                font-size: 20px;
            }
            .reset-code {
                font-size: 28px;
                padding: 10px 16px;
            }
        }

        .security-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #FCB51F;
            padding: 16px;
            margin: 20px 0;
            text-align: left;
        }

        .security-info p {
            margin: 8px 0;
            color: #454545;
        }

        .security-info .label {
            font-weight: bold;
            color: #113158;
            display: inline-block;
            width: 120px;
        }

        .warning {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
            padding: 12px;
            margin: 20px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .action-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #113158;
            color: #ffffff;
            text-decoration: none;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }

        .action-button:hover {
            background-color: #1a4a85;
        }
    </style>
</head>
<body>
    <table role="presentation" align="center" class="email-container">
        <!-- Header Section -->
        <tr>
            <td class="header">
                <table align="center">
                    <tr>
                        <td class="logo-container">
                            <img src="{{ logo_url }}" alt="{{ site_name }}">
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Content Section -->
        <tr>
            <td class="content">
                <table align="center" role="presentation">
                    <tr>
                        <td class="title-text">
                            {{ translations.new_login_detected }}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p class="instructions">{{ translations.new_login_message }}</p>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="security-info">
                                <p><span class="label">{{ translations.login_time }}:</span> {{ login_time }}</p>
                                <p><span class="label">{{ translations.location }}:</span> {{ location }}</p>
                                <p><span class="label">{{ translations.device }}:</span> {{ device }}</p>
                                <p><span class="label">{{ translations.ip_address }}:</span> {{ ip_address }}</p>
                                <p><span class="label">{{ translations.previous_location }}:</span> {{ previous_location }}</p>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="warning">
                                {{ translations.not_you_warning }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center;">
                            <a href="{{ account_security_url }}" class="action-button">
                                {{ translations.secure_account }}
                            </a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td class="footer">
                <p>© {{ site_name }} {{ translations.all_rights_reserved }}</p>
                <div class="divider"></div>
                <p>{{ translations.questions_email_us }} <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </td>
        </tr>
    </table>
</body>
</html>
