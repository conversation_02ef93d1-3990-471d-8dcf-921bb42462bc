from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import ChatViewSet, MessageViewSet
from .views.support_team import (
    support_analytics_by_priority,
    support_analytics_summary,
    support_analytics_timeseries,
)
from .views.websocket_health import (
    WebSocketConfigView,
    WebSocketHealthView,
    WebSocketTestView,
)

router = DefaultRouter()
router.register(r"chats", ChatViewSet, basename="chat")
router.register(r"messages", MessageViewSet, basename="message")

urlpatterns = [
    path("", include(router.urls)),
    path(
        "analytics/summary/",
        support_analytics_summary,
        name="support-analytics-summary",
    ),
    path(
        "analytics/timeseries/",
        support_analytics_timeseries,
        name="support-analytics-timeseries",
    ),
    path(
        "analytics/by-priority/",
        support_analytics_by_priority,
        name="support-analytics-by-priority",
    ),
    # WebSocket diagnostic endpoints
    path("websocket/health/", WebSocketHealthView.as_view(), name="websocket-health"),
    path("websocket/config/", WebSocketConfigView.as_view(), name="websocket-config"),
    path("websocket/test/", WebSocketTestView.as_view(), name="websocket-test"),
]
