# Quick Start Guide: Heibooky Monitoring

## Prerequisites

1. <PERSON><PERSON> and <PERSON><PERSON> Compose installed
2. Python dependencies installed: `pip install prometheus-client psutil django-prometheus`

## Start the Full Stack

```bash
# Start everything (application + monitoring)
docker-compose up -d

# Or start just the monitoring stack
./scripts/monitoring.sh start    # Linux/macOS
```

## Access the Monitoring Dashboards

- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Alertmanager**: http://localhost:9093

## Application Health Endpoints

- **Health**: http://localhost:8000/monitoring/health/
- **Metrics**: http://localhost:8000/monitoring/metrics/
- **Ready**: http://localhost:8000/monitoring/ready/
- **Alive**: http://localhost:8000/monitoring/alive/

## Key Features

✅ **Application Metrics** - Request rates, latency, errors  
✅ **Infrastructure Metrics** - CPU, memory, disk, network  
✅ **Log Aggregation** - Structured logs with <PERSON>  
✅ **Alerting** - Email and webhook notifications  
✅ **Dashboards** - Pre-built Grafana dashboards  
✅ **Security Monitoring** - Security event tracking  

## Management Commands

```bash
# Check status
./scripts/monitoring.sh status

# View logs
./scripts/monitoring.sh logs grafana

# Backup data
./scripts/monitoring.sh backup

# Generate report
./scripts/monitoring.sh report
```

## Next Steps

1. Customize alert thresholds in `monitoring/prometheus/alert_rules.yml`
2. Add business-specific metrics to `apps/monitoring/metrics.py`
3. Create custom Grafana dashboards
4. Configure alert notifications (email, Slack, etc.)

For detailed documentation, see `monitoring/README.md`.
