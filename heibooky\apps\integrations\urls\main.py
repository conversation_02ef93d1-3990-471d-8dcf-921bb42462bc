from apps.integrations.views import *
from django.urls import include, path
from rest_framework.routers import DefaultRouter

from .online_checkin import urlpatterns as online_checkin_urlpatterns

router = DefaultRouter()
router.register(r"payouts", PayoutViewSet, basename="payout")

urlpatterns = [
    path("", include(router.urls)),
    path("onboard-property/", PropertyOnboardView.as_view(), name="onboard-property"),
    path(
        "create-connected-account/",
        CreateConnectedAccount.as_view(),
        name="create_connected_account",
    ),
    path("invoice/", InvoiceView.as_view(), name="invoices"),
    path("notifications/", NotificationView.as_view(), name="notifications"),
    path("templates/", DownloadableTemplateListView.as_view(), name="template-list"),
    path("test/", Test.as_view(), name="test"),
    # Dashboard endpoints
    path(
        "dashboard/unread-counts/",
        DashboardUnreadCountsView.as_view(),
        name="dashboard-unread-counts",
    ),
    # Online check-in endpoints
    path("online-checkin/", include(online_checkin_urlpatterns)),
]
