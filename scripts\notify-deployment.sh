#!/bin/bash

# Deployment Notification Script
# This script sends deployment notifications via multiple channels
# Usage: ./notify-deployment.sh <status> <environment> <details>

set -euo pipefail

# Configuration
EMAIL_RECIPIENT="<EMAIL>"
SMTP_SERVER="smtp.gmail.com"
SMTP_PORT="587"

# Function to send email notification
send_email_notification() {
    local status="$1"
    local environment="$2"
    local details="$3"
    
    # Set up cleanup trap for temporary files
    local template_file
    trap 'rm -f "$template_file"' EXIT
    
    if [[ -z "$SMTP_USERNAME" || -z "$SMTP_PASSWORD" ]]; then
        echo "Warning: SMTP credentials not configured. Skipping email notification."
        return 0
    fi
    
    local subject
    local template_file
    
    if [[ "$status" == "success" ]]; then
        subject="✅ Heibooky Deployment Successful - $environment"
        template_file=$(mktemp --suffix=.html)
        
        cat > "$template_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Deployment Success</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0fff4; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; padding: 30px; }
        .header { background-color: #d4edda; color: #155724; padding: 20px; border-radius: 5px; text-align: center; }
        .details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Deployment Successful!</h1>
            <p>Heibooky has been successfully deployed to <strong>$environment</strong></p>
        </div>
        <div class="details">
            <h3>Deployment Details</h3>
            <pre>$details</pre>
        </div>
    </div>
</body>
</html>
EOF
    else
        subject="❌ Heibooky Deployment Failed - $environment"
        template_file=$(mktemp --suffix=.html)
        
        cat > "$template_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Deployment Failure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #fff5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; padding: 30px; }
        .header { background-color: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; text-align: center; }
        .details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .alert { background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ Deployment Failed!</h1>
            <p>Heibooky deployment to <strong>$environment</strong> has failed</p>
        </div>
        <div class="alert">
            <strong>⚠️ Immediate Action Required</strong><br>
            The deployment process encountered an error and needs attention.
        </div>
        <div class="details">
            <h3>Failure Details</h3>
            <pre>$details</pre>
        </div>
    </div>
</body>
</html>
EOF
    fi
    
    # Send email using Python (more reliable than sendmail)
    python3 << EOF
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os

smtp_username = os.environ.get('SMTP_USERNAME')
smtp_password = os.environ.get('SMTP_PASSWORD')

if not smtp_username or not smtp_password:
    print("SMTP credentials not found")
    exit(1)

# Read HTML template
with open('$template_file', 'r') as f:
    html_content = f.read()

# Create message
msg = MIMEMultipart('alternative')
msg['Subject'] = '$subject'
msg['From'] = smtp_username
msg['To'] = '$EMAIL_RECIPIENT'

# Attach HTML
html_part = MIMEText(html_content, 'html')
msg.attach(html_part)

# Send email
try:
    context = ssl.create_default_context()
    with smtplib.SMTP('$SMTP_SERVER', $SMTP_PORT) as server:
        server.starttls(context=context)
        server.login(smtp_username, smtp_password)
        server.sendmail(smtp_username, '$EMAIL_RECIPIENT', msg.as_string())
    print("Email sent successfully")
except Exception as e:
    print(f"Failed to send email: {e}")
    exit(1)
EOF
}

# Function to send Slack notification
send_slack_notification() {
    local status="$1"
    local environment="$2"
    local details="$3"
    
    if [[ -z "$SLACK_WEBHOOK_URL" ]]; then
        echo "Warning: Slack webhook URL not configured. Skipping Slack notification."
        return 0
    fi
    
    local color
    local emoji
    local title
    
    if [[ "$status" == "success" ]]; then
        color="good"
        emoji="✅"
        title="Deployment Successful"
    else
        color="danger"
        emoji="❌"
        title="Deployment Failed"
    fi
    
    local payload=$(jq -n \
        --arg color "$color" \
        --arg emoji "$emoji" \
        --arg title "$emoji Heibooky $title" \
        --arg environment "$environment" \
        --arg status "$status" \
        --arg details "$details" \
        --argjson timestamp "$(date +%s)" \
        '{
            "attachments": [
                {
                    "color": $color,
                    "title": $title,
                    "fields": [
                        {
                            "title": "Environment",
                            "value": $environment,
                            "short": true
                        },
                        {
                            "title": "Status",
                            "value": $status,
                            "short": true
                        },
                        {
                            "title": "Details",
                            "value": ("```" + $details + "```"),
                            "short": false
                        }
                    ],
                    "ts": $timestamp
                }
            ]
        }')
    
    curl -X POST -H 'Content-type: application/json' \
         --data "$payload" \
         "$SLACK_WEBHOOK_URL" || echo "Failed to send Slack notification"
}

# Function to log notification
log_notification() {
    local status="$1"
    local environment="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S UTC')
    
    echo "[$timestamp] Deployment notification sent:"
    echo "  Status: $status"
    echo "  Environment: $environment"
    echo "  Email: $EMAIL_RECIPIENT"
    echo "  Slack: ${SLACK_WEBHOOK_URL:+configured}"
}

# Main function
main() {
    local status="$1"
    local environment="$2"
    local details="$3"
    
    if [[ -z "$status" || -z "$environment" ]]; then
        echo "Usage: $0 <status> <environment> [details]"
        echo "  status: success|failure"
        echo "  environment: staging|production"
        echo "  details: optional deployment details"
        exit 1
    fi
    
    if [[ "$status" != "success" && "$status" != "failure" ]]; then
        echo "Error: Status must be 'success' or 'failure'"
        exit 1
    fi
    
    if [[ "$environment" != "staging" && "$environment" != "production" ]]; then
        echo "Error: Environment must be 'staging' or 'production'"
        exit 1
    fi
    
    # Default details if not provided
    if [[ -z "$details" ]]; then
        details="Deployment to $environment completed with status: $status"
    fi
    
    echo "Sending deployment notifications..."
    
    # Send notifications
    send_email_notification "$status" "$environment" "$details"
    send_slack_notification "$status" "$environment" "$details"
    
    # Log the notification
    log_notification "$status" "$environment"
    
    echo "Deployment notifications completed."
    
    # Exit with error code if deployment failed
    if [[ "$status" == "failure" ]]; then
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
