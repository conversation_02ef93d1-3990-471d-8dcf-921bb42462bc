#!/bin/bash
set -e

# Docker entrypoint script for Heibooky Django application
# This script handles initialization, health checks, and graceful startup

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Function to wait for database
wait_for_db() {
    log "Waiting for database connection..."
    
    cd heibooky
    python << END
import os
import sys
import time
import django
from django.conf import settings
from django.db import connections
from django.core.management.base import BaseCommand

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

def wait_for_db():
    db_conn = None
    max_retries = 30
    retry_count = 0
    
    while not db_conn and retry_count < max_retries:
        try:
            db_conn = connections['default']
            db_conn.ensure_connection()
            print("Database connection successful!")
            return True
        except Exception as e:
            retry_count += 1
            print(f"Database unavailable, waiting... ({retry_count}/{max_retries})")
            print(f"Error: {e}")
            time.sleep(2)
    
    print("Failed to connect to database after maximum retries")
    return False

if not wait_for_db():
    sys.exit(1)
END
    
    if [ $? -ne 0 ]; then
        error "Failed to connect to database"
        exit 1
    fi
    
    cd ..
}

# Function to wait for Redis
wait_for_redis() {
    log "Waiting for Redis connection..."
    
    cd heibooky
    python << END
import os
import sys
import time
import django
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

def wait_for_redis():
    max_retries = 30
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', timeout=10)
            result = cache.get('health_check')
            if result == 'ok':
                cache.delete('health_check')
                print("Redis connection successful!")
                return True
        except Exception as e:
            retry_count += 1
            print(f"Redis unavailable, waiting... ({retry_count}/{max_retries})")
            print(f"Error: {e}")
            time.sleep(2)
    
    print("Failed to connect to Redis after maximum retries")
    return False

if not wait_for_redis():
    sys.exit(1)
END
    
    if [ $? -ne 0 ]; then
        error "Failed to connect to Redis"
        exit 1
    fi
    
    cd ..
}

# Function to run database migrations
run_migrations() {
    log "Running database migrations..."
    cd heibooky
    
    if python manage.py migrate --check; then
        log "No migrations needed"
    else
        log "Running migrations..."
        python manage.py migrate
        if [ $? -eq 0 ]; then
            log "Migrations completed successfully"
        else
            error "Migration failed"
            exit 1
        fi
    fi
    
    cd ..
}

# Function to collect static files
collect_static() {
    log "Collecting static files..."
    cd heibooky
    
    python manage.py collectstatic --noinput --clear
    if [ $? -eq 0 ]; then
        log "Static files collected successfully"
    else
        warn "Static file collection failed, continuing anyway"
    fi
    
    cd ..
}

# Function to create superuser if needed
create_superuser() {
    if [ "$DJANGO_SUPERUSER_EMAIL" ] && [ "$DJANGO_SUPERUSER_PASSWORD" ]; then
        log "Creating superuser if it doesn't exist..."
        cd heibooky
        
        python manage.py shell << END
import os
from django.contrib.auth import get_user_model

User = get_user_model()
superuser_email = os.environ.get('DJANGO_SUPERUSER_EMAIL', '')
superuser_password = os.environ.get('DJANGO_SUPERUSER_PASSWORD', '')

if superuser_email and superuser_password:
    if not User.objects.filter(email=superuser_email).exists():
        User.objects.create_superuser(
            email=superuser_email,
            password=superuser_password
        )
        print("Superuser created successfully")
    else:
        print("Superuser already exists")
else:
    print("Superuser email or password not provided")
END
        
        cd ..
    fi
}

# Function to start the appropriate service
start_service() {
    local service_type=${1:-web}
    
    cd heibooky
    
    case $service_type in
        web)
            log "Starting Django web server..."
            exec daphne -b 0.0.0.0 -p 8000 heibooky.asgi:application
            ;;
        celery-worker)
            log "Starting Celery worker..."
            exec celery -A heibooky worker \
                --loglevel=info \
                --pool=solo \
                --max-tasks-per-child=50 \
                --concurrency=1
            ;;
        celery-beat)
            log "Starting Celery beat scheduler..."
            exec celery -A heibooky beat \
                --loglevel=info \
                --scheduler django_celery_beat.schedulers:DatabaseScheduler
            ;;
        *)
            error "Unknown service type: $service_type"
            exit 1
            ;;
    esac
}

# Main execution
main() {
    log "Starting Heibooky application initialization..."
    
    # Parse command line arguments
    SERVICE_TYPE="web"
    SKIP_MIGRATIONS=false
    SKIP_STATIC=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --service)
                SERVICE_TYPE="$2"
                shift 2
                ;;
            --skip-migrations)
                SKIP_MIGRATIONS=true
                shift
                ;;
            --skip-static)
                SKIP_STATIC=true
                shift
                ;;
            *)
                break
                ;;
        esac
    done
    
    # Wait for dependencies
    wait_for_db
    wait_for_redis
    
    # Run initialization tasks (only for web service)
    if [ "$SERVICE_TYPE" = "web" ]; then
        if [ "$SKIP_MIGRATIONS" = false ]; then
            run_migrations
        fi
        
        if [ "$SKIP_STATIC" = false ]; then
            collect_static
        fi
        
        create_superuser
    fi
    
    # Start the service
    start_service "$SERVICE_TYPE"
}

#!/bin/bash
set -e

# Handle signals for graceful shutdown
trap 'log "Received shutdown signal, exiting gracefully..."; exit 0' SIGTERM SIGINT

# Docker entrypoint script for Heibooky Django application
# …rest of script…

# Run main function
main "$@"
