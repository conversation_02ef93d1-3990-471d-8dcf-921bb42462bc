import os

from django.utils.log import ServerFormatter


class DetailedRequestFormatter(ServerFormatter):
    def format(self, record):
        request = getattr(record, "request", None)
        if request:
            record.source_ip = request.META.get("REMOTE_ADDR", "")
            record.http_method = request.method
            record.requested_url = request.get_full_path()
            record.user_agent = request.META.get("HTTP_USER_AGENT", "")
            record.request_headers = {
                "Referer": request.META.get("HTTP_REFERER", ""),
                "Accept-Language": request.META.get("HTTP_ACCEPT_LANGUAGE", ""),
                "Cookie": request.META.get("HTTP_COOKIE", ""),
            }
            record.user_id = (
                request.user.id if request.user.is_authenticated else "Anonymous"
            )
            record.session_id = (
                request.session.session_key if hasattr(request, "session") else ""
            )
            record.server_ip = request.META.get("SERVER_NAME", "")
            record.process_id = os.getpid()
        else:
            record.source_ip = ""
            record.http_method = ""
            record.requested_url = ""
            record.user_agent = ""
            record.request_headers = {}
            record.user_id = ""
            record.session_id = ""
            record.server_ip = ""
            record.process_id = ""

        return super().format(record)
