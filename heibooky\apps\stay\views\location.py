from apps.stay.models import Location, StaffRole
from apps.stay.serializers import LocationSerializer
from apps.stay.utils import validate_ownership
from django.db.models import Q
from rest_framework import status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


class LocationViewSet(viewsets.ModelViewSet):
    """
    A viewset for managing location data.
    """

    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Return locations of properties where the authenticated user is a staff member.
        """
        return Location.objects.filter(property__staffs=self.request.user)

    def create(self, request, *args, **kwargs):
        """
        Check for duplicate locations and set the owner field.
        """
        data = request.data

        # Check for duplicate location
        existing_location = Location.objects.filter(
            Q(street=data.get("street"))
            & Q(post_code=data.get("post_code"))
            & Q(city=data.get("city"))
            & Q(country=data.get("country"))
            & Q(latitude=data.get("latitude"))
            & Q(longitude=data.get("longitude"))
        ).first()

        if existing_location:
            serializer = self.get_serializer(existing_location)
            return Response(serializer.data, status=status.HTTP_200_OK)

        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        location = self.get_object()
        associated_properties = location.property_set.all()

        if not associated_properties.exists():
            return Response(
                {
                    "error": "Location must be associated with a property before modification"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user has property config permission or is owner for any associated property
        has_permission = False
        for property in associated_properties:
            is_owner = validate_ownership(request.user, str(property.id))
            is_staff_with_permission = StaffRole.objects.filter(
                property=property,
                user=request.user,
                permissions__name="property_config",
                is_active=True,
            ).exists()

            if is_owner or is_staff_with_permission:
                has_permission = True
                break

        if not has_permission:
            return Response(
                {"error": "Insufficient permissions to modify this location"},
                status=status.HTTP_403_FORBIDDEN,
            )

        return super().update(request, *args, **kwargs)
