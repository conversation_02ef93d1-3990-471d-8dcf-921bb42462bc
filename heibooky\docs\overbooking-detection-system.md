# Overbooking Detection and Email Notification System

## Overview

The overbooking detection system automatically identifies when two or more non-manual bookings overlap for the same property and sends immediate email alerts to administrators.

## Features

### 1. Automatic Detection
- Monitors all new non-manual bookings (from Su API/OTAs)
- Detects overlapping dates excluding exact adjacent bookings
- Ignores manual bookings to prevent false positives
- Excludes cancelled bookings from detection

### 2. Email Notification
- Sends immediate alerts to `assisten<PERSON>@heibooky.com`
- Includes comprehensive booking details for both conflicting reservations
- Provides direct links to admin panel for quick resolution
- Available in both HTML and plain text formats

### 3. Detailed Information
The email includes:
- Property details (name, ID, chain ID)
- Existing booking information
- New conflicting booking information
- Overlap period calculation
- Guest details for both bookings
- Channel information (Booking.com, Airbnb, etc.)
- Direct admin panel links

## Implementation Details

### Signal Handler
- `handle_overbooking_detection` - Triggered on booking creation
- Uses Django's `post_save` signal with `transaction.on_commit`
- Ensures database consistency before detection

### Detection Logic
The system identifies overbooking when:
1. A new non-manual booking is created
2. Existing bookings overlap with the new booking dates
3. Overlapping bookings are not cancelled
4. Bookings don't just touch at endpoints (adjacent is allowed)

### Email Templates
- **HTML**: `templates/emails/overbooking_alert.html` - Rich formatted email with styling
- **Text**: `templates/emails/overbooking_alert.txt` - Plain text fallback

### Translations
All text is localized in Italian using the `TRANSLATIONS_IT` dictionary:
- Alert titles and messages
- Field labels and descriptions
- Action prompts and instructions

## Files Created/Modified

### New Files
- `templates/emails/overbooking_alert.html`
- `templates/emails/overbooking_alert.txt`
- `apps/booking/tests/test_overbooking_email.py`

### Modified Files
- `apps/booking/signals.py` - Added overbooking detection logic
- `services/email/email_service.py` - Added `send_overbooking_alert` method
- `assets/translations.py` - Added Italian translations

## Usage

The system works automatically once deployed. No manual intervention is required for detection.

### Testing
```bash
# Run overbooking-specific tests
python manage.py test apps.booking.tests.test_overbooking_email

# Run all booking tests
python manage.py test apps.booking.tests
```

### Manual Testing
To test the system manually:
1. Create a property with bookings
2. Create overlapping non-manual bookings via API
3. Check logs and email delivery

## Configuration

### Settings Required
- `ADMIN_URL` - Base URL for admin panel links
- `DEFAULT_FROM_EMAIL` - Email sender configuration
- Email backend configuration for sending

### Logging
All overbooking events are logged with structured data:
- Property information
- Booking IDs involved
- Overlap dates
- Success/failure of email delivery

## Error Handling

- Graceful degradation if email sending fails
- Comprehensive logging for troubleshooting
- Database transaction safety
- Retry logic for email delivery (built into EmailService)

## Future Enhancements

Potential improvements:
- Configurable recipient lists
- Different alert levels based on overlap severity
- Integration with notification systems
- Dashboard widgets for overbooking trends
- Automatic resolution suggestions
