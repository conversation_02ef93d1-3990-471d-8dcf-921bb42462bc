from datetime import timedelta

from django.db import models
from django.utils.timezone import now


class Notification(models.Model):
    user = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, related_name="notifications"
    )
    title = models.CharField(max_length=255)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Notification for {self.user.name} - {self.title}"

    @classmethod
    def create_notification(cls, user, title, message):
        notification = cls.objects.create(user=user, title=title, message=message)
        return notification

    @staticmethod
    def delete_old_notifications():
        # Delete notifications older than a month
        cutoff_date = now() - timedelta(days=30)
        Notification.objects.filter(created_at__lt=cutoff_date).delete()
