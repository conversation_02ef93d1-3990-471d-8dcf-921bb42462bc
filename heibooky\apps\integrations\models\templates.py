from django.db import models


class DownloadableTemplate(models.Model):
    TEMPLATE_CATEGORIES = (
        ("checkin", "Check-in"),
        ("Legal & Compliance", "Legale e Conformità"),
        ("Terms & Conditions", "Termini e Condizioni"),
        ("Privacy Policy", "Politica sulla Privacy"),
        ("Marketing & Communication", "Marketing e Comunicazione"),
        ("Branding & Design", "Marchio e Design"),
        ("Operations & Management", "Operazioni e Gestione"),
        ("Financial & Accounting", "Finanza e Contabilità"),
        ("Human Resources", "Risorse Umane"),
        ("Technology & IT", "Tecnologia e IT"),
        ("Safety & Security", "Sicurezza e Protezione"),
        ("Health & Well-being", "Salute e Benessere"),
        ("Environment & Sustainability", "Ambiente e Sostenibilità"),
        ("Guest Experience", "Esperienza Ospiti"),
        ("other", "Altro"),
    )

    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    file = models.FileField(upload_to="templates/")
    category = models.CharField(max_length=50, choices=TEMPLATE_CATEGORIES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title
