# Docker Compose Performance Optimization Override
# Use with: docker-compose -f docker-compose.yml -f monitoring/docker-compose.performance.yml up -d

version: '3.8'

services:
  prometheus:
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=${PROMETHEUS_RETENTION_TIME:-15d}'
      - '--storage.tsdb.retention.size=${PROMETHEUS_RETENTION_SIZE:-10GB}'
      - '--storage.tsdb.wal-compression'
      - '--storage.tsdb.min-block-duration=2h'
      - '--storage.tsdb.max-block-duration=36h'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api=${PROMETHEUS_WEB_ENABLE_ADMIN_API:-false}'
      - '--web.max-connections=${PROMETHEUS_WEB_MAX_CONNECTIONS:-512}'
      - '--query.timeout=${PROMETHEUS_QUERY_TIMEOUT:-2m}'
      - '--query.max-concurrency=${PROMETHEUS_QUERY_MAX_CONCURRENCY:-20}'
      - '--query.max-samples=50000000'
      - '--query.lookback-delta=5m'
      - '--log.level=info'
      - '--log.format=json'
    environment:
      - PROMETHEUS_RETENTION_TIME=${PROMETHEUS_RETENTION_TIME:-15d}
      - PROMETHEUS_RETENTION_SIZE=${PROMETHEUS_RETENTION_SIZE:-10GB}
      - PROMETHEUS_QUERY_TIMEOUT=${PROMETHEUS_QUERY_TIMEOUT:-2m}
      - PROMETHEUS_QUERY_MAX_CONCURRENCY=${PROMETHEUS_QUERY_MAX_CONCURRENCY:-20}
      - PROMETHEUS_WEB_MAX_CONNECTIONS=${PROMETHEUS_WEB_MAX_CONNECTIONS:-512}
      - PROMETHEUS_WEB_ENABLE_ADMIN_API=${PROMETHEUS_WEB_ENABLE_ADMIN_API:-false}
    deploy:
      resources:
        limits:
          memory: ${PROMETHEUS_MEMORY_LIMIT:-2G}
          cpus: '${PROMETHEUS_CPU_LIMIT:-2.0}'
        reservations:
          memory: ${PROMETHEUS_MEMORY_REQUEST:-1G}
          cpus: '${PROMETHEUS_CPU_REQUEST:-0.5}'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  grafana:
    environment:
      # Performance optimizations
      - GF_DATABASE_WAL=${GF_DATABASE_WAL:-true}
      - GF_DATABASE_CACHE_MODE=${GF_DATABASE_CACHE_MODE:-shared}
      - GF_DATABASE_MAX_IDLE_CONN=${GF_DATABASE_MAX_IDLE_CONN:-10}
      - GF_DATABASE_MAX_OPEN_CONN=${GF_DATABASE_MAX_OPEN_CONN:-100}
      - GF_DATABASE_CONN_MAX_LIFETIME=${GF_DATABASE_CONN_MAX_LIFETIME:-14400}
      - GF_DATABASE_QUERY_RETRIES=${GF_DATABASE_QUERY_RETRIES:-3}
      - GF_DATABASE_QUERY_TIMEOUT=${GF_DATABASE_QUERY_TIMEOUT:-30s}
      
      # Caching
      - GF_CACHING_ENABLED=${GF_CACHING_ENABLED:-true}
      
      # Data proxy optimizations
      - GF_DATAPROXY_TIMEOUT=${GF_DATAPROXY_TIMEOUT:-30}
      - GF_DATAPROXY_DIAL_TIMEOUT=${GF_DATAPROXY_DIAL_TIMEOUT:-10}
      - GF_DATAPROXY_KEEP_ALIVE_SECONDS=${GF_DATAPROXY_KEEP_ALIVE_SECONDS:-30}
      - GF_DATAPROXY_MAX_IDLE_CONNECTIONS=${GF_DATAPROXY_MAX_IDLE_CONNECTIONS:-100}
      - GF_DATAPROXY_MAX_IDLE_CONNECTIONS_PER_HOST=${GF_DATAPROXY_MAX_IDLE_CONNECTIONS_PER_HOST:-10}
      - GF_DATAPROXY_IDLE_CONN_TIMEOUT=${GF_DATAPROXY_IDLE_CONN_TIMEOUT:-90}
      - GF_DATAPROXY_TLS_HANDSHAKE_TIMEOUT=${GF_DATAPROXY_TLS_HANDSHAKE_TIMEOUT:-10}
      - GF_DATAPROXY_EXPECT_CONTINUE_TIMEOUT=${GF_DATAPROXY_EXPECT_CONTINUE_TIMEOUT:-1}
      
      # Rendering optimizations
      - GF_RENDERING_CONCURRENT_RENDER_REQUEST_LIMIT=${GF_RENDERING_CONCURRENT_RENDER_REQUEST_LIMIT:-30}
      - GF_RENDERING_RENDERING_TIMEOUT=${GF_RENDERING_RENDERING_TIMEOUT:-20s}
      
      # Alerting optimizations
      - GF_ALERTING_CONCURRENT_RENDER_LIMIT=${GF_ALERTING_CONCURRENT_RENDER_LIMIT:-5}
      - GF_ALERTING_EVALUATION_TIMEOUT_SECONDS=${GF_ALERTING_EVALUATION_TIMEOUT_SECONDS:-30}
      - GF_ALERTING_NOTIFICATION_TIMEOUT_SECONDS=${GF_ALERTING_NOTIFICATION_TIMEOUT_SECONDS:-30}
      - GF_ALERTING_MAX_ATTEMPTS=${GF_ALERTING_MAX_ATTEMPTS:-3}
      
      # Unified alerting optimizations
      - GF_UNIFIED_ALERTING_MAX_ATTEMPTS=${GF_UNIFIED_ALERTING_MAX_ATTEMPTS:-3}
      - GF_UNIFIED_ALERTING_MIN_INTERVAL=${GF_UNIFIED_ALERTING_MIN_INTERVAL:-10s}
      
      # Live features
      - GF_LIVE_MAX_CONNECTIONS=${GF_LIVE_MAX_CONNECTIONS:-100}
      
      # Metrics
      - GF_METRICS_ENABLED=${GF_METRICS_ENABLED:-true}
      - GF_METRICS_INTERVAL_SECONDS=${GF_METRICS_INTERVAL_SECONDS:-10}
      
      # Feature toggles for performance
      - GF_FEATURE_TOGGLES_ENABLE=${GF_FEATURE_TOGGLES_ENABLE:-ngalert,publicDashboards}
    deploy:
      resources:
        limits:
          memory: ${GRAFANA_MEMORY_LIMIT:-1G}
          cpus: '${GRAFANA_CPU_LIMIT:-1.0}'
        reservations:
          memory: ${GRAFANA_MEMORY_REQUEST:-512M}
          cpus: '${GRAFANA_CPU_REQUEST:-0.25}'
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  loki:
    environment:
      # Performance environment variables
      - LOKI_INGESTION_RATE_MB=${LOKI_INGESTION_RATE_MB:-4}
      - LOKI_INGESTION_BURST_SIZE_MB=${LOKI_INGESTION_BURST_SIZE_MB:-6}
      - LOKI_MAX_QUERY_PARALLELISM=${LOKI_MAX_QUERY_PARALLELISM:-32}
      - LOKI_QUERY_TIMEOUT=${LOKI_QUERY_TIMEOUT:-1m}
      - LOKI_MAX_ENTRIES_LIMIT=${LOKI_MAX_ENTRIES_LIMIT:-5000}
      - LOKI_SPLIT_QUERIES_BY_INTERVAL=${LOKI_SPLIT_QUERIES_BY_INTERVAL:-30m}
      - LOKI_CACHE_RESULTS=${LOKI_CACHE_RESULTS:-true}
      - LOKI_RESULTS_CACHE_MAX_SIZE_MB=${LOKI_RESULTS_CACHE_MAX_SIZE_MB:-500}
      - LOKI_RESULTS_CACHE_TTL=${LOKI_RESULTS_CACHE_TTL:-1h}
      - LOKI_CHUNK_IDLE_PERIOD=${LOKI_CHUNK_IDLE_PERIOD:-1h}
      - LOKI_MAX_CHUNK_AGE=${LOKI_MAX_CHUNK_AGE:-1h}
      - LOKI_CHUNK_TARGET_SIZE=${LOKI_CHUNK_TARGET_SIZE:-1048576}
      - LOKI_COMPACTION_INTERVAL=${LOKI_COMPACTION_INTERVAL:-10m}
      - LOKI_RETENTION_PERIOD=${LOKI_RETENTION_PERIOD:-336h}
    deploy:
      resources:
        limits:
          memory: ${LOKI_MEMORY_LIMIT:-2G}
          cpus: '${LOKI_CPU_LIMIT:-1.5}'
        reservations:
          memory: ${LOKI_MEMORY_REQUEST:-1G}
          cpus: '${LOKI_CPU_REQUEST:-0.5}'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3100/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  alertmanager:
    environment:
      # Performance optimizations
      - AM_CLUSTER_PEER_TIMEOUT=${AM_CLUSTER_PEER_TIMEOUT:-15s}
      - AM_CLUSTER_GOSSIP_INTERVAL=${AM_CLUSTER_GOSSIP_INTERVAL:-200ms}
      - AM_CLUSTER_PUSH_PULL_INTERVAL=${AM_CLUSTER_PUSH_PULL_INTERVAL:-60s}
      - AM_DATA_RETENTION=${AM_DATA_RETENTION:-120h}
    deploy:
      resources:
        limits:
          memory: ${ALERTMANAGER_MEMORY_LIMIT:-512M}
          cpus: '${ALERTMANAGER_CPU_LIMIT:-0.5}'
        reservations:
          memory: ${ALERTMANAGER_MEMORY_REQUEST:-256M}
          cpus: '${ALERTMANAGER_CPU_REQUEST:-0.1}'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9093/-/healthy || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optimized exporters
  redis-exporter:
    deploy:
      resources:
        limits:
          memory: ${REDIS_EXPORTER_MEMORY_LIMIT:-128M}
          cpus: '${REDIS_EXPORTER_CPU_LIMIT:-0.2}'
        reservations:
          memory: ${REDIS_EXPORTER_MEMORY_REQUEST:-64M}
          cpus: '${REDIS_EXPORTER_CPU_REQUEST:-0.05}'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9121/metrics || exit 1"]
      interval: 60s
      timeout: 10s
      retries: 3

  node-exporter:
    deploy:
      resources:
        limits:
          memory: ${NODE_EXPORTER_MEMORY_LIMIT:-128M}
          cpus: '${NODE_EXPORTER_CPU_LIMIT:-0.2}'
        reservations:
          memory: ${NODE_EXPORTER_MEMORY_REQUEST:-64M}
          cpus: '${NODE_EXPORTER_CPU_REQUEST:-0.05}'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9100/metrics || exit 1"]
      interval: 60s
      timeout: 10s
      retries: 3

  cadvisor:
    deploy:
      resources:
        limits:
          memory: ${CADVISOR_MEMORY_LIMIT:-256M}
          cpus: '${CADVISOR_CPU_LIMIT:-0.3}'
        reservations:
          memory: ${CADVISOR_MEMORY_REQUEST:-128M}
          cpus: '${CADVISOR_CPU_REQUEST:-0.1}'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/healthz || exit 1"]
      interval: 60s
      timeout: 10s
      retries: 3

  promtail:
    deploy:
      resources:
        limits:
          memory: ${PROMTAIL_MEMORY_LIMIT:-128M}
          cpus: '${PROMTAIL_CPU_LIMIT:-0.2}'
        reservations:
          memory: ${PROMTAIL_MEMORY_REQUEST:-64M}
          cpus: '${PROMTAIL_CPU_REQUEST:-0.05}'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9080/ready || exit 1"]
      interval: 60s
      timeout: 10s
      retries: 3

# Performance-optimized networks
networks:
  monitoring:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-monitoring
      com.docker.network.driver.mtu: 1500
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-backend
      com.docker.network.driver.mtu: 1500
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

# Performance-optimized volumes
volumes:
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PROMETHEUS_DATA_PATH:-./monitoring/data/prometheus}
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${GRAFANA_DATA_PATH:-./monitoring/data/grafana}
  
  alertmanager_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${ALERTMANAGER_DATA_PATH:-./monitoring/data/alertmanager}
  
  loki_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOKI_DATA_PATH:-./monitoring/data/loki}
