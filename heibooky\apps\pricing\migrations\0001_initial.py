# Generated by Django 5.1.2 on 2025-06-28 07:58

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="RatePlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        help_text="Name of the rate plan.", max_length=255
                    ),
                ),
                (
                    "close_out_days",
                    models.IntegerField(
                        blank=True,
                        help_text="Number of days before check-in when bookings are closed (0-30).",
                        null=True,
                    ),
                ),
                (
                    "checkin_time",
                    models.TimeField(
                        blank=True,
                        help_text="Time of day when guests can check in.",
                        null=True,
                    ),
                ),
                (
                    "close_out_time",
                    models.TimeField(
                        blank=True,
                        help_text="Time of day when bookings are closed (required if close_out_days is 0).",
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        help_text="Description of the rate plan.", max_length=300
                    ),
                ),
                (
                    "meal_plan",
                    models.IntegerField(
                        choices=[
                            (1, "All inclusive"),
                            (2, "Breakfast"),
                            (3, "Lunch"),
                            (4, "Dinner"),
                            (5, "American"),
                            (6, "Bed & breakfast"),
                            (7, "Buffet breakfast"),
                            (8, "Caribbean breakfast"),
                            (9, "Continental breakfast"),
                            (10, "English breakfast"),
                            (11, "European plan"),
                            (12, "Family plan"),
                            (13, "Full board"),
                            (14, "Half board/modified American plan"),
                            (15, "Room only (Default)"),
                            (16, "Self catering"),
                            (17, "Bermuda"),
                            (18, "Dinner bed and breakfast plan"),
                            (19, "Family American"),
                            (20, "Modified"),
                            (21, "Breakfast & lunch"),
                            (22, "Full breakfast"),
                        ],
                        default=15,
                        help_text="Select the meal plan.",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_onboarded", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="RoomRate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("rate", models.DecimalField(decimal_places=2, max_digits=10)),
                ("room_amount", models.IntegerField()),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "minimum_stay",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(730),
                        ]
                    ),
                ),
                (
                    "maximum_stay",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(730),
                        ]
                    ),
                ),
                ("is_season", models.BooleanField()),
                ("ticket_id", models.CharField(blank=True, null=True)),
                ("is_onboarded", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
