<?xml version="1.0" encoding="UTF-8"?>
<p:FatturaElettronica versione="FPR12" 
    xmlns:ds="http://www.w3.org/2000/09/xmldsig#" 
    xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2 http://www.fatturapa.gov.it/export/fatturazione/sdi/fatturapa/v1.2/Schema_del_file_xsd_FatturaPA_versione_1.2.xsd">
    
    <FatturaElettronicaHeader>
        <DatiTrasmissione>
            <IdTrasmittente>
                <IdPaese>IT</IdPaese>
                <IdCodice>{{ COMPANY_VAT }}</IdCodice>
            </IdTrasmittente>
            <ProgressivoInvio>{{ progressive_number }}</ProgressivoInvio>
            <FormatoTrasmissione>FPR12</FormatoTrasmissione>
            <CodiceDestinatario>0000000</CodiceDestinatario>
        </DatiTrasmissione>
        
        <CedentePrestatore>
            <DatiAnagrafici>
                <IdFiscaleIVA>
                    <IdPaese>IT</IdPaese>
                    <IdCodice>{{ COMPANY_VAT }}</IdCodice>
                </IdFiscaleIVA>
                <Anagrafica>
                    <Denominazione>{{ COMPANY_NAME }}</Denominazione>
                </Anagrafica>
                <RegimeFiscale>RF01</RegimeFiscale>
            </DatiAnagrafici>
            <Sede>
                <Indirizzo>{{ COMPANY_ADDRESS }}</Indirizzo>
                <NumeroCivico>SNC</NumeroCivico>
                <CAP>{{ COMPANY_ZIP }}</CAP>
                <Comune>{{ COMPANY_CITY }}</Comune>
                <Nazione>IT</Nazione>
            </Sede>
        </CedentePrestatore>
        
        <CessionarioCommittente>
            <DatiAnagrafici>
                <CodiceFiscale> </CodiceFiscale>
                <Anagrafica>
                    <Nome>{{ booking.customer.first_name }}</Nome>
                    <Cognome>{{ booking.customer.last_name }}</Cognome>
                </Anagrafica>
            </DatiAnagrafici>
            <Sede>
                <Indirizzo>{{ booking.customer.address }}</Indirizzo>
                <NumeroCivico>SNC</NumeroCivico>
                <CAP>{{ booking.customer.zip_code }}</CAP>
                <Comune>{{ booking.customer.city }}</Comune>
                <Nazione>IT</Nazione>
            </Sede>
        </CessionarioCommittente>
    </FatturaElettronicaHeader>
    
    <FatturaElettronicaBody>
        <DatiGenerali>
            <DatiGeneraliDocumento>
                <TipoDocumento>TD01</TipoDocumento>
                <Divisa>EUR</Divisa>
                <Data>{% now "Y-m-d" %}</Data>
                <Numero>{{ progressive_number }}</Numero>
                <ImportoTotaleDocumento>{{ total_amount|floatformat:2 }}</ImportoTotaleDocumento>
            </DatiGeneraliDocumento>
        </DatiGenerali>
        
        <DatiBeniServizi>
            <DettaglioLinee>
                <NumeroLinea>1</NumeroLinea>
                <Descrizione>Affitto struttura {{ booking.property.name }}</Descrizione>
                <Quantita>1.00</Quantita>
                <UnitaMisura>N.</UnitaMisura>
                <PrezzoUnitario>{{ taxable_amount|floatformat:2 }}</PrezzoUnitario>
                <PrezzoTotale>{{ taxable_amount|floatformat:2 }}</PrezzoTotale>
                <AliquotaIVA>22.00</AliquotaIVA>
            </DettaglioLinee>
            
            <DatiRiepilogo>
                <AliquotaIVA>22.00</AliquotaIVA>
                <ImponibileImporto>{{ taxable_amount|floatformat:2 }}</ImponibileImporto>
                <Imposta>{{ vat_amount|floatformat:2 }}</Imposta>
                <EsigibilitaIVA>I</EsigibilitaIVA>
            </DatiRiepilogo>
        </DatiBeniServizi>
        
        <DatiPagamento>
            <CondizioniPagamento>TP02</CondizioniPagamento>
            <DettaglioPagamento>
                <ModalitaPagamento>MP05</ModalitaPagamento>
                <DataScadenzaPagamento>{% now "Y-m-d" %}</DataScadenzaPagamento>
                <ImportoPagamento>{{ total_amount|floatformat:2 }}</ImportoPagamento>
            </DettaglioPagamento>
        </DatiPagamento>
    </FatturaElettronicaBody>
</p:FatturaElettronica>