from django.contrib import admin
from django.core.exceptions import ValidationError
from django.utils import timezone

from .models import (
    Amenity,
    GuestArrivalInfo,
    Location,
    Photo,
    Property,
    PropertyAmenity,
    PropertyMetadata,
    PropertyOwnership,
    PropertyPermission,
    Room,
    RoomAmenity,
    StaffRole,
    TeamInvite,
)


class PhotoInline(admin.TabularInline):
    model = Photo
    extra = 1
    fields = ("image", "description", "is_onboarded")
    readonly_fields = ("is_onboarded",)


class RoomInline(admin.TabularInline):
    model = Room
    extra = 1
    fields = (
        "room_type",
        "room_rate",
        "max_occupancy",
        "max_child_occupancy",
        "quantity",
        "is_active",
        "is_onboarded",
        "bathroom_quntity",
    )
    readonly_fields = ("created_at", "updated_at")


class PropertyOwnershipInline(admin.TabularInline):
    model = PropertyOwnership
    extra = 1
    fields = ("user", "relation_type", "created_at")
    readonly_fields = ("created_at",)


class PropertyMetadataInline(admin.StackedInline):
    model = PropertyMetadata
    extra = 0
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (
            None,
            {"fields": ("check_in_time", "check_out_time", "cancelation_policy_type")},
        ),
        ("Licenses", {"fields": ("regional_id_code",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "hotel_id",
        "chain_id",
        "property_type",
        "location",
        "is_active",
        "is_onboarded",
    )
    list_filter = (
        "property_type",
        "is_active",
        "is_onboarded",
        "is_multi_unit",
        "created_at",
    )
    search_fields = ("name", "hotel_id", "chain_id", "description")
    readonly_fields = ("hotel_id", "chain_id", "created_at", "updated_at")
    inlines = [PropertyOwnershipInline, RoomInline, PhotoInline, PropertyMetadataInline]
    ordering = ("-created_at",)
    filter_horizontal = ("staffs",)
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "property_type",
                    "description",
                    "location",
                    "cover_image",
                    "staffs",
                )
            },
        ),
        ("Property Details", {"fields": ("hotel_id", "chain_id", "is_multi_unit")}),
        ("Status", {"fields": ("is_active", "is_onboarded")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )
    actions = ["make_onboarded", "make_not_onboarded"]

    def make_onboarded(self, request, queryset):
        queryset.update(is_onboarded=True)

    make_onboarded.short_description = "Mark selected properties as onboarded"

    def make_not_onboarded(self, request, queryset):
        queryset.update(is_onboarded=False)

    make_not_onboarded.short_description = "Mark selected properties as not onboarded"


@admin.register(PropertyPermission)
class PropertyPermissionAdmin(admin.ModelAdmin):
    list_display = ("name", "description")
    search_fields = ("name", "description")
    ordering = ("name",)


class StaffRolePermissionInline(admin.TabularInline):
    model = StaffRole.permissions.through
    extra = 1


@admin.register(StaffRole)
class StaffRoleAdmin(admin.ModelAdmin):
    list_display = ("user", "property", "is_active", "created_at", "updated_at")
    list_filter = ("is_active", "property", "created_at")
    search_fields = ("user__email", "property__name")
    readonly_fields = ("created_at", "updated_at")
    inlines = [StaffRolePermissionInline]
    exclude = ("permissions",)
    ordering = ("-created_at",)

    fieldsets = (
        (None, {"fields": ("property", "user", "is_active")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def save_model(self, request, obj, form, change):
        try:
            obj.clean()  # This will run our custom validation
            super().save_model(request, obj, form, change)
        except ValidationError as e:
            form.add_error(None, e.message_dict)
            return

    def formfield_for_manytomany(self, db_field, request, **kwargs):
        if db_field.name == "permissions":
            kwargs["widget"] = admin.widgets.FilteredSelectMultiple(
                db_field.verbose_name, is_stacked=False
            )
        return super().formfield_for_manytomany(db_field, request, **kwargs)


@admin.register(TeamInvite)
class TeamInviteAdmin(admin.ModelAdmin):
    list_display = (
        "email",
        "property",
        "invited_by",
        "created_at",
        "expires_at",
        "accepted",
        "accepted_at",
        "is_expired",
    )
    list_filter = ("accepted", "property", "created_at")
    search_fields = ("email", "property__name", "invited_by__email")
    readonly_fields = ("id", "created_at")
    ordering = ("-created_at",)
    date_hierarchy = "created_at"

    fieldsets = (
        (
            "Invite Details",
            {"fields": ("id", "user", "email", "property", "invited_by")},
        ),
        (
            "Status Information",
            {"fields": ("accepted", "is_registered", "accepted_at", "expires_at")},
        ),
        ("Timestamps", {"fields": ("created_at",), "classes": ("collapse",)}),
    )

    def is_expired(self, obj):
        return obj.is_expired()

    is_expired.boolean = True
    is_expired.short_description = "Expired"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("property", "invited_by")

    def save_model(self, request, obj, form, change):
        if "accepted" in form.changed_data and obj.accepted:
            obj.accepted_at = timezone.now()
        super().save_model(request, obj, form, change)


@admin.register(PropertyMetadata)
class PropertyMetadataAdmin(admin.ModelAdmin):
    list_display = (
        "property",
        "check_in_time",
        "check_out_time",
        "cancelation_policy_type",
    )
    list_filter = ("cancelation_policy_type", "created_at")
    search_fields = ("property__name", "regional_id_code")
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "property",
                    "check_in_time",
                    "check_out_time",
                    "close_out_days",
                    "close_out_time",
                )
            },
        ),
        ("Cancelation Policy", {"fields": ("cancelation_policy_type",)}),
        ("Identification", {"fields": ("regional_id_code",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = (
        "get_room_type_display",
        "property",
        "room_rate",
        "max_occupancy",
        "quantity",
        "is_active",
        "is_onboarded",
    )
    list_filter = ("room_type", "is_active", "is_onboarded", "created_at")
    search_fields = ("property__name", "description")
    readonly_fields = ("created_at", "updated_at")
    inlines = [PhotoInline]
    ordering = ("property", "room_type")
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "property",
                    "room_type",
                    "room_rate",
                    "description",
                    "bed_config",
                    "bathroom_quntity",
                )
            },
        ),
        ("Occupancy", {"fields": ("max_occupancy", "max_child_occupancy", "quantity")}),
        ("Size", {"fields": ("size_measurement", "size_measurement_unit")}),
        ("Status", {"fields": ("is_active", "is_onboarded")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )
    actions = ["make_onboarded", "make_not_onboarded"]

    def make_onboarded(self, request, queryset):
        queryset.update(is_onboarded=True)

    make_onboarded.short_description = "Mark selected rooms as onboarded"

    def make_not_onboarded(self, request, queryset):
        queryset.update(is_onboarded=False)

    make_not_onboarded.short_description = "Mark selected rooms as not onboarded"


@admin.register(PropertyOwnership)
class PropertyOwnershipAdmin(admin.ModelAdmin):
    list_display = ("user", "property", "relation_type", "created_at")
    list_filter = ("relation_type", "created_at")
    search_fields = ("user__name", "property__name")
    ordering = ("-created_at",)


@admin.register(RoomAmenity)
class RoomAmenityAdmin(admin.ModelAdmin):
    list_display = ("room", "amenity", "is_available")
    list_filter = ("is_available",)
    search_fields = ("room__description", "room__property__name")
    ordering = ("amenity", "room")
    fieldsets = ((None, {"fields": ("room", "amenity", "is_available")}),)


@admin.register(Photo)
class PhotoAdmin(admin.ModelAdmin):
    list_display = ("get_related_name", "description", "is_onboarded")
    list_filter = ("is_onboarded",)
    search_fields = ("description", "property__name")

    def get_related_name(self, obj):
        return obj.property.name if obj.property else obj.room.get_room_type_display()

    get_related_name.short_description = "Related To"

    actions = ["make_onboarded", "make_not_onboarded"]

    def make_onboarded(self, request, queryset):
        queryset.update(is_onboarded=True)

    make_onboarded.short_description = "Mark selected photos as onboarded"

    def make_not_onboarded(self, request, queryset):
        queryset.update(is_onboarded=False)

    make_not_onboarded.short_description = "Mark selected photos as not onboarded"


@admin.register(GuestArrivalInfo)
class GuestArrivalInfoAdmin(admin.ModelAdmin):
    list_display = (
        "property",
        "contact_name",
        "contact_surname",
        "email",
        "phone_number",
    )
    search_fields = (
        "property__name",
        "contact_name",
        "contact_surname",
        "email",
        "phone_number",
    )
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("property",)}),
        (
            "Contact Information",
            {"fields": ("contact_name", "contact_surname", "email", "phone_number")},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(Amenity)
class AmenityAdmin(admin.ModelAdmin):
    list_display = ("name", "category")
    list_filter = ("category",)
    search_fields = ("name", "category")
    ordering = ("category", "name")
    fieldsets = ((None, {"fields": ("name", "category")}),)


@admin.register(PropertyAmenity)
class PropertyAmenityAdmin(admin.ModelAdmin):
    list_display = ("property", "amenity", "is_available")
    list_filter = ("amenity", "is_available")
    search_fields = ("property__name", "amenity__name")
    ordering = ("amenity", "property")
    fieldsets = ((None, {"fields": ("property", "amenity", "is_available")}),)


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = (
        "street",
        "city",
        "country",
        "post_code",
        "is_editable",
        "latitude",
        "longitude",
    )
    list_filter = ("country", "city")
    search_fields = ("street", "city", "country", "post_code")
    readonly_fields = ("id",)
    fieldsets = (
        (None, {"fields": ("street", "post_code", "is_editable")}),
        ("Area Information", {"fields": ("city", "country")}),
        ("Coordinates", {"fields": ("latitude", "longitude")}),
    )
