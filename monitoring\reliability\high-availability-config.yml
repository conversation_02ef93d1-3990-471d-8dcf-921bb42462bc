# High Availability Configuration for Heibooky Monitoring Stack
# This configuration provides comprehensive reliability and error handling

# Prometheus High Availability Configuration
prometheus:
  # Clustering and federation
  clustering:
    enabled: true
    replicas: 2
    federation:
      enabled: true
      global_config:
        scrape_interval: 15s
        evaluation_interval: 15s
        external_labels:
          cluster: 'heibooky-production'
          replica: 'replica-1'  # Set to 'replica-1', 'replica-2', etc. for each instance
    
  # Storage reliability
  storage:
    retention:
      time: "15d"
      size: "10GB"
    wal:
      compression: true
      segment_duration: "2h"
    backup:
      enabled: true
      schedule: "0 2 * * *"  # Daily at 2 AM
      retention: "30d"
      storage_type: "local"  # local, s3, gcs, azure
      
  # Query reliability
  query:
    timeout: "2m"
    max_concurrency: 20
    max_samples: 50000000
    lookback_delta: "5m"
    
  # Error handling
  error_handling:
    max_retries: 3
    retry_interval: "30s"
    circuit_breaker:
      enabled: true
      failure_threshold: 5
      recovery_timeout: "60s"
    
  # Health checks
  health:
    endpoint: "/-/healthy"
    readiness_endpoint: "/-/ready"
    interval: "30s"
    timeout: "10s"
    retries: 3

# Grafana High Availability Configuration
grafana:
  # Database reliability
  database:
    type: "postgres"  # Use PostgreSQL for HA
    host: "postgres-primary"
    port: 5432
    name: "grafana"
    ssl_mode: "require"
    connection_pool:
      max_idle: 25
      max_open: 300
      max_lifetime: "14400s"
    backup:
      enabled: true
      schedule: "0 3 * * *"  # Daily at 3 AM
      retention: "30d"
      
  # Session management
  session:
    provider: "redis"  # Use Redis for session clustering
    config: "addr=redis-cluster:6379,pool_size=100,db=grafana"
    cookie_secure: true
    cookie_same_site: "strict"
    
  # Error handling
  error_handling:
    max_retries: 3
    retry_interval: "5s"
    timeout: "30s"
    circuit_breaker:
      enabled: true
      failure_threshold: 10
      recovery_timeout: "120s"
      
  # Load balancing
  load_balancing:
    enabled: true
    replicas: 2
    strategy: "round_robin"
    health_check:
      path: "/api/health"
      interval: "30s"
      timeout: "10s"
      
  # Backup and recovery
  backup:
    dashboards:
      enabled: true
      schedule: "0 1 * * *"  # Daily at 1 AM
      retention: "90d"
      include_data_sources: true
    settings:
      enabled: true
      schedule: "0 4 * * *"  # Daily at 4 AM
      retention: "30d"

# Loki High Availability Configuration
loki:
  # Clustering
  clustering:
    enabled: true
    replicas: 3
    memberlist:
      bind_port: 7946
      join_members:
        - "loki-1:7946"
        - "loki-2:7946"
        - "loki-3:7946"
        
  # Storage reliability
  storage:
    type: "s3"  # Use object storage for HA
    s3:
      bucket: "heibooky-loki-chunks"
      region: "us-east-1"
      access_key_id: "${AWS_ACCESS_KEY_ID}"
      secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
    retention:
      enabled: true
      period: "336h"  # 14 days
      
  # Ingestion reliability
  ingestion:
    rate_limit: "4MB"
    burst_size: "6MB"
    max_line_size: "256KB"
    timeout: "30s"
    retries: 3
    
  # Query reliability
  query:
    timeout: "1m"
    max_concurrent: 32
    max_entries: 5000
    split_interval: "30m"
    cache:
      enabled: true
      ttl: "1h"
      max_size: "500MB"
      
  # Error handling
  error_handling:
    max_retries: 3
    retry_interval: "10s"
    circuit_breaker:
      enabled: true
      failure_threshold: 5
      recovery_timeout: "60s"

# Alertmanager High Availability Configuration
alertmanager:
  # Clustering
  clustering:
    enabled: true
    replicas: 3
    listen_address: "0.0.0.0:9094"
    peers:
      - "alertmanager-1:9094"
      - "alertmanager-2:9094"
      - "alertmanager-3:9094"
    gossip_interval: "200ms"
    push_pull_interval: "60s"
    peer_timeout: "15s"
    
  # Storage reliability
  storage:
    path: "/alertmanager/data"
    retention: "120h"  # 5 days
    backup:
      enabled: true
      schedule: "0 5 * * *"  # Daily at 5 AM
      retention: "30d"
      
  # Notification reliability
  notifications:
    timeout: "10s"
    retry_interval: "1m"
    max_retries: 3
    circuit_breaker:
      enabled: true
      failure_threshold: 5
      recovery_timeout: "300s"
      
  # Error handling
  error_handling:
    webhook_timeout: "30s"
    email_timeout: "30s"
    slack_timeout: "30s"
    dead_letter_queue:
      enabled: true
      retention: "24h"

# Network Reliability Configuration
network:
  # Connection pooling
  connection_pool:
    max_idle_connections: 100
    max_connections_per_host: 10
    idle_timeout: "90s"
    keep_alive: "30s"
    
  # Timeouts
  timeouts:
    dial: "10s"
    tls_handshake: "10s"
    response_header: "30s"
    expect_continue: "1s"
    
  # Retry configuration
  retry:
    max_attempts: 3
    initial_interval: "1s"
    max_interval: "30s"
    multiplier: 2.0
    
  # Circuit breaker
  circuit_breaker:
    enabled: true
    failure_threshold: 5
    success_threshold: 3
    timeout: "60s"

# Storage Reliability Configuration
storage:
  # Volume configuration
  volumes:
    prometheus:
      type: "persistent"
      size: "50Gi"
      storage_class: "fast-ssd"
      backup:
        enabled: true
        schedule: "0 2 * * *"
        retention: "30d"
        
    grafana:
      type: "persistent"
      size: "10Gi"
      storage_class: "standard"
      backup:
        enabled: true
        schedule: "0 3 * * *"
        retention: "30d"
        
    loki:
      type: "object_storage"
      provider: "s3"
      bucket: "heibooky-loki-data"
      backup:
        enabled: true
        schedule: "0 4 * * *"
        retention: "90d"
        
    alertmanager:
      type: "persistent"
      size: "5Gi"
      storage_class: "standard"
      backup:
        enabled: true
        schedule: "0 5 * * *"
        retention: "30d"

# Monitoring and Health Checks
monitoring:
  # Service health checks
  health_checks:
    prometheus:
      endpoint: "http://prometheus:9090/-/healthy"
      interval: "30s"
      timeout: "10s"
      retries: 3
      
    grafana:
      endpoint: "http://grafana:3000/api/health"
      interval: "30s"
      timeout: "10s"
      retries: 3
      
    loki:
      endpoint: "http://loki:3100/ready"
      interval: "30s"
      timeout: "10s"
      retries: 3
      
    alertmanager:
      endpoint: "http://alertmanager:9093/-/healthy"
      interval: "30s"
      timeout: "10s"
      retries: 3
      
  # Dependency health checks
  dependencies:
    redis:
      endpoint: "redis://redis:6379"
      command: "PING"
      interval: "30s"
      timeout: "5s"
      retries: 3
      
    postgres:
      endpoint: "postgres://postgres:5432/grafana"
      query: "SELECT 1"
      interval: "30s"
      timeout: "10s"
      retries: 3

# Disaster Recovery Configuration
disaster_recovery:
  # Backup strategy
  backup:
    full_backup:
      schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM
      retention: "12w"       # 12 weeks
      compression: true
      encryption: true
      
    incremental_backup:
      schedule: "0 2 * * 1-6"  # Daily except Sunday at 2 AM
      retention: "4w"          # 4 weeks
      compression: true
      
  # Recovery procedures
  recovery:
    rto: "1h"    # Recovery Time Objective
    rpo: "15m"   # Recovery Point Objective
    automated: true
    validation: true
    
  # Failover configuration
  failover:
    automatic: true
    threshold: "3 consecutive failures"
    cooldown: "5m"
    rollback: true
