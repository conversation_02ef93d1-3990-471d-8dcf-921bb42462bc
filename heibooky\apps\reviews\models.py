import uuid

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class Review(models.Model):
    """Model to store reviews received from SU API."""

    class ReviewerRole(models.TextChoices):
        HOST = "host", _("Host")
        GUEST = "guest", _("Guest")

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    external_review_id = models.CharField(
        max_length=255, unique=True, help_text="Review ID from OTA"
    )
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="reviews"
    )
    listing_id = models.CharField(
        max_length=255, null=True, blank=True, help_text="OTA Listing ID"
    )
    booking = models.ForeignKey(
        "booking.Booking", on_delete=models.SET_NULL, null=True, blank=True
    )

    # Review details
    public_review = models.TextField(blank=True, null=True)
    private_feedback = models.TextField(blank=True, null=True)
    ratings = models.JSO<PERSON>ield(default=dict, blank=True)

    # Reviewer information
    reviewer_id = models.Char<PERSON>ield(max_length=255)
    reviewer_role = models.CharField(max_length=10, choices=ReviewerRole.choices)
    reviewee_id = models.CharField(max_length=255)
    reviewee_role = models.CharField(max_length=10, choices=ReviewerRole.choices)

    # Metadata
    channel_id = models.CharField(max_length=10, help_text="OTA Channel ID")
    thread_id = models.CharField(max_length=255, blank=True, null=True)
    is_hidden = models.BooleanField(default=False)
    is_read = models.BooleanField(default=False)
    submitted_at = models.DateTimeField()
    expires_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    responses = models.JSONField(
        default=list, help_text="List of responses to the review"
    )

    def add_response(self, user, text):
        """Add a new response to the review."""
        response = {
            "text": text,
            "user_id": str(user.id),
            "user_name": user.name,
            "created_at": timezone.now().isoformat(),
        }

        if not self.responses:
            self.responses = []

        self.responses.append(response)
        return response

    def __str__(self):
        return f"Review for {self.property.name} by {self.reviewer_role}"

    class Meta:
        ordering = ["-submitted_at"]
