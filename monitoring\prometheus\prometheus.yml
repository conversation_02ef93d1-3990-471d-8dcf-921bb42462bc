# Prometheus Configuration - Performance Optimized
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s
  query_log_file: /prometheus/query.log
  external_labels:
    cluster: 'heibooky-production'
    environment: 'production'
    region: 'us-east-1'
    replica: 'prometheus-1'
  # Performance optimizations
  body_size_limit: "50MB"     # Reasonable limit on response body size
  sample_limit: 1000000       # Limit samples per scrape
  target_limit: 1000          # Limit targets per scrape
  label_limit: 1000           # Limit labels per scrape
  label_name_length_limit: 200
  label_value_length_limit: 500
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
      timeout: 10s
      api_version: v2

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    scrape_timeout: 10s
    metrics_path: /metrics
    honor_labels: false
    honor_timestamps: true
    scheme: http

  # Django application metrics - High frequency for critical metrics
  - job_name: 'django-app'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/monitoring/metrics/'
    scrape_interval: 10s  # More frequent for application metrics
    scrape_timeout: 8s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    sample_limit: 10000  # Limit samples per scrape
    params:
      format: ['prometheus']
    metric_relabel_configs:
      # Drop high-cardinality metrics that aren't essential
      - source_labels: [__name__]
        regex: 'django_http_requests_total'
        target_label: __tmp_keep
        replacement: 'yes'
      - source_labels: [__name__]
        regex: 'django_http_request_duration_seconds.*'
        target_label: __tmp_keep
        replacement: 'yes'
      - source_labels: [__name__]
        regex: 'django_db_.*'
        target_label: __tmp_keep
        replacement: 'yes'
      - source_labels: [__name__]
        regex: 'celery_.*'
        target_label: __tmp_keep
        replacement: 'yes'
      # Keep only essential metrics
      - source_labels: [__tmp_keep]
        regex: 'yes'
        action: keep
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: web:8000

  # Redis metrics - Optimized for cache performance
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 20s  # Less frequent for infrastructure
    scrape_timeout: 15s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    sample_limit: 5000
    metric_relabel_configs:
      # Keep only essential Redis metrics
      - source_labels: [__name__]
        regex: 'redis_(memory_used_bytes|connected_clients|commands_processed_total|keyspace_hits_total|keyspace_misses_total|up)'
        action: keep

  # PostgreSQL metrics (if needed) - Database performance focus
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s  # Database metrics change less frequently
    scrape_timeout: 20s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    sample_limit: 3000
    metric_relabel_configs:
      # Keep only essential PostgreSQL metrics
      - source_labels: [__name__]
        regex: 'pg_(up|stat_database_.*|locks_count|connections)'
        action: keep

  # Node exporter for system metrics - System performance focus
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 20s  # System metrics don't need high frequency
    scrape_timeout: 15s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    sample_limit: 8000
    metric_relabel_configs:
      # Keep only essential system metrics
      - source_labels: [__name__]
        regex: 'node_(cpu_seconds_total|memory_.*|filesystem_.*|network_.*|disk_.*|load.*|up)'
        action: keep
      # Drop unnecessary filesystem metrics
      - source_labels: [__name__, fstype]
        regex: 'node_filesystem_.*;(tmpfs|devtmpfs|proc|sys|cgroup.*)'
        action: drop

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Celery metrics
  - job_name: 'celery'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/monitoring/celery-metrics/'
    scrape_interval: 30s
    scrape_timeout: 15s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 15s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    metrics_path: /metrics

  # Alertmanager metrics
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Loki metrics
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true
