# Alertmanager Email Templates

This directory contains HTML email templates for Alertmanager notifications. These templates are used to format email alerts sent by the monitoring system.

## Template Files

- **slo-critical.tmpl** - Critical SLO violation alerts (highest priority)
- **business-critical.tmpl** - Business critical alerts affecting revenue/customers
- **revenue-impact.tmpl** - Revenue impact alerts for payment system issues
- **multi-window.tmpl** - Multi-window alerts for sustained issues
- **escalation.tmpl** - Escalated alerts requiring management attention
- **capacity.tmpl** - Capacity planning alerts
- **grouped.tmpl** - Grouped alerts to reduce noise
- **critical.tmpl** - General critical alerts
- **security.tmpl** - Security-related alerts
- **warning.tmpl** - Warning level alerts
- **info.tmpl** - Information alerts and daily digests

## Template Usage

Templates are loaded automatically by Alertmanager through the configuration in `alertmanager.yml`:

```yaml
templates:
  - '/etc/alertmanager/templates/*.tmpl'
  - './templates/*.tmpl'
```

In the receiver configurations, templates are referenced using:

```yaml
html: '{{ template "template-name.tmpl" . }}'
```

## Template Variables

All templates have access to the following Alertmanager variables:

- `{{ .Alerts }}` - Array of alert objects
- `{{ .GroupLabels }}` - Labels used for grouping
- `{{ .CommonAnnotations }}` - Common annotations across alerts
- `{{ .Status }}` - Alert status (firing/resolved)

### Alert Object Properties

- `{{ .Annotations.summary }}` - Alert summary
- `{{ .Annotations.description }}` - Alert description
- `{{ .Annotations.runbook_url }}` - Link to runbook
- `{{ .Labels.severity }}` - Alert severity level
- `{{ .Labels.instance }}` - Instance identifier
- `{{ .StartsAt }}` - Alert start time
- `{{ .EndsAt }}` - Alert end time (for resolved alerts)

## Styling

Each template includes inline CSS styling to ensure consistent rendering across email clients. The color schemes are chosen to reflect the severity and type of alert:

- **Red/Pink** - Critical/SLO violations
- **Orange/Yellow** - Business critical/escalations
- **Green** - Multi-window alerts
- **Blue** - Capacity planning
- **Gray** - Grouped alerts
- **Yellow** - Security/warnings
- **Light Blue** - Info alerts

## Maintenance

When updating templates:

1. Ensure HTML is valid and email-client compatible
2. Test template rendering with sample data
3. Maintain consistent styling across templates
4. Update this README if adding new templates
5. Restart Alertmanager to reload templates

## Best Practices

- Keep HTML simple and inline styles for email compatibility
- Use semantic color coding for different alert types
- Include all essential information without clutter
- Test templates with various email clients
- Maintain responsive design for mobile viewing
