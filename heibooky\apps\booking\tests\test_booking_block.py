import uuid
from datetime import timedelta

from apps.booking.models import Booking, BookingBlock
from apps.stay.models import Property, Room
from apps.users.models import User
from django.test import TestCase
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient


class BookingBlockTestCase(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", name="Test User"
        )

        # Create a test property
        self.property = Property.objects.create(
            name="Test Property",
            property_type=Property.HOTEL,
            location_id=1,  # Assuming a location with ID 1 exists
        )
        self.property.staffs.add(self.user)

        # Create a test room
        self.room = Room.objects.create(
            property=self.property,
            room_type="9",  # Double room
            room_rate=100.00,
            max_occupancy=2,
            max_child_occupancy=1,
            quantity=1,
        )

        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Set up dates for testing
        self.today = timezone.now().date()
        self.tomorrow = self.today + timedelta(days=1)
        self.day_after_tomorrow = self.today + timedelta(days=2)
        self.three_days_later = self.today + timedelta(days=3)
        self.four_days_later = self.today + timedelta(days=4)

    def test_create_booking_block(self):
        """Test creating a booking block"""
        url = "/api/booking-blocks/"
        data = {
            "property": self.property.id,
            "start_date": self.tomorrow.isoformat(),
            "end_date": self.three_days_later.isoformat(),
            "reason": "Test block",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(BookingBlock.objects.count(), 1)

    def test_booking_block_with_existing_booking(self):
        """Test creating a booking block that overlaps with an existing booking"""
        # Create a booking first
        booking = Booking.objects.create(
            property=self.property,
            customer_id=uuid.uuid4(),  # Assuming a customer exists
            reservation_data_id="test_reservation",  # Assuming a reservation exists
            is_manual=True,
            checkin_date=self.tomorrow,
            checkout_date=self.day_after_tomorrow,
            status="new",
        )

        # Try to create a booking block that overlaps with the booking
        url = "/api/booking-blocks/"
        data = {
            "property": self.property.id,
            "start_date": self.tomorrow.isoformat(),
            "end_date": self.three_days_later.isoformat(),
            "reason": "Test block",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(BookingBlock.objects.count(), 0)

    def test_booking_block_adjacent_to_booking(self):
        """Test creating a booking block that starts on the end date of a booking"""
        # Create a booking first
        booking = Booking.objects.create(
            property=self.property,
            customer_id=uuid.uuid4(),  # Assuming a customer exists
            reservation_data_id="test_reservation",  # Assuming a reservation exists
            is_manual=True,
            checkin_date=self.tomorrow,
            checkout_date=self.day_after_tomorrow,
            status="new",
        )

        # Create a booking block that starts on the end date of the booking
        url = "/api/booking-blocks/"
        data = {
            "property": self.property.id,
            "start_date": self.day_after_tomorrow.isoformat(),
            "end_date": self.three_days_later.isoformat(),
            "reason": "Test block",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(BookingBlock.objects.count(), 1)

    def test_booking_with_existing_block(self):
        """Test creating a booking that overlaps with an existing block"""
        # Create a booking block first
        block = BookingBlock.objects.create(
            property=self.property,
            start_date=self.tomorrow,
            end_date=self.three_days_later,
            reason="Test block",
        )

        # Try to create a booking that overlaps with the block
        url = "/api/bookings/"
        data = {
            "property": self.property.id,
            "customer": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "telephone": "1234567890",
            },
            "reservation_data": {
                "checkin_date": self.tomorrow.isoformat(),
                "checkout_date": self.day_after_tomorrow.isoformat(),
                "total_price": 200.00,
            },
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(Booking.objects.count(), 0)

    def test_booking_adjacent_to_block(self):
        """Test creating a booking that ends on the start date of a block"""
        # Create a booking block first
        block = BookingBlock.objects.create(
            property=self.property,
            start_date=self.day_after_tomorrow,
            end_date=self.three_days_later,
            reason="Test block",
        )

        # Create a booking that ends on the start date of the block
        url = "/api/bookings/"
        data = {
            "property": self.property.id,
            "customer": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "telephone": "1234567890",
            },
            "reservation_data": {
                "checkin_date": self.tomorrow.isoformat(),
                "checkout_date": self.day_after_tomorrow.isoformat(),
                "total_price": 200.00,
            },
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Booking.objects.count(), 1)

    def test_deactivate_booking_block(self):
        """Test deactivating a booking block"""
        # Create an active booking block
        block = BookingBlock.objects.create(
            property=self.property,
            start_date=self.today,
            end_date=self.three_days_later,
            reason="Test block",
            is_active=True,
        )

        # Deactivate all rooms
        self.room.is_active = False
        self.room.save()

        # Deactivate the booking block
        url = f"/api/booking-blocks/{block.id}/deactivate/"
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(BookingBlock.objects.count(), 0)

        # Check that the room has been reactivated
        self.room.refresh_from_db()
        self.assertTrue(self.room.is_active)
