import json
import logging
from typing import Any, Dict
from uuid import UUID

from apps.booking.models import Booking, BookingBlock
from apps.booking.tasks.inventory import (
    block_rooms_task,
    unblock_rooms_task,
    update_inventory_for_booking,
)
from apps.integrations.utils import log_action
from django.db import transaction
from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver
from services.email.email_service import EmailService
from services.su_api import confirm_or_cancel_booking

logger = logging.getLogger(__name__)


class BookingError(Exception):
    """Custom exception for booking-related errors."""

    pass


class JSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle UUID serialization."""

    def default(self, obj):
        if isinstance(obj, UUID):
            return str(obj)
        return super().default(obj)


def serialize_details(details: Dict[str, Any]) -> Dict[str, Any]:
    """Serialize details dictionary to ensure JSON compatibility."""
    return json.loads(json.dumps(details, cls=JSONEncoder))


def log_booking_event(
    user, property_id: str, action: str, description: str, status: str, details: Dict
) -> None:
    """Safely log booking events with proper serialization."""
    try:
        serialized_details = serialize_details(details)
        transaction.on_commit(
            lambda: log_action(
                user=user,
                property_id=property_id,
                action=action,
                description=description,
                status=status,
                details=serialized_details,
            )
        )
    except Exception as e:
        logger.error(
            "Failed to log booking event",
            exc_info=True,
            extra={"property_id": str(property_id), "action": action, "error": str(e)},
        )


def _process_reservation_update(instance: Booking, status: str) -> bool:
    """Process reservation status update with proper error handling."""
    if not instance.reservation_data or not instance.reservation_data.id:
        logger.error(
            "No reservation data found for booking",
            extra={"booking_id": str(instance.id)},
        )
        return False

    try:
        response = confirm_or_cancel_booking(booking_id=instance.reservation_data.id)
        success = response.get("Success") == "Success"

        transaction.on_commit(
            lambda: log_booking_event(
                user=instance.property.staffs.first(),
                property_id=instance.property.id,
                action="update",
                description=f"Booking {instance.status} on SU API",
                status="successful" if success else "failed",
                details={"reservation_id": str(instance.id), "su_response": response},
            )
        )

        return success

    except Exception as e:
        logger.error(
            "Failed to update reservation",
            exc_info=True,
            extra={"booking_id": str(instance.id), "status": status, "error": str(e)},
        )
        return False


@receiver(post_save, sender=Booking)
def handle_requested_booking_confirmation(
    sender, instance: Booking, created: bool, **kwargs
) -> None:
    """Signal handler to confirm bookings with 'requested' status."""
    if not created and instance.status == Booking.Status.REQUEST:
        transaction.on_commit(
            lambda: _process_reservation_update(instance, Booking.Status.REQUEST)
        )


@receiver(post_delete, sender=BookingBlock)
def handle_booking_block_deletion(sender, instance: BookingBlock, **kwargs):
    """
    Handle booking block deletion by unblocking rooms and syncing inventory.
    """
    # Unblock the rooms first
    if instance.property.is_onboarded:
        transaction.on_commit(
            lambda: unblock_rooms_task.delay(
                str(instance.property.id), instance.start_date, instance.end_date
            )
        )


@receiver(post_save, sender=BookingBlock)
def handle_booking_block_creation(
    sender, instance: BookingBlock, created: bool, **kwargs
):
    """Handle booking block creation."""
    if created and instance.property.is_onboarded:
        transaction.on_commit(lambda: block_rooms_task.delay(instance.id))


@receiver(post_save, sender=Booking)
def handle_booking_inventory_update(sender, instance: Booking, created: bool, **kwargs):
    """
    Signal handler to update inventory when bookings are created, modified, or cancelled.

    """
    if created or instance.status in [Booking.Status.NEW, Booking.Status.MODIFIED]:
        # For new or modified bookings, decrease inventory
        transaction.on_commit(
            lambda: update_inventory_for_booking.delay(str(instance.id), "decrease")
        )
    elif instance.status == Booking.Status.CANCELLED:
        # For cancelled bookings, increase inventory
        transaction.on_commit(
            lambda: update_inventory_for_booking.delay(str(instance.id), "increase")
        )


def _detect_overbooking(instance: Booking) -> None:
    """
    Detect and send alert for overbooking when a non-manual booking is created.

    Args:
        instance: The newly created booking instance
    """
    # Only check for non-manual bookings from SU API
    if instance.is_manual:
        return

    # Find overlapping bookings (excluding the current booking)
    overlapping_bookings = (
        Booking.objects.filter(
            property=instance.property,
            checkin_date__lt=instance.checkout_date,  # Existing booking starts before new booking ends
            checkout_date__gt=instance.checkin_date,  # Existing booking ends after new booking starts
        )
        .exclude(id=instance.id)  # Exclude the current booking
        .exclude(
            # Exclude bookings that end exactly on the new booking's start date
            checkout_date=instance.checkin_date
        )
        .exclude(
            # Exclude bookings that start exactly on the new booking's end date
            checkin_date=instance.checkout_date
        )
        .exclude(
            # Exclude cancelled bookings
            status=Booking.Status.CANCELLED
        )
    )

    if overlapping_bookings.exists():
        # Get the first overlapping booking
        existing_booking = overlapping_bookings.first()

        logger.warning(
            "Overbooking detected",
            extra={
                "property_id": str(instance.property.id),
                "property_name": instance.property.name,
                "existing_booking_id": str(existing_booking.id),
                "new_booking_id": str(instance.id),
                "overlap_dates": {
                    "existing_checkin": existing_booking.checkin_date.isoformat(),
                    "existing_checkout": existing_booking.checkout_date.isoformat(),
                    "new_checkin": instance.checkin_date.isoformat(),
                    "new_checkout": instance.checkout_date.isoformat(),
                },
            },
        )

        # Send overbooking alert email
        try:
            email_service = EmailService()
            transaction.on_commit(
                lambda: email_service.send_overbooking_alert(
                    property_obj=instance.property,
                    existing_booking=existing_booking,
                    new_booking=instance,
                )
            )

            # Log the overbooking event
            transaction.on_commit(
                lambda: log_booking_event(
                    user=instance.property.staffs.first(),
                    property_id=instance.property.id,
                    action="overbooking_detected",
                    description=f"Overbooking detected between bookings {existing_booking.id} and {instance.id}",
                    status="alert_sent",
                    details={
                        "existing_booking_id": str(existing_booking.id),
                        "new_booking_id": str(instance.id),
                        "existing_dates": {
                            "checkin": str(existing_booking.checkin_date),
                            "checkout": str(existing_booking.checkout_date),
                        },
                        "new_dates": {
                            "checkin": str(instance.checkin_date),
                            "checkout": str(instance.checkout_date),
                        },
                    },
                )
            )

        except Exception as e:
            logger.error(
                "Failed to send overbooking alert",
                exc_info=True,
                extra={
                    "property_id": str(instance.property.id),
                    "existing_booking_id": str(existing_booking.id),
                    "new_booking_id": str(instance.id),
                    "error": str(e),
                },
            )


@receiver(post_save, sender=Booking)
def handle_overbooking_detection(
    sender, instance: Booking, created: bool, **kwargs
) -> None:
    """
    Signal handler to detect overbooking when a new booking is created.
    """
    if created:
        transaction.on_commit(lambda: _detect_overbooking(instance))
