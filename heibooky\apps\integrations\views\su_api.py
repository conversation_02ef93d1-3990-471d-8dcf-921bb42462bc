from apps.integrations.tasks import onboarding_task
from apps.stay.models import Property
from apps.stay.utils import check_status
from apps.users.permissions import VerifiedOwnerPermission
from django.core.exceptions import ObjectDoesNotExist, PermissionDenied
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView


class PropertyOnboardView(APIView):
    """
    Initiates property onboarding background process
    Returns Celery task ID for tracking
    """

    permission_classes = [IsAuthenticated, VerifiedOwnerPermission]

    def post(self, request, *args, **kwargs):
        property_id = request.data.get("property_id")
        user = request.user

        if not property_id:
            return Response(
                {"error": "Property ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            property_instance = Property.objects.get(id=property_id)

            if not property_instance.is_active:
                return Response(
                    {"error": "Property is deactivated"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            status_check = check_status(property_instance, user)
            if not all(status_check.values()):
                return Response(
                    {"error": "Property is not ready for onboarding"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Start background onboarding task
            task = onboarding_task.delay(property_id, user.id)

            return Response(
                {"message": "Onboarding process started", "task_id": task.id},
                status=status.HTTP_202_ACCEPTED,
            )

        except ObjectDoesNotExist:
            return Response(
                {"error": "Property not found or access denied"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def handle_exception(self, exc):
        if isinstance(exc, PermissionDenied):
            return Response(
                {"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN
            )
        return super().handle_exception(exc)
