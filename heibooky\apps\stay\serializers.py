import os

from apps.pricing.serializers import *
from rest_framework import serializers

from .models import *


# Location Serializer
class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = [
            "id",
            "street",
            "post_code",
            "city",
            "country",
            "latitude",
            "longitude",
            "is_editable",
        ]

    def validate_post_code(self, value):
        # Assuming Italian postcodes are 5 digits
        if len(value) != 5 or not value.isdigit():
            raise serializers.ValidationError(
                "Invalid Italian post code format. Please enter a 5-digit number."
            )
        return value

    def validate(self, data):
        """
        Custom validation to ensure latitude and longitude are within valid ranges.
        """
        latitude = data.get("latitude")
        longitude = data.get("longitude")

        if latitude is not None and (latitude < -90 or latitude > 90):
            raise serializers.ValidationError(
                "Latitude must be between -90 and 90 degrees."
            )
        if longitude is not None and (longitude < -180 or longitude > 180):
            raise serializers.ValidationError(
                "Longitude must be between -180 and 180 degrees."
            )

        return data


# Photo Serializer
class PhotoSerializer(serializers.ModelSerializer):
    """
    Serializer for Photo model with enhanced validation and CDN URL support.
    """

    class Meta:
        model = Photo
        fields = ["id", "property", "room", "image", "description", "created_at"]
        read_only_fields = ["created_at"]

    def validate(self, data):
        """
        Validate that only one of property or room is provided.
        """
        if data.get("property") and data.get("room"):
            raise serializers.ValidationError(
                "Cannot assign photo to both property and room simultaneously"
            )
        if not data.get("property") and not data.get("room"):
            raise serializers.ValidationError(
                "Must assign photo to either property or room"
            )
        return data

    def validate_image(self, value):
        """
        Validate that uploaded file is an image with proper format and extension.
        Enhanced with better error handling and detailed error messages.
        """
        import logging

        logger = logging.getLogger(__name__)

        if not value:
            raise serializers.ValidationError(
                {"error": "È richiesto un file immagine."}
            )

        allowed_content_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
        valid_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"]

        # Get file extension and content type
        file_ext = os.path.splitext(value.name)[1].lower()
        content_type = getattr(value, "content_type", None)

        logger.info(
            f"Validating image: {value.name}, size: {value.size}, content_type: {content_type}"
        )

        # Validate file size first (30MB limit)
        if value.size > 30 * 1024 * 1024:
            logger.warning(
                f"File size validation failed: {value.size} bytes exceeds limit"
            )
            raise serializers.ValidationError(
                {
                    "error": "File troppo grande. La dimensione massima consentita è di 30MB."
                }
            )

        # Validate extension
        if file_ext not in valid_extensions:
            logger.warning(
                f"File extension validation failed: {file_ext} not in {valid_extensions}"
            )
            raise serializers.ValidationError(
                {
                    "error": f"Tipo di file non valido. Il file deve essere uno dei seguenti: {', '.join(valid_extensions)}"
                }
            )

        # Validate content type
        if content_type not in allowed_content_types:
            logger.warning(
                f"Content type validation failed: {content_type} not in {allowed_content_types}"
            )
            raise serializers.ValidationError(
                {
                    "error": "Tipo di file non valido. Il file deve essere in formato JPEG, PNG, GIF o WEBP"
                }
            )

        # Verify file is actually an image
        try:
            from PIL import Image

            # Reset file pointer to the beginning
            if hasattr(value, "seek") and callable(value.seek):
                value.seek(0)

            # Try to open the image
            try:
                img = Image.open(value)
            except Exception as e:
                logger.error(f"Failed to open image with PIL: {str(e)}")
                raise serializers.ValidationError(
                    {"error": f"Impossibile aprire l'immagine: {str(e)}"}
                )

            # Verify it's a valid image
            try:
                img.verify()  # Verify it's an image
            except Exception as e:
                logger.error(f"Image verification failed: {str(e)}")
                raise serializers.ValidationError(
                    {"error": f"Verifica dell'immagine fallita: {str(e)}"}
                )

            # Reset file pointer after verification
            if hasattr(value, "seek") and callable(value.seek):
                value.seek(0)

            # Re-open the image after verification
            img = Image.open(value)

            # Verify format matches extension
            img_format = getattr(img, "format", "").lower()
            valid_formats = [ext.replace(".", "") for ext in valid_extensions]

            if img_format and img_format not in valid_formats:
                logger.warning(
                    f"Image format mismatch: {img_format} not in {valid_formats}"
                )
                raise serializers.ValidationError(
                    {
                        "error": f"L'estensione del file non corrisponde al formato effettivo dell'immagine ({img_format})."
                    }
                )

            logger.info(
                f"Image validation successful: {value.name}, format: {img_format}"
            )

        except serializers.ValidationError:
            # Re-raise validation errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error during image validation: {str(e)}")
            raise serializers.ValidationError(
                {"error": f"Errore durante la validazione dell'immagine: {str(e)}"}
            )

        # Reset file pointer before returning
        if hasattr(value, "seek") and callable(value.seek):
            value.seek(0)

        return value


# Property Serializer
class PropertySerializer(serializers.ModelSerializer):
    photos = PhotoSerializer(many=True, read_only=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "name",
            "property_type",
            "description",
            "location",
            "is_multi_unit",
            "is_onboarded",
            "is_active",
            "hotel_id",
            "cover_image",
            "photos",
        ]
        read_only_fields = ["hotel_id", "cover_image", "is_onboarded", "is_active"]


class PropertyMetadataSerializer(serializers.ModelSerializer):
    """
    Serializer for PropertyMetadata model to handle additional property details.
    """

    property = PropertySerializer(read_only=True)

    class Meta:
        model = PropertyMetadata
        fields = [
            "id",
            "property",
            "check_in_time",
            "check_out_time",
            "close_out_days",
            "close_out_time",
            "cancelation_policy_type",
            "regional_id_code",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["property", "created_at", "updated_at"]

    def validate_close_out_days(self, value):
        # Ensure close_out_days is within the range 0 to 30, or can be blank
        if value is not None and not (0 <= int(value) <= 30):
            raise serializers.ValidationError(
                "Close-out days must be between 0 and 30."
            )
        return value

    def validate_close_out_time(self, value):
        # Ensure close_out_time has 30-minute intervals if provided
        if value and value.minute not in [0, 30]:
            raise serializers.ValidationError(
                "Close-out time must be in 30-minute intervals (e.g., 00:00, 00:30)."
            )
        return value

    def validate(self, data):
        """
        Custom validation for check-in and check-out times.
        """
        check_in_time = data.get("check_in_time")
        check_out_time = data.get("check_out_time")

        if check_in_time and check_out_time and check_in_time > check_out_time:
            raise serializers.ValidationError(
                "Check-in time cannot be later than check-out time."
            )

        return data


class TeamInviteSerializer(serializers.ModelSerializer):
    permissions = serializers.SlugRelatedField(
        many=True, queryset=PropertyPermission.objects.all(), slug_field="name"
    )
    invited_by = serializers.SlugRelatedField(read_only=True, slug_field="email")
    property_name = serializers.CharField(source="property.name", read_only=True)

    class Meta:
        model = TeamInvite
        fields = [
            "id",
            "property",
            "property_name",
            "email",
            "invited_by",
            "permissions",
            "is_registered",
            "created_at",
            "expires_at",
            "accepted",
            "accepted_at",
        ]
        read_only_fields = ["id", "created_at", "expires_at", "accepted", "accepted_at"]

    def validate_email(self, value):
        """
        Validate that the email is not already a staff member of the property and convert to lowercase
        """
        # Convert email to lowercase
        value = value.lower()

        property_id = self.initial_data.get("property")
        if property_id:
            property_instance = Property.objects.get(id=property_id)
            if property_instance.staffs.filter(email=value).exists():
                raise serializers.ValidationError(
                    "This user is already a staff member of this property"
                )
        return value


class RoomAmenitySerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="amenity.name")
    category = serializers.CharField(source="amenity.category")

    class Meta:
        model = RoomAmenity
        fields = ["id", "name", "category", "is_available"]


class RoomSerializer(serializers.ModelSerializer):
    amenities = RoomAmenitySerializer(many=True, required=False)
    size_measurement = serializers.DecimalField(
        max_digits=5, decimal_places=2, required=False, allow_null=True
    )
    size_measurement_unit = serializers.ChoiceField(
        choices=Room.SIZE_MEASUREMENT_UNIT_CHOICES, required=False, allow_null=True
    )

    class Meta:
        model = Room
        fields = [
            "id",
            "property",
            "room_type",
            "room_rate",
            "max_occupancy",
            "max_child_occupancy",
            "quantity",
            "size_measurement",
            "size_measurement_unit",
            "description",
            "bed_config",
            "bathroom_quntity",
            "amenities",
            "is_active",
            "is_onboarded",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["is_onboarded"]

    def validate_description(self, value):
        """Validate room description length."""
        if value and len(value) > 2000:
            raise serializers.ValidationError(
                "Room description must not exceed 2000 characters."
            )
        return value

    def validate(self, data):
        # Check if size_measurement and size_measurement_unit are provided together
        size_measurement = data.get("size_measurement")
        size_measurement_unit = data.get("size_measurement_unit")

        if size_measurement is not None and size_measurement_unit is None:
            raise serializers.ValidationError(
                "If 'size_measurement' is provided, 'size_measurement_unit' must also be provided."
            )
        if size_measurement_unit is not None and size_measurement is None:
            raise serializers.ValidationError(
                "If 'size_measurement_unit' is provided, 'size_measurement' must also be provided."
            )

        # Validate max_occupancy and max_child_occupancy
        max_occupancy = data.get("max_occupancy", 0)
        max_child_occupancy = data.get("max_child_occupancy", 0)
        if max_occupancy < 0 or max_occupancy >= 30:
            raise serializers.ValidationError(
                "'max_occupancy' must be a non-negative integer less than 30."
            )
        if max_child_occupancy < 0 or max_child_occupancy >= 30:
            raise serializers.ValidationError(
                "'max_child_occupancy' must be a non-negative integer less than 30."
            )
        if max_child_occupancy > max_occupancy:
            raise serializers.ValidationError(
                "'max_child_occupancy' cannot exceed 'max_occupancy'."
            )

        return data

    def create(self, validated_data):
        property_instance = validated_data.get("property")
        if not property_instance.is_multi_unit:
            if Room.objects.filter(property=property_instance).exists():
                raise serializers.ValidationError(
                    "Cannot create more than one room for a single-unit property."
                )

        amenities_data = validated_data.pop("amenities", [])
        room = Room.objects.create(**validated_data)
        for amenity_data in amenities_data:
            RoomAmenity.objects.create(room=room, **amenity_data)
        return room

    def update(self, instance, validated_data):
        amenities_data = validated_data.pop("amenities", [])
        instance = super().update(instance, validated_data)

        # Clear and update room amenities
        instance.amenities.all().delete()
        for amenity_data in amenities_data:
            RoomAmenity.objects.create(room=instance, **amenity_data)
        return instance


class PropertyOwnershipSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyOwnership
        fields = ["user", "property", "relation_type", "created_at"]

    def validate_relation_type(self, value):
        """
        Validate that the relation_type is one of the defined choices.
        """
        if value not in PropertyOwnership.LegalRelation.values:
            raise serializers.ValidationError(
                f"Invalid relation_type '{value}'. Must be one of {', '.join(PropertyOwnership.LegalRelation.values)}."
            )
        return value


class GuestArrivalInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = GuestArrivalInfo
        fields = [
            "id",
            "property",
            "contact_name",
            "contact_surname",
            "email",
            "phone_number",
        ]

    def validate_phone_number(self, value):
        """
        Validates and cleans up the phone number by removing spaces.
        """
        return value.replace(" ", "")


class AmenitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Amenity
        fields = ["id", "name", "category"]


class PropertyAmenitySerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="amenity.name")
    category = serializers.CharField(source="amenity.category")

    class Meta:
        model = PropertyAmenity
        fields = ["id", "name", "category", "is_available"]
