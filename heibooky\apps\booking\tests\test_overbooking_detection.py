import uuid
from datetime import date, timedelta
from unittest.mock import MagicMock, patch

from apps.booking.models import Booking, Customer, Reservation
from apps.booking.signals import _detect_overbooking
from apps.stay.models import Property
from apps.stay.models.location import Location
from apps.users.models import User
from django.test import TestCase
from django.utils import timezone
from services.email.email_service import EmailService


class OverbookingDetectionTestCase(TestCase):
    def setUp(self):
        """Set up test data for overbooking detection tests."""
        # Create a test user
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", name="Test User"
        )

        # Create a test location
        self.location = Location.objects.create(
            street="Via Roma 123",
            post_code="00100",
            city="Roma",
            country="Italy",
            latitude=41.9028,
            longitude=12.4964,
        )

        # Create a test property
        self.property = Property.objects.create(
            name="Test Property",
            property_type=Property.HOTEL,
            location=self.location,
            hotel_id="TEST123",
            chain_id="CHAIN123",
        )
        self.property.staffs.add(self.user)

        # Set up dates for testing
        self.today = timezone.now().date()
        self.tomorrow = self.today + timedelta(days=1)
        self.day_after_tomorrow = self.today + timedelta(days=2)
        self.three_days_later = self.today + timedelta(days=3)

        # Create test customers
        self.customer1 = Customer.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            telephone="1234567890",
        )

        self.customer2 = Customer.objects.create(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            telephone="0987654321",
        )

    def _create_reservation_and_booking(
        self,
        reservation_id,
        guest_name,
        customer,
        checkin_date,
        checkout_date,
        is_manual=False,
        channel_code=None,
    ):
        """Helper method to create reservation and booking objects."""
        reservation = Reservation.objects.create(
            id=reservation_id,
            guest_name=guest_name,
            checkin_date=timezone.make_aware(
                timezone.datetime.combine(checkin_date, timezone.datetime.min.time())
            ),
            checkout_date=timezone.make_aware(
                timezone.datetime.combine(checkout_date, timezone.datetime.min.time())
            ),
            total_price=100.00,
            number_of_guests=2,
            number_of_adults=2,
        )

        booking = Booking.objects.create(
            property=self.property,
            customer=customer,
            reservation_data=reservation,
            is_manual=is_manual,
            channel_code=channel_code or Booking.ChannelChoices.BOOKING,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            status=Booking.Status.NEW,
        )

        return reservation, booking

    @patch("apps.booking.signals.EmailService")
    @patch("apps.booking.signals.log_booking_event")
    @patch("django.db.transaction.on_commit")
    def test_overbooking_detection_with_overlap(
        self, mock_on_commit, mock_log_booking, mock_email_service
    ):
        """Test that overbooking is detected when two non-manual bookings overlap."""
        # Create the first booking (existing)
        reservation1, booking1 = self._create_reservation_and_booking(
            "existing_booking",
            "John Doe",
            self.customer1,
            self.tomorrow,
            self.three_days_later,
            is_manual=False,
            channel_code=Booking.ChannelChoices.BOOKING,
        )

        # Mock the email service
        mock_email_instance = MagicMock()
        mock_email_service.return_value = mock_email_instance
        mock_email_instance.send_overbooking_alert.return_value = True

        # Mock on_commit to execute immediately
        def execute_immediately(func):
            func()

        mock_on_commit.side_effect = execute_immediately

        # Create the second booking (new) that overlaps with the first
        reservation2, booking2 = self._create_reservation_and_booking(
            "new_booking",
            "Jane Smith",
            self.customer2,
            self.day_after_tomorrow,  # Starts one day after first booking starts
            self.three_days_later
            + timedelta(days=1),  # Ends one day after first booking ends
            is_manual=False,
            channel_code=Booking.ChannelChoices.AIRBNB,
        )

        # Manually trigger the overbooking detection
        _detect_overbooking(booking2)

        # Verify that email service was called
        mock_email_service.assert_called_once()
        mock_email_instance.send_overbooking_alert.assert_called_once_with(
            property_obj=self.property, existing_booking=booking1, new_booking=booking2
        )

        # Verify that on_commit was called (for both email and logging)
        self.assertGreaterEqual(mock_on_commit.call_count, 2)

    @patch("apps.booking.signals.EmailService")
    def test_no_overbooking_detection_for_manual_booking(self, mock_email_service):
        """Test that overbooking detection is skipped for manual bookings."""
        # Create the first booking (existing)
        reservation1, booking1 = self._create_reservation_and_booking(
            "existing_booking",
            "John Doe",
            self.customer1,
            self.tomorrow,
            self.three_days_later,
            is_manual=False,
        )

        # Create a manual booking that overlaps
        reservation2, booking2 = self._create_reservation_and_booking(
            "manual_booking",
            "Jane Smith",
            self.customer2,
            self.day_after_tomorrow,
            self.three_days_later + timedelta(days=1),
            is_manual=True,  # This is a manual booking
        )

        # Manually trigger the overbooking detection
        _detect_overbooking(booking2)

        # Verify that email service was NOT called for manual booking
        mock_email_service.assert_not_called()

    @patch("apps.booking.signals.EmailService")
    def test_no_overbooking_detection_for_adjacent_bookings(self, mock_email_service):
        """Test that adjacent bookings (no overlap) don't trigger overbooking alert."""
        # Create the first booking
        reservation1, booking1 = self._create_reservation_and_booking(
            "first_booking",
            "John Doe",
            self.customer1,
            self.tomorrow,
            self.day_after_tomorrow,  # Ends on day 2
            is_manual=False,
        )

        # Create a second booking that starts when the first ends
        reservation2, booking2 = self._create_reservation_and_booking(
            "second_booking",
            "Jane Smith",
            self.customer2,
            self.day_after_tomorrow,  # Starts on day 2 (when first ends)
            self.three_days_later,
            is_manual=False,
        )

        # Manually trigger the overbooking detection
        _detect_overbooking(booking2)

        # Verify that email service was NOT called
        mock_email_service.assert_not_called()

    @patch("apps.booking.signals.EmailService")
    def test_no_overbooking_detection_with_cancelled_booking(self, mock_email_service):
        """Test that cancelled bookings don't trigger overbooking alert."""
        # Create the first booking and cancel it
        reservation1, booking1 = self._create_reservation_and_booking(
            "cancelled_booking",
            "John Doe",
            self.customer1,
            self.tomorrow,
            self.three_days_later,
            is_manual=False,
        )
        booking1.status = Booking.Status.CANCELLED
        booking1.save()

        # Create a second booking that would overlap with the cancelled one
        reservation2, booking2 = self._create_reservation_and_booking(
            "new_booking",
            "Jane Smith",
            self.customer2,
            self.day_after_tomorrow,
            self.three_days_later + timedelta(days=1),
            is_manual=False,
        )

        # Manually trigger the overbooking detection
        _detect_overbooking(booking2)

        # Verify that email service was NOT called
        mock_email_service.assert_not_called()

    def test_overbooking_email_service_method(self):
        """Test the overbooking email service method directly."""
        # Create test bookings
        reservation1, booking1 = self._create_reservation_and_booking(
            "existing_booking",
            "John Doe",
            self.customer1,
            self.tomorrow,
            self.three_days_later,
            is_manual=False,
        )

        reservation2, booking2 = self._create_reservation_and_booking(
            "new_booking",
            "Jane Smith",
            self.customer2,
            self.day_after_tomorrow,
            self.three_days_later + timedelta(days=1),
            is_manual=False,
        )

        # Test the email service method
        email_service = EmailService()

        # Mock the send_email method to avoid actually sending emails in tests
        with patch.object(email_service, "send_email", return_value=True) as mock_send:
            result = email_service.send_overbooking_alert(
                property_obj=self.property,
                existing_booking=booking1,
                new_booking=booking2,
            )

            # Verify the method returns True
            self.assertTrue(result)

            # Verify send_email was called
            mock_send.assert_called_once()

            # Get the call arguments
            args, kwargs = mock_send.call_args
            recipient, content = args

            # Verify recipient
            self.assertEqual(recipient.email, "<EMAIL>")

            # Verify content structure
            self.assertEqual(content.template_name, "overbooking_alert")
            self.assertIn("🚨 Overbooking Rilevato", content.subject)
            self.assertEqual(content.context["property"], self.property)
            self.assertEqual(content.context["existing_booking"], booking1)
            self.assertEqual(content.context["new_booking"], booking2)

    def test_overlap_calculation(self):
        """Test that overlap period is calculated correctly."""
        # Create bookings with specific overlap
        reservation1, booking1 = self._create_reservation_and_booking(
            "booking1",
            "John Doe",
            self.customer1,
            self.tomorrow,  # Day 1
            self.three_days_later,  # Day 3
            is_manual=False,
        )

        reservation2, booking2 = self._create_reservation_and_booking(
            "booking2",
            "Jane Smith",
            self.customer2,
            self.day_after_tomorrow,  # Day 2
            self.three_days_later + timedelta(days=1),  # Day 4
            is_manual=False,
        )

        # Calculate expected overlap
        expected_start = max(booking1.checkin_date, booking2.checkin_date)  # Day 2
        expected_end = min(booking1.checkout_date, booking2.checkout_date)  # Day 3

        self.assertEqual(expected_start, self.day_after_tomorrow)
        self.assertEqual(expected_end, self.three_days_later)

    def tearDown(self):
        """Clean up test data."""
        # Django's TestCase automatically handles database cleanup
        pass


class OverbookingEmailTemplateTestCase(TestCase):
    """Test the overbooking email templates."""

    def setUp(self):
        """Set up test data for template tests."""
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", name="Test User"
        )

        self.location = Location.objects.create(
            street="Via Roma 123",
            post_code="00100",
            city="Roma",
            country="Italy",
            latitude=41.9028,
            longitude=12.4964,
        )

        self.property = Property.objects.create(
            name="Hotel Test Roma",
            property_type=Property.HOTEL,
            location=self.location,
            hotel_id="TEST123",
            chain_id="CHAIN123",
        )

        # Create mock bookings for template testing
        self.existing_booking = MagicMock()
        self.existing_booking.id = uuid.uuid4()
        self.existing_booking.checkin_date = date.today() + timedelta(days=1)
        self.existing_booking.checkout_date = date.today() + timedelta(days=3)
        self.existing_booking.status = "new"
        self.existing_booking.get_status_display.return_value = "Nuovo"
        self.existing_booking.get_channel_code_display.return_value = "Booking.com"

        self.existing_booking.reservation_data = MagicMock()
        self.existing_booking.reservation_data.guest_name = "Mario Rossi"
        self.existing_booking.reservation_data.total_price = 150.00

        self.new_booking = MagicMock()
        self.new_booking.id = uuid.uuid4()
        self.new_booking.checkin_date = date.today() + timedelta(days=2)
        self.new_booking.checkout_date = date.today() + timedelta(days=4)
        self.new_booking.status = "new"
        self.new_booking.get_status_display.return_value = "Nuovo"
        self.new_booking.get_channel_code_display.return_value = "Airbnb"

        self.new_booking.reservation_data = MagicMock()
        self.new_booking.reservation_data.guest_name = "Laura Bianchi"
        self.new_booking.reservation_data.total_price = 200.00

    @patch("services.email.email_service.render_to_string")
    def test_overbooking_template_context(self, mock_render):
        """Test that the overbooking email template receives correct context."""
        mock_render.return_value = "Mock email content"

        email_service = EmailService()

        with patch.object(email_service, "send_email", return_value=True):
            email_service.send_overbooking_alert(
                property_obj=self.property,
                existing_booking=self.existing_booking,
                new_booking=self.new_booking,
            )

        # Verify that render_to_string was called with the correct template
        mock_render.assert_called()
        call_args = mock_render.call_args_list

        # Find the call for the HTML template
        html_call = None
        for call in call_args:
            if "overbooking_alert.html" in call[0]:
                html_call = call
                break

        self.assertIsNotNone(html_call, "HTML template should be rendered")

        # Check context
        context = html_call[0][1]
        self.assertEqual(context["property"], self.property)
        self.assertEqual(context["existing_booking"], self.existing_booking)
        self.assertEqual(context["new_booking"], self.new_booking)
        self.assertIn("overlap_start", context)
        self.assertIn("overlap_end", context)
        self.assertIn("admin_url", context)
