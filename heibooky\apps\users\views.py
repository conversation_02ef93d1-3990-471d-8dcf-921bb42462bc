import logging

from django.contrib.auth import authenticate, logout
from django.contrib.gis.geoip2 import GeoIP2
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from rest_framework import generics, status, views
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.parsers import FormParser, MultiPartParser
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework_simplejwt.tokens import (
    BlacklistedToken,
    OutstandingToken,
    RefreshToken,
)
from services.email import AccountEmailService, VerificationService
from user_agents import parse

from .models import User, UserProfile
from .serializers import (
    ChangePasswordSerializer,
    LoginSerializer,
    PasswordResetSerializer,
    ResendVerificationSerializer,
    SetNewPasswordSerializer,
    SetPasswordSerializer,
    TeamUserSerializer,
    UserProfileSerializer,
    UserSerializer,
    VerifyEmailSerializer,
)
from .tasks import (
    generate_and_send_verification_code,
    login_location_task,
    send_account_deletion_email_task,
    send_password_changed_email_task,
    send_welcome_email,
)
from .utils import format_error_response, get_client_ip

logger = logging.getLogger(__name__)


class EmailVerificationMixin:
    """Mixin to handle common email verification operations"""

    def _get_verification_services(self):
        """Get instances of email and verification services"""
        return AccountEmailService(), VerificationService()

    def send_verification_code(self, email: str) -> tuple[bool, str]:
        """Send verification code asynchronously and return status and message"""
        try:
            # Queue the email task asynchronously
            result = generate_and_send_verification_code.delay(email, "verification")
            logger.info(f"Verification email queued for {email}, task_id: {result.id}")
            return True, "Verification code sent successfully."
        except Exception as e:
            logger.error(
                f"Failed to queue verification code for {email}: {str(e)}",
                exc_info=True,
            )
            return False, "Failed to send verification code."

    def send_password_reset_code(self, email: str) -> tuple[bool, str]:
        """Send password reset code asynchronously and return status and message"""
        try:
            # Queue the email task asynchronously
            result = generate_and_send_verification_code.delay(email, "password_reset")
            logger.info(
                f"Password reset email queued for {email}, task_id: {result.id}"
            )
            return True, "Password reset code sent successfully."
        except Exception as e:
            logger.error(
                f"Failed to queue password reset code for {email}: {str(e)}",
                exc_info=True,
            )
            return False, "Failed to send password reset code."

    def verify_code(self, email: str, code: str) -> bool:
        """Verify the provided code"""
        _, verification_service = self._get_verification_services()
        return verification_service.verify_code(email, code)

    def check_rate_limit(
        self, email: str, action: str = "resend_verification"
    ) -> tuple[bool, int]:
        """
        Check if the user has exceeded the rate limit for the given action.
        Returns (is_allowed, remaining_time_seconds)
        """
        cache_key = f"{action}:{email}"
        attempts_key = f"{action}_attempts:{email}"

        # Get current attempts and last attempt time
        last_attempt_timestamp = cache.get(cache_key)
        attempts = cache.get(attempts_key, 0)

        # Rate limiting rules
        MAX_ATTEMPTS = 3  # Maximum attempts per window
        WINDOW_MINUTES = 15  # Time window in minutes
        COOLDOWN_MINUTES = 5  # Cooldown after each attempt

        current_time = timezone.now()
        current_timestamp = current_time.timestamp()

        # If there's a last attempt, check if we're still in cooldown
        if last_attempt_timestamp:
            # Ensure timestamp is a float
            if isinstance(last_attempt_timestamp, str):
                try:
                    last_attempt_timestamp = float(last_attempt_timestamp)
                except (ValueError, TypeError):
                    # If conversion fails, treat as no previous attempt
                    last_attempt_timestamp = None

            if last_attempt_timestamp:
                time_diff = current_timestamp - last_attempt_timestamp
                if time_diff < (COOLDOWN_MINUTES * 60):
                    remaining_time = int((COOLDOWN_MINUTES * 60) - time_diff)
                    return False, remaining_time

        # Check if we've exceeded max attempts in the window
        if attempts >= MAX_ATTEMPTS:
            # Check if the window has expired
            if last_attempt_timestamp and (
                current_timestamp - last_attempt_timestamp
            ) > (WINDOW_MINUTES * 60):
                # Reset attempts counter
                cache.delete(attempts_key)
                attempts = 0
            else:
                if last_attempt_timestamp:
                    remaining_time = int(
                        (WINDOW_MINUTES * 60)
                        - (current_timestamp - last_attempt_timestamp)
                    )
                    return False, remaining_time

        return True, 0

    def record_attempt(self, email: str, action: str = "resend_verification"):
        """Record an attempt for rate limiting"""
        cache_key = f"{action}:{email}"
        attempts_key = f"{action}_attempts:{email}"

        current_timestamp = timezone.now().timestamp()
        attempts = cache.get(attempts_key, 0) + 1

        # Store the attempt timestamp and count
        cache.set(cache_key, current_timestamp, timeout=15 * 60)  # 15 minutes
        cache.set(attempts_key, attempts, timeout=15 * 60)  # 15 minutes


class SignupView(EmailVerificationMixin, generics.CreateAPIView):
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            # Validate but handle errors manually instead of raising exceptions
            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user = serializer.save()

            success, message = self.send_verification_code(user.email)
            if not success:
                user.delete()  # Rollback user creation if email sending fails
                return Response(
                    {"errors": {"email": [message]}},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            return Response(
                {
                    "user": UserSerializer(
                        user, context=self.get_serializer_context()
                    ).data,
                    "detail": message,
                },
                status=status.HTTP_201_CREATED,
            )
        except ValidationError as e:
            # Handle DRF ValidationError
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle other exceptions
            logger.error(f"Signup error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class VerifyEmailView(EmailVerificationMixin, views.APIView):
    serializer_class = VerifyEmailSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)

            # Validate but handle errors manually
            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]

            if self.verify_code(email, verification_code):
                user = User.objects.get(email=email)
                user.is_verified = True
                user.save()
                return Response(
                    {"detail": "Email successfully verified."},
                    status=status.HTTP_200_OK,
                )

            return Response(
                {"errors": {"verification_code": ["Invalid verification code."]}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except ObjectDoesNotExist:
            return Response(
                {"errors": {"email": ["User not found."]}},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValidationError as e:
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Email verification error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SetPasswordView(generics.GenericAPIView):
    serializer_class = SetPasswordSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email = serializer.validated_data["email"]
            user = User.objects.get(email=email)

            if user.has_set_password:
                return Response(
                    {
                        "errors": {
                            "password": ["Password has already been set for this user."]
                        }
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not user.is_verified:
                return Response(
                    {
                        "errors": {
                            "email": [
                                "Email verification is required before setting password."
                            ]
                        }
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user.set_password(serializer.validated_data["password"])
            user.has_set_password = True
            user.save()

            # Send welcome email after password is set (asynchronously)
            try:
                send_welcome_email.delay(user.email)
                logger.info(f"Welcome email queued for {user.email}")
            except Exception as e:
                logger.warning(f"Failed to queue welcome email: {str(e)}")

            return Response(
                {
                    "detail": "Password set successfully. You can now login to your account."
                },
                status=status.HTTP_200_OK,
            )

        except ObjectDoesNotExist:
            return Response(
                {"errors": {"email": ["User not found."]}},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.error(f"Set password error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ResendVerificationView(EmailVerificationMixin, views.APIView):
    """
    Resend email verification code to users who haven't verified their email yet.
    Includes rate limiting to prevent abuse.
    """

    permission_classes = [AllowAny]
    serializer_class = ResendVerificationSerializer

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email = serializer.validated_data["email"].lower()

            # Check rate limiting
            is_allowed, remaining_time = self.check_rate_limit(email)
            if not is_allowed:
                minutes = remaining_time // 60
                seconds = remaining_time % 60
                time_msg = f"{minutes}m {seconds}s" if minutes > 0 else f"{seconds}s"

                return Response(
                    {
                        "errors": {
                            "email": [
                                f"Too many requests. Please wait {time_msg} before trying again."
                            ]
                        }
                    },
                    status=status.HTTP_429_TOO_MANY_REQUESTS,
                )

            try:
                user = User.objects.get(email=email)

                if user.is_verified:
                    return Response(
                        {"detail": "Email is already verified."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # Record the attempt for rate limiting
                self.record_attempt(email)

                # Send verification code
                success, message = self.send_verification_code(email)
                if success:
                    return Response(
                        {
                            "detail": "Verification code sent successfully. Please check your email."
                        },
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "errors": {
                                "general": [
                                    "Failed to send verification code. Please try again later."
                                ]
                            }
                        },
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )

            except User.DoesNotExist:
                # For security, we don't want to leak whether an email exists in the system
                # So we record the attempt and return a generic success message
                self.record_attempt(email)
                return Response(
                    {
                        "detail": "If the email exists in our system and is not verified, a verification code has been sent."
                    },
                    status=status.HTTP_200_OK,
                )

        except ValidationError as e:
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Resend verification error: {str(e)}", exc_info=True)
            return Response(
                {
                    "errors": {
                        "general": [
                            "An unexpected error occurred. Please try again later."
                        ]
                    }
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetRequestView(EmailVerificationMixin, generics.GenericAPIView):
    serializer_class = PasswordResetSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email = serializer.validated_data["email"]
            user = User.objects.filter(email=email).first()

            if not user:
                return Response(
                    {"errors": {"email": ["User with this email does not exist."]}},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            success, message = self.send_password_reset_code(email)
            if success:
                return Response(
                    {
                        "detail": "Password reset instructions have been sent to your email."
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "errors": {
                            "general": [
                                "Failed to send reset instructions. Please try again later."
                            ]
                        }
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Password reset request error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class PasswordResetConfirmView(EmailVerificationMixin, generics.GenericAPIView):
    serializer_class = SetNewPasswordSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]

            if self.verify_code(email, verification_code):
                user = User.objects.get(email=email)
                user.set_password(serializer.validated_data["password"])
                user.save()

                # Send password change notification email asynchronously
                try:
                    context = {
                        "change_time": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "location": "Password Reset",
                        "device": "Web Browser",
                    }
                    send_password_changed_email_task.delay(user.email, context)
                    logger.info(f"Password reset notification queued for {user.email}")
                except Exception as e:
                    logger.warning(
                        f"Failed to queue password reset notification: {str(e)}"
                    )

                return Response(
                    {
                        "detail": "Password has been reset successfully. You can now login with your new password."
                    },
                    status=status.HTTP_200_OK,
                )

            return Response(
                {
                    "errors": {
                        "verification_code": ["Invalid or expired verification code."]
                    }
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except ObjectDoesNotExist:
            return Response(
                {"errors": {"email": ["User not found."]}},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.error(f"Password reset confirm error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class LoginView(ObtainAuthToken):
    serializer_class = LoginSerializer

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get client information before authentication
            ip_address = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")

            # Authenticate user
            user = authenticate(
                request,
                username=serializer.validated_data["email"],
                password=serializer.validated_data["password"],
            )

            if not user:
                logger.warning(f"Failed login attempt from {ip_address}")
                return Response(
                    {"errors": {"credentials": ["Invalid email or password."]}},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            if not user.is_verified:
                logger.warning(f"Unverified user login attempt: {user.email}")
                return Response(
                    {
                        "errors": {
                            "verification": [
                                "Please verify your email before logging in."
                            ]
                        }
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            # Start async location processing
            login_location_task.delay(
                user_id=str(user.id),
                ip_address=ip_address,
                user_agent=user_agent,
                login_time=timezone.now().isoformat(),
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "name": user.name,
                        "is_verified": user.is_verified,
                        "is_admin": user.is_admin,
                    },
                    "detail": "Login successful. Welcome back!",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Login error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": ["Authentication failed. Please try again."]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SupportLoginView(LoginView):
    """Special login endpoint for staff and admin users only."""

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get client information before authentication
            ip_address = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")

            # Authenticate user
            user = authenticate(
                request,
                username=serializer.validated_data["email"],
                password=serializer.validated_data["password"],
            )

            if not user:
                logger.warning(f"Failed staff login attempt from {ip_address}")
                return Response(
                    {"errors": {"credentials": ["Invalid email or password."]}},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            if not user.is_verified:
                logger.warning(f"Unverified staff user login attempt: {user.email}")
                return Response(
                    {
                        "errors": {
                            "verification": [
                                "Please verify your email before logging in."
                            ]
                        }
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            # Staff-specific check
            if not (user.is_staff or user.is_admin):
                logger.warning(
                    f"Non-staff user attempted to access staff login: {user.email}"
                )
                return Response(
                    {
                        "errors": {
                            "authorization": [
                                "Access denied. Staff credentials required."
                            ]
                        }
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Start async location processing
            login_location_task.delay(
                user_id=str(user.id),
                ip_address=ip_address,
                user_agent=user_agent,
                login_time=timezone.now().isoformat(),
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "name": user.name,
                        "is_verified": user.is_verified,
                        "is_admin": user.is_admin,
                        "is_staff": user.is_staff,
                    },
                    "detail": "Staff login successful. Access granted to admin panel.",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Staff login error: {str(e)}", exc_info=True)
            return Response(
                {
                    "errors": {
                        "general": ["Staff authentication failed. Please try again."]
                    }
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class UserProfileView(generics.RetrieveUpdateAPIView):
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_object(self):
        """
        Returns the UserProfile instance for the authenticated user.
        Creates it if it doesn't exist.
        """
        profile = UserProfile.objects.get(user=self.request.user)
        return profile

    def perform_update(self, serializer):
        try:
            user = self.request.user
            phone = self.request.data.get("phone")
            name = self.request.data.get("name")

            # Update user fields if provided
            if phone or name:
                user.phone = phone if phone else user.phone
                user.name = name if name else user.name
                user.save()

            # Update profile image if provided
            if "image" in self.request.data:
                serializer.save(user=user, image=self.request.data["image"])
            else:
                serializer.save(user=user)

        except Exception as e:
            raise ValidationError({"detail": str(e)})


class LogoutView(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            Token.objects.filter(user=request.user).delete()  # Delete the auth token
            # Manually log out the user
            logout(request)

            # Revoke the refresh token
            refresh_token = request.data.get("refresh")
            if not refresh_token:
                return Response(
                    {
                        "errors": {
                            "refresh_token": ["Refresh token is required for logout."]
                        }
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                token = RefreshToken(refresh_token)
                token.blacklist()  # Blacklist the refresh token and, by extension, the access token
            except Exception as e:
                logger.warning(f"Token blacklist error: {str(e)}")
                return Response(
                    {"errors": {"refresh_token": ["Invalid refresh token."]}},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {"detail": "Successfully logged out. All tokens have been revoked."},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.error(f"Logout error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"Logout failed: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AccountDeleteView(views.APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request):
        try:
            user = request.user
            user_email = user.email
            user_name = user.name

            # Delete the user
            user.delete()

            # Send deletion confirmation email asynchronously
            try:
                send_account_deletion_email_task.delay(user_email, user_name)
                logger.info(f"Account deletion email queued for {user_email}")
            except Exception as e:
                logger.error(f"Failed to queue account deletion email: {str(e)}")
                # Don't fail the request if email queueing fails

            return Response(
                {
                    "detail": "Your account has been permanently deleted. We're sorry to see you go."
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.error(f"Account deletion error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"Account deletion failed: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ChangePasswordView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ChangePasswordSerializer

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Verify current password
            user = request.user
            if not user.check_password(serializer.validated_data["current_password"]):
                return Response(
                    {
                        "errors": {
                            "current_password": ["Current password is incorrect."]
                        }
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Change password
            user.set_password(serializer.validated_data["new_password"])
            user.save()

            # Logout from all sessions if requested
            if serializer.validated_data.get("logout_all_sessions"):
                tokens = OutstandingToken.objects.filter(user=user)
                for token in tokens:
                    BlacklistedToken.objects.get_or_create(token=token)

            # Get device and location info for security notification
            user_agent_string = request.META.get("HTTP_USER_AGENT", "")
            user_agent = parse(user_agent_string)
            device = f"{user_agent.browser.family} on {user_agent.os.family}"

            # Get location info using GeoIP2
            try:
                g = GeoIP2()
                ip = request.META.get("REMOTE_ADDR")
                location_info = g.city(ip)
                location = f"{location_info['city']}, {location_info['country_name']}"
            except Exception:
                location = "Unknown location"

            # Send security notification email asynchronously
            try:
                context = {
                    "change_time": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "location": location,
                    "device": device,
                }
                send_password_changed_email_task.delay(user.email, context)
                logger.info(f"Password change notification queued for {user.email}")
            except Exception as e:
                # Log the error but don't fail the request
                logger.error(f"Failed to queue password change email: {str(e)}")

            logout_message = (
                " All sessions have been logged out."
                if serializer.validated_data.get("logout_all_sessions")
                else ""
            )

            return Response(
                {"detail": f"Password changed successfully.{logout_message}"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Change password error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"Password change failed: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TeamSignupView(generics.CreateAPIView):
    serializer_class = TeamUserSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            # Validate but handle errors manually
            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if the invite exists and is valid
            from apps.stay.models import TeamInvite

            team_invite = TeamInvite.objects.filter(
                id=serializer.validated_data["invite_id"],
                email=serializer.validated_data["email"],
                is_registered=False,
                accepted=False,
            ).first()

            if not team_invite:
                return Response(
                    {"errors": {"invite_id": ["Invalid or expired team invitation."]}},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create the user
            user = serializer.save()

            from apps.stay.utils import approve_invite

            if approve_invite(team_invite, user):
                try:
                    send_welcome_email.delay(user.email)
                    logger.info(f"Welcome email queued for team member {user.email}")
                except Exception as e:
                    logger.warning(f"Failed to queue welcome email: {str(e)}")

                return Response(
                    {
                        "user": UserSerializer(
                            user, context=self.get_serializer_context()
                        ).data,
                        "detail": "Team member registered successfully.",
                    },
                    status=status.HTTP_201_CREATED,
                )

        except ValidationError as e:
            # Handle DRF ValidationError
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle other exceptions
            logger.error(f"Team signup error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )
