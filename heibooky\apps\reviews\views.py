import base64
import json
import logging
from datetime import datetime

from apps.booking.models import Booking
from apps.integrations.utils import log_action
from apps.stay.models import Property
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from services.su_api import reply_to_review

from .models import Review
from .serializers import ReviewResponseSerializer, ReviewSerializer

logger = logging.getLogger(__name__)


class ReviewsViewset(viewsets.ModelViewSet):
    """Viewset for Review model."""

    queryset = Review.objects.all()
    serializer_class = ReviewSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Filter reviews based on user permissions and staff association.
        Returns reviews only for properties where user is staff.
        """
        user = self.request.user

        if user.is_superuser:
            return Review.objects.all()

        try:
            # Get reviews for properties where user is staff
            return Review.objects.filter(property__staffs=user).distinct()
        except Exception:
            # Log error if needed
            return Review.objects.none()

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def mark_all_read(self, request):
        """Mark all unread reviews as read for the current user."""
        unread_reviews = self.get_queryset().filter(is_read=False)
        count = unread_reviews.count()

        if count > 0:
            unread_reviews.update(is_read=True)

        return Response(
            {
                "status": "success",
                "message": f"Marked {count} reviews as read",
                "count": count,
            }
        )

    @action(detail=True, methods=["post"], serializer_class=ReviewResponseSerializer)
    def respond(self, request, pk=None):
        """Add a response to a review and send it to SU API."""
        review = self.get_object()
        serializer = ReviewResponseSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        response_text = serializer.validated_data["response_text"]

        try:
            # Send response to SU API
            api_response = reply_to_review(
                property_id=review.property.hotel_id,
                channel_id=review.channel_id,
                review_id=review.external_review_id,
                reply_text=response_text,
                listing_id=review.listing_id,
                location_id=(
                    review.booking.reservation_data.id if review.booking else None
                ),
            )

            if api_response.get("error"):
                logger.error(
                    f"SU API error when replying to review {review.id}: {api_response['error']}"
                )
                return Response(
                    {"error": "Failed to send response to channel"},
                    status=status.HTTP_502_BAD_GATEWAY,
                )

            # Store response locally
            response_data = review.add_response(request.user, response_text)
            review.save()

            # Log the action
            log_action(
                user=request.user,
                property_id=review.property.id,
                action="create",
                description=f"Response added to review {review.external_review_id}",
                status="successful",
                details={"response": api_response},
            )

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.exception(f"Error responding to review: {e}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def mark_read(self, request, pk=None):
        """Mark a review as read."""
        review = self.get_object()
        if not review.is_read:
            review.is_read = True
            review.save(update_fields=["is_read"])
        return Response(
            {
                "status": "marked as read",
                "review_id": str(review.id),
                "is_read": review.is_read,
            }
        )


@csrf_exempt
@require_http_methods(["POST"])
def review_webhook(request):
    """Handle incoming review webhooks from SU API."""

    # Verify Basic Auth
    # auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    # if not verify_auth(auth_header):
    #     return JsonResponse({'error': 'Unauthorized'}, status=401)

    try:
        data = json.loads(request.body)
        review_data = data.get("review", {})

        if not review_data or review_data.get("action") != "review_published":
            return JsonResponse({"status": "ignored"}, status=200)

        # Get or create review
        review = Review.objects.filter(
            external_review_id=str(review_data["reviewid"])
        ).first()
        if review:
            return JsonResponse({"status": "review already exists"}, status=200)

        # Find related property and booking
        property_instance = Property.objects.get(hotel_id=data["hotelcode"])
        booking = None
        if data.get("bookingid"):
            booking = Booking.objects.filter(reservation_data=data["bookingid"]).first()

        # Create review
        Review.objects.create(
            external_review_id=str(review_data["reviewid"]),
            property=property_instance,
            booking=booking,
            public_review=review_data.get("public_review"),
            private_feedback=review_data.get("private_feedback"),
            ratings=review_data.get("ratings", {}),
            reviewer_id=str(review_data["reviewerid"]),
            reviewer_role=review_data["reviewer_role"],
            reviewee_id=str(review_data["revieweeid"]),
            reviewee_role=review_data["reviewee_role"],
            channel_id=data["channel_id"],
            thread_id=data.get("threadid"),
            is_hidden=review_data.get("hidden", False),
            submitted_at=datetime.fromisoformat(
                review_data["submitted_at"].replace("Z", "+00:00")
            ),
            expires_at=(
                datetime.fromisoformat(review_data["expires_at"].replace("Z", "+00:00"))
                if review_data.get("expires_at")
                else None
            ),
        )

        return JsonResponse({"status": "success"}, status=201)

    except ObjectDoesNotExist:
        return JsonResponse({"error": "Property not found"}, status=404)
    except (KeyError, json.JSONDecodeError, ValueError) as e:
        return JsonResponse({"error": str(e)}, status=400)


def verify_auth(auth_header):
    """Verify Basic Auth credentials."""
    try:
        auth_type, auth_string = auth_header.split(" ", 1)
        if auth_type.lower() != "basic":
            return False

        decoded = base64.b64decode(auth_string).decode()
        username, password = decoded.split(":", 1)

        return (
            username == settings.SU_API_USERNAME
            and password == settings.SU_API_PASSWORD
        )
    except Exception:
        return False
