from apps.stay.utils import cleaning_staff_check
from rest_framework import serializers

from .models import *


class RatePlanSerializer(serializers.ModelSerializer):
    checkin_time = serializers.TimeField(format="%H:%M", input_formats=["%H:%M"])
    close_out_time = serializers.TimeField(format="%H:%M", input_formats=["%H:%M"])

    class Meta:
        model = RatePlan
        fields = [
            "id",
            "property",
            "name",
            "close_out_days",
            "checkin_time",
            "close_out_time",
            "description",
            "meal_plan",
            "is_active",
            "is_onboarded",
        ]
        read_only_fields = ["is_onboarded"]

    def validate_close_out_days(self, value):
        # Ensure close_out_days is within the range 0 to 30, or can be blank
        if value is not None and not (0 <= value <= 30):
            raise serializers.ValidationError(
                "Close-out days must be between 0 and 30."
            )
        return value

    def validate_close_out_time(self, value):
        # Ensure close_out_time has 30-minute intervals if provided
        if value and value.minute not in [0, 30]:
            raise serializers.ValidationError(
                "Close-out time must be in 30-minute intervals (e.g., 00:00, 00:30)."
            )
        return value

    def validate(self, attrs):
        close_out_days = attrs.get("close_out_days")
        close_out_time = attrs.get("close_out_time")

        # Ensure close_out_time is required when close_out_days is 0
        if close_out_days == 0 and close_out_time is None:
            raise serializers.ValidationError(
                "Close-out time is required if close-out days is set to 0."
            )
        return attrs


class RoomRateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoomRate
        fields = [
            "id",
            "rate_plan",
            "room",
            "rate",
            "room_amount",
            "start_date",
            "end_date",
            "minimum_stay",
            "maximum_stay",
            "is_season",
            "is_active",
            "is_onboarded",
        ]
        read_only_fields = ["is_onboarded"]

    def create(self, validated_data):
        instance = super().create(validated_data)
        return instance

    def validate(self, data):
        data = super().validate(data)

        # Validate date ranges
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        today = now().date()

        if start_date:
            if start_date < today:
                raise serializers.ValidationError(
                    {"start_date": "Start date must be today or in the future."}
                )

        if start_date and end_date:
            if start_date >= end_date:
                raise serializers.ValidationError(
                    {"end_date": "End date must be greater than the start date"}
                )

            # Check if date range is too long
            date_range = (end_date - start_date).days
            if date_range > 365:
                raise serializers.ValidationError(
                    {"date_range": "Date range cannot exceed 365 days"}
                )

        return data

    def validate_minimum_stay(self, value):
        if not (1 <= value <= 730):
            raise serializers.ValidationError(
                "Minimum stay must be between 1 and 730 days."
            )
        return value

    def validate_maximum_stay(self, value):
        if not (1 <= value <= 730):
            raise serializers.ValidationError(
                "Maximum stay must be between 1 and 730 days."
            )
        return value

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        request = self.context.get("request")

        if request and cleaning_staff_check(instance.room.property, request.user):
            representation["rate"] = 0

        return representation


class CalendarRoomRateSerializer(serializers.Serializer):
    date = serializers.DateField()
    rates = serializers.SerializerMethodField()

    def get_rates(self, obj):
        return {str(room_id): rate for room_id, rate in obj["rates"].items()}
