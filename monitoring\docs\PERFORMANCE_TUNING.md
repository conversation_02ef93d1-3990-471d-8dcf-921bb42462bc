# Performance Tuning Guide

This guide provides comprehensive performance optimization strategies for the Heibooky monitoring stack, covering Prometheus, Grafana, Loki, and Alertmanager.

## Table of Contents

1. [Performance Overview](#performance-overview)
2. [Prometheus Optimization](#prometheus-optimization)
3. [Grafana Optimization](#grafana-optimization)
4. [Loki Optimization](#loki-optimization)
5. [Alertmanager Optimization](#alertmanager-optimization)
6. [Container Resource Optimization](#container-resource-optimization)
7. [Network Performance](#network-performance)
8. [Storage Optimization](#storage-optimization)
9. [Monitoring Performance](#monitoring-performance)
10. [Troubleshooting Performance Issues](#troubleshooting-performance-issues)

## Performance Overview

### Key Performance Metrics

| Component | Metric | Target | Alert Threshold |
|-----------|--------|--------|-----------------|
| Prometheus | Query Duration (95th) | < 500ms | > 1s |
| Prometheus | Ingestion Rate | > 1000 samples/sec | < 500 samples/sec |
| Grafana | Dashboard Load Time | < 2s | > 5s |
| Grafana | API Response Time | < 200ms | > 500ms |
| Loki | Query Duration (95th) | < 1s | > 3s |
| Loki | Ingestion Rate | > 1MB/sec | < 500KB/sec |
| System | Memory Usage | < 80% | > 90% |
| System | CPU Usage | < 70% | > 85% |

### Performance Testing

Use the performance monitoring script to regularly assess performance:

```bash
# Run complete performance analysis
./scripts/performance-monitor.sh all

# Run specific tests
./scripts/performance-monitor.sh benchmark
./scripts/performance-monitor.sh analyze
./scripts/performance-monitor.sh optimize
```

## Prometheus Optimization

### 1. Storage Optimization

#### Retention Policies
```yaml
# Optimize retention for performance vs storage
command:
  - '--storage.tsdb.retention.time=15d'  # Reduce for better performance
  - '--storage.tsdb.retention.size=10GB' # Limit storage usage
  - '--storage.tsdb.wal-compression'     # Enable WAL compression
```

#### Block Duration Tuning
```yaml
command:
  - '--storage.tsdb.min-block-duration=2h'   # Optimize for write performance
  - '--storage.tsdb.max-block-duration=36h'  # Balance query vs compaction
```

### 2. Query Optimization

#### Query Limits
```yaml
command:
  - '--query.timeout=2m'                # Prevent long-running queries
  - '--query.max-concurrency=20'        # Limit concurrent queries
  - '--query.max-samples=50000000'      # Limit sample processing
  - '--query.lookback-delta=5m'         # Optimize staleness handling
```

#### Recording Rules Strategy
```yaml
# High-frequency rules (15s) - Critical metrics only
- name: critical_metrics
  interval: 15s
  rules:
    - record: django:http_requests:rate5m
      expr: sum(rate(django_http_requests_total[5m])) by (job, instance, method)

# Medium-frequency rules (30s) - Dashboard metrics
- name: dashboard_metrics
  interval: 30s
  rules:
    - record: dashboard:cpu_usage_by_instance
      expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

# Low-frequency rules (60s) - SLI/SLO metrics
- name: sli_metrics
  interval: 60s
  rules:
    - record: sli:availability
      expr: rate(django_http_requests_total{status!~"5.."}[5m]) / rate(django_http_requests_total[5m])
```

### 3. Scrape Configuration Optimization

#### Tiered Scraping Strategy
```yaml
# High-priority targets (10s interval)
- job_name: 'django-app'
  scrape_interval: 10s
  scrape_timeout: 8s
  sample_limit: 10000

# Medium-priority targets (20s interval)
- job_name: 'redis'
  scrape_interval: 20s
  scrape_timeout: 15s
  sample_limit: 5000

# Low-priority targets (30s interval)
- job_name: 'postgres'
  scrape_interval: 30s
  scrape_timeout: 20s
  sample_limit: 3000
```

#### Metric Filtering
```yaml
metric_relabel_configs:
  # Keep only essential metrics
  - source_labels: [__name__]
    regex: 'django_(http_requests_total|http_request_duration_seconds.*|db_.*)'
    action: keep
  
  # Drop high-cardinality metrics
  - source_labels: [__name__]
    regex: 'django_http_requests_total'
    target_label: __tmp_keep
    replacement: 'yes'
  - source_labels: [__tmp_keep]
    regex: 'yes'
    action: keep
```

### 4. Memory Optimization

#### Heap Size Configuration
```bash
# Set appropriate heap size (50-60% of container memory)
PROMETHEUS_MEMORY_LIMIT=2G
# Heap will be approximately 1.2GB
```

#### Chunk Configuration
```yaml
# Optimize chunk settings for memory usage
chunks_to_persist: 524288
max_chunks_to_persist: 1048576
chunk_encoding: "XOR"  # Most efficient encoding
```

## Grafana Optimization

### 1. Database Optimization

#### SQLite Optimization (Default)
```ini
[database]
type = sqlite3
max_idle_conn = 10
max_open_conn = 100
conn_max_lifetime = 14400
wal = true
cache_mode = shared
query_timeout = 30s
query_retries = 3
```

#### PostgreSQL for High Load
```ini
[database]
type = postgres
host = postgres:5432
name = grafana
user = grafana
password = ${GF_DATABASE_PASSWORD}
ssl_mode = require
max_idle_conn = 25
max_open_conn = 300
conn_max_lifetime = 14400
```

### 2. Query Performance

#### Dashboard Query Optimization
```json
{
  "targets": [
    {
      "expr": "django:http_requests:rate5m",  // Use recording rules
      "interval": "30s",                      // Appropriate step size
      "maxDataPoints": 1000,                  // Limit data points
      "refId": "A"
    }
  ],
  "refresh": "30s",                          // Reasonable refresh rate
  "timeFrom": null,
  "timeShift": null
}
```

#### Template Variables Optimization
```json
{
  "templating": {
    "list": [
      {
        "name": "instance",
        "query": "label_values(up, instance)",
        "refresh": 2,                        // On time range change
        "sort": 1,                          // Alphabetical sort
        "includeAll": true,
        "multi": true
      }
    ]
  }
}
```

### 3. Caching Configuration

#### Query Result Caching
```ini
[caching]
enabled = true

[dataproxy]
timeout = 30
dial_timeout = 10
keep_alive_seconds = 30
max_idle_connections = 100
max_idle_connections_per_host = 10
idle_conn_timeout = 90
```

### 4. Rendering Optimization

#### Concurrent Rendering Limits
```ini
[rendering]
concurrent_render_request_limit = 30
rendering_timeout = 20s
rendering_ignore_https_errors = false
```

## Loki Optimization

### 1. Ingestion Optimization

#### Rate Limiting
```yaml
limits_config:
  ingestion_rate_mb: 4              # Per-tenant ingestion rate
  ingestion_burst_size_mb: 6        # Burst allowance
  max_line_size: 256000             # Maximum log line size
  per_stream_rate_limit: 3MB        # Per-stream rate limit
  per_stream_rate_limit_burst: 15MB # Per-stream burst limit
```

#### Ingester Configuration
```yaml
ingester:
  chunk_idle_period: 1h             # How long chunks stay in memory
  max_chunk_age: 1h                 # Maximum chunk age before flushing
  chunk_target_size: 1048576        # Target chunk size (1MB)
  chunk_retain_period: 30s          # Retention period for chunks
```

### 2. Query Optimization

#### Query Limits
```yaml
limits_config:
  max_query_parallelism: 32         # Parallel query execution
  max_entries_limit_per_query: 5000 # Limit entries per query
  max_query_length: 721h            # Maximum query time range
  split_queries_by_interval: 30m    # Split large queries
```

#### Query Frontend Configuration
```yaml
frontend:
  max_outstanding_per_tenant: 256   # Outstanding requests per tenant
  compress_responses: true          # Enable response compression
  log_queries_longer_than: 5s       # Log slow queries
```

### 3. Storage Optimization

#### Compaction Settings
```yaml
compactor:
  working_directory: /loki/compactor
  compaction_interval: 10m          # Frequent compaction
  retention_enabled: true
  retention_delete_delay: 2h        # Delay before deletion
  retention_delete_worker_count: 150 # Parallel deletion workers
```

#### Caching Configuration
```yaml
query_range:
  cache_results: true
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 500              # 500MB cache
        ttl: 1h                       # 1 hour TTL
```

## Alertmanager Optimization

### 1. Grouping Optimization

#### Efficient Grouping Strategy
```yaml
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s                   # Quick initial grouping
  group_interval: 5m                # Frequent group updates
  repeat_interval: 12h              # Reasonable repeat interval
```

### 2. Notification Optimization

#### Timeout and Retry Configuration
```yaml
global:
  smtp_smarthost: 'smtp.example.com:587'
  smtp_timeout: 10s                 # Quick timeout

receivers:
- name: 'email-alerts'
  email_configs:
  - to: '<EMAIL>'
    send_resolved: true
    html: '{{ template "email.html" . }}'
    headers:
      Subject: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
```

### 3. Storage Optimization

#### Data Retention
```yaml
# Command line options
--data.retention=120h               # 5 days retention
--storage.path=/alertmanager        # Persistent storage
```

## Container Resource Optimization

### 1. Resource Allocation Strategy

#### Production Resource Limits
```yaml
services:
  prometheus:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  grafana:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  loki:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### 2. Health Check Optimization

#### Efficient Health Checks
```yaml
healthcheck:
  test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
  interval: 30s                     # Reasonable interval
  timeout: 10s                      # Quick timeout
  retries: 3                        # Limited retries
  start_period: 40s                 # Startup grace period
```

## Network Performance

### 1. Network Segmentation

#### Optimized Network Configuration
```yaml
networks:
  monitoring:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1500
    ipam:
      config:
        - subnet: **********/16
```

## Storage Optimization

### 1. Volume Performance

#### SSD Storage Recommendations
```bash
# Use SSD storage for better I/O performance
# Mount with appropriate options
mount -o noatime,nodiratime /dev/ssd /var/lib/prometheus
```

#### Volume Configuration
```yaml
volumes:
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind,noatime
      device: /fast-storage/prometheus
```

### 2. Backup Strategy

#### Efficient Backup Configuration
```bash
# Use incremental backups
rsync -av --delete /var/lib/prometheus/ /backup/prometheus/

# Compress old data
find /backup/prometheus -name "*.db" -mtime +7 -exec gzip {} \;
```

## Monitoring Performance

### 1. Performance Metrics Dashboard

Create dashboards to monitor:
- Query duration percentiles
- Ingestion rates
- Memory usage trends
- Disk I/O patterns
- Network throughput
- Error rates

### 2. Performance Alerts

#### Critical Performance Alerts
```yaml
groups:
- name: performance_alerts
  rules:
  - alert: HighQueryLatency
    expr: prometheus_engine_query_duration_seconds{quantile="0.95"} > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High query latency detected"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage detected"
```

## Troubleshooting Performance Issues

### 1. Common Performance Problems

#### Slow Queries
```bash
# Check query log
grep "slow" /prometheus/query.log

# Analyze query patterns
curl -s "http://localhost:9090/api/v1/status/tsdb" | jq '.data.headStats'
```

#### High Memory Usage
```bash
# Check memory usage
docker stats prometheus

# Analyze series cardinality
curl -s "http://localhost:9090/api/v1/label/__name__/values" | jq '.data | length'
```

#### Disk I/O Issues
```bash
# Monitor disk usage
iostat -x 1

# Check Prometheus metrics
curl -s "http://localhost:9090/metrics" | grep prometheus_tsdb
```

### 2. Performance Optimization Checklist

- [ ] Recording rules implemented for complex queries
- [ ] Appropriate scrape intervals configured
- [ ] Metric filtering applied to reduce cardinality
- [ ] Resource limits set appropriately
- [ ] Health checks optimized
- [ ] Caching enabled where applicable
- [ ] Retention policies configured
- [ ] Performance monitoring in place
- [ ] Regular performance testing scheduled
- [ ] Backup strategy optimized

### 3. Scaling Strategies

#### Horizontal Scaling
```yaml
# Prometheus federation for scaling
- job_name: 'federate'
  scrape_interval: 15s
  honor_labels: true
  metrics_path: '/federate'
  params:
    'match[]':
      - '{job=~"prometheus"}'
  static_configs:
    - targets:
      - 'prometheus-1:9090'
      - 'prometheus-2:9090'
```

#### Vertical Scaling
```yaml
# Increase resources for single instance
deploy:
  resources:
    limits:
      memory: 4G      # Double memory
      cpus: '4.0'     # Double CPU
```

---

**Performance Monitoring**: Regular performance assessment is crucial for maintaining optimal monitoring stack performance. Use the provided tools and guidelines to ensure your monitoring infrastructure scales with your application needs.
