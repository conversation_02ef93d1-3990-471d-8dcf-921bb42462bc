{"dashboard": {"id": null, "title": "SLI/SLO Monitoring - Heibooky", "tags": ["he<PERSON><PERSON>y", "sli", "slo", "reliability", "production"], "style": "dark", "timezone": "browser", "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "Service Level Objectives Overview", "type": "stat", "targets": [{"expr": "(\n  sum(rate(django_http_requests_total{status!~\"5..\"}[30d])) /\n  sum(rate(django_http_requests_total[30d]))\n) * 100", "legendFormat": "Availability SLO", "refId": "A"}, {"expr": "(\n  sum(rate(django_http_request_duration_seconds_bucket{le=\"0.5\"}[30d])) /\n  sum(rate(django_http_request_duration_seconds_count[30d]))\n) * 100", "legendFormat": "Latency SLO (500ms)", "refId": "B"}, {"expr": "(\n  1 - (\n    sum(rate(django_http_requests_total{status=~\"5..\"}[30d])) /\n    sum(rate(django_http_requests_total[30d]))\n  )\n) * 100", "legendFormat": "Error Budget", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Availability SLI (99.9% Target)", "type": "timeseries", "targets": [{"expr": "(\n  sum(rate(django_http_requests_total{status!~\"5..\"}[5m])) /\n  sum(rate(django_http_requests_total[5m]))\n) * 100", "legendFormat": "Current Availability", "refId": "A"}, {"expr": "99.9", "legendFormat": "SLO Target (99.9%)", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "min": 95, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 3, "title": "Latency SLI (500ms Target)", "type": "timeseries", "targets": [{"expr": "(\n  sum(rate(django_http_request_duration_seconds_bucket{le=\"0.5\"}[5m])) /\n  sum(rate(django_http_request_duration_seconds_count[5m]))\n) * 100", "legendFormat": "Requests < 500ms", "refId": "A"}, {"expr": "95", "legendFormat": "SLO Target (95%)", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "percent", "min": 80, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 4, "title": "Error Budget Burn Rate", "type": "timeseries", "targets": [{"expr": "(\n  sum(rate(django_http_requests_total{status=~\"5..\"}[1h])) /\n  sum(rate(django_http_requests_total[1h]))\n) / (1 - 0.999) * 100", "legendFormat": "1h Burn Rate", "refId": "A"}, {"expr": "(\n  sum(rate(django_http_requests_total{status=~\"5..\"}[6h])) /\n  sum(rate(django_http_requests_total[6h]))\n) / (1 - 0.999) * 100", "legendFormat": "6h Burn Rate", "refId": "B"}, {"expr": "100", "legendFormat": "Critical Threshold", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "percent", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Request Volume SLI", "type": "timeseries", "targets": [{"expr": "sum(rate(django_http_requests_total[5m]))", "legendFormat": "Request Rate (req/s)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "SLO Compliance Status", "type": "table", "targets": [{"expr": "(\n  sum(rate(django_http_requests_total{status!~\"5..\"}[30d])) /\n  sum(rate(django_http_requests_total[30d]))\n) * 100", "legendFormat": "Availability", "refId": "A", "format": "table", "instant": true}, {"expr": "(\n  sum(rate(django_http_request_duration_seconds_bucket{le=\"0.5\"}[30d])) /\n  sum(rate(django_http_request_duration_seconds_count[30d]))\n) * 100", "legendFormat": "Latency", "refId": "B", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent", "custom": {"align": "auto", "displayMode": "color-background"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}], "templating": {"list": [{"name": "time_window", "type": "custom", "options": [{"text": "1h", "value": "1h"}, {"text": "6h", "value": "6h"}, {"text": "24h", "value": "24h"}, {"text": "7d", "value": "7d"}, {"text": "30d", "value": "30d"}], "current": {"text": "1h", "value": "1h"}}]}, "annotations": {"list": [{"name": "SLO Violations", "datasource": "Prometheus", "enable": true, "expr": "ALERTS{alertname=~\".*SLO.*\"}", "iconColor": "red", "titleFormat": "SLO Alert: {{alertname}}", "textFormat": "{{description}}"}]}}, "overwrite": true}