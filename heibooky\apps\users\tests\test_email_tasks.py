"""
Tests for async email tasks in the users app.
"""

import logging
from unittest.mock import Mock, patch

from celery import Celery
from django.core.cache import cache
from django.test import TestCase, override_settings
from django.utils import timezone

from apps.users.models import User
from apps.users.tasks import (
    generate_and_send_verification_code,
    send_account_deletion_email_task,
    send_new_login_location_email_task,
    send_password_changed_email_task,
    send_verification_email_task,
    send_welcome_email,
)

# Disable logging during tests to reduce noise
logging.disable(logging.CRITICAL)

# Use eager task execution for testing
app = Celery("test")
app.conf.task_always_eager = True
app.conf.task_eager_propagates = True


class BaseEmailTaskTest(TestCase):
    """Base test class for email tasks"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email="<EMAIL>", name="Test User", password="testpassword123"
        )
        # Clear cache before each test
        cache.clear()

    def tearDown(self):
        """Clean up after each test"""
        cache.clear()


class TestWelcomeEmailTask(BaseEmailTaskTest):
    """Tests for send_welcome_email task"""

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_welcome_email_success(self, mock_email_service):
        """Test successful welcome email sending"""
        # Mock the email service
        mock_service_instance = Mock()
        mock_service_instance.send_welcome_email.return_value = True
        mock_email_service.return_value = mock_service_instance

        # Execute the task
        result = send_welcome_email.apply(args=[self.user.email])

        # Verify
        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_service_instance.send_welcome_email.assert_called_once()

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_welcome_email_user_not_found(self, mock_email_service):
        """Test welcome email with non-existent user"""
        result = send_welcome_email.apply(args=["<EMAIL>"])

        # Should return False but not fail
        self.assertTrue(result.successful())
        self.assertFalse(result.result)

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_welcome_email_service_failure(self, mock_email_service):
        """Test welcome email when service fails"""
        # Mock service to raise exception
        mock_service_instance = Mock()
        mock_service_instance.send_welcome_email.side_effect = Exception(
            "Email service down"
        )
        mock_email_service.return_value = mock_service_instance

        # Execute the task
        result = send_welcome_email.apply(args=[self.user.email])

        # Should handle the error gracefully after retries
        self.assertTrue(result.successful())
        self.assertFalse(result.result)


class TestVerificationEmailTask(BaseEmailTaskTest):
    """Tests for send_verification_email_task"""

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_verification_email_success(self, mock_email_service):
        """Test successful verification email sending"""
        mock_service_instance = Mock()
        mock_service_instance.send_verification_email.return_value = True
        mock_email_service.return_value = mock_service_instance

        result = send_verification_email_task.apply(args=[self.user.email, "1234"])

        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_service_instance.send_verification_email.assert_called_once_with(
            self.user.email, "1234"
        )

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_verification_email_user_not_found(self, mock_email_service):
        """Test verification email with non-existent user"""
        result = send_verification_email_task.apply(
            args=["<EMAIL>", "1234"]
        )

        self.assertTrue(result.successful())
        self.assertFalse(result.result)


class TestGenerateAndSendVerificationCode(BaseEmailTaskTest):
    """Tests for generate_and_send_verification_code task"""

    @patch("apps.users.tasks.VerificationService")
    @patch("apps.users.tasks.AccountEmailService")
    def test_generate_and_send_verification_success(
        self, mock_email_service, mock_verification_service
    ):
        """Test successful code generation and sending"""
        # Mock services
        mock_verification_instance = Mock()
        mock_verification_instance.generate_code.return_value = "1234"
        mock_verification_service.return_value = mock_verification_instance

        mock_email_instance = Mock()
        mock_email_instance.send_verification_email.return_value = True
        mock_email_service.return_value = mock_email_instance

        # Execute task
        result = generate_and_send_verification_code.apply(
            args=[self.user.email, "verification"]
        )

        # Verify
        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_verification_instance.generate_code.assert_called_once()
        mock_verification_instance.store_code.assert_called_once_with(
            self.user.email, "1234"
        )
        mock_email_instance.send_verification_email.assert_called_once_with(
            self.user.email, "1234"
        )

    @patch("apps.users.tasks.VerificationService")
    @patch("apps.users.tasks.AccountEmailService")
    def test_generate_and_send_password_reset(
        self, mock_email_service, mock_verification_service
    ):
        """Test password reset code generation and sending"""
        # Mock services
        mock_verification_instance = Mock()
        mock_verification_instance.generate_code.return_value = "5678"
        mock_verification_service.return_value = mock_verification_instance

        mock_email_instance = Mock()
        mock_email_instance.send_password_reset_email.return_value = True
        mock_email_service.return_value = mock_email_instance

        # Execute task
        result = generate_and_send_verification_code.apply(
            args=[self.user.email, "password_reset"]
        )

        # Verify
        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_email_instance.send_password_reset_email.assert_called_once_with(
            self.user.email, "5678"
        )

    def test_generate_and_send_invalid_action(self):
        """Test with invalid action parameter"""
        result = generate_and_send_verification_code.apply(
            args=[self.user.email, "invalid_action"]
        )

        # Should fail due to invalid action
        self.assertTrue(result.successful())
        self.assertFalse(result.result)


class TestPasswordChangedEmailTask(BaseEmailTaskTest):
    """Tests for send_password_changed_email_task"""

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_password_changed_email_success(self, mock_email_service):
        """Test successful password changed email"""
        mock_service_instance = Mock()
        mock_service_instance.send_password_changed_email.return_value = True
        mock_email_service.return_value = mock_service_instance

        context = {
            "change_time": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
            "location": "New York, US",
            "device": "Chrome on Windows",
        }

        result = send_password_changed_email_task.apply(args=[self.user.email, context])

        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_service_instance.send_password_changed_email.assert_called_once_with(
            self.user.email, context
        )


class TestAccountDeletionEmailTask(BaseEmailTaskTest):
    """Tests for send_account_deletion_email_task"""

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_account_deletion_email_success(self, mock_email_service):
        """Test successful account deletion email"""
        mock_service_instance = Mock()
        mock_service_instance.send_account_deletion_email.return_value = True
        mock_email_service.return_value = mock_service_instance

        result = send_account_deletion_email_task.apply(
            args=[self.user.email, self.user.name]
        )

        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_service_instance.send_account_deletion_email.assert_called_once_with(
            self.user.email, self.user.name
        )


class TestNewLoginLocationEmailTask(BaseEmailTaskTest):
    """Tests for send_new_login_location_email_task"""

    @patch("apps.users.tasks.AccountEmailService")
    def test_send_new_login_location_email_success(self, mock_email_service):
        """Test successful new login location email"""
        mock_service_instance = Mock()
        mock_service_instance.send_new_login_location_email.return_value = True
        mock_email_service.return_value = mock_service_instance

        context = {
            "user_name": self.user.name,
            "login_time": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
            "location": "London, UK",
            "device": "Safari on iOS",
            "ip_address": "***********",
        }

        result = send_new_login_location_email_task.apply(
            args=[self.user.email, context]
        )

        self.assertTrue(result.successful())
        self.assertTrue(result.result)
        mock_service_instance.send_new_login_location_email.assert_called_once_with(
            self.user.email, context
        )


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class TestTaskRetryLogic(BaseEmailTaskTest):
    """Tests for task retry logic and error handling"""

    @patch("apps.users.tasks.AccountEmailService")
    def test_task_retry_on_failure(self, mock_email_service):
        """Test that tasks retry on failure"""
        # Mock service to fail initially, then succeed
        mock_service_instance = Mock()
        mock_service_instance.send_welcome_email.side_effect = [
            Exception("Temporary failure"),
            Exception("Another failure"),
            True,  # Success on third try
        ]
        mock_email_service.return_value = mock_service_instance

        # Execute the task - in eager mode, retries are not actually executed
        # This test mainly ensures the retry logic structure is correct
        result = send_welcome_email.apply(args=[self.user.email])

        # In eager mode, the task will fail after the first exception
        self.assertTrue(result.successful())
        self.assertFalse(result.result)

    @patch("apps.users.tasks.AccountEmailService")
    def test_task_final_failure_after_retries(self, mock_email_service):
        """Test task behavior after all retries are exhausted"""
        mock_service_instance = Mock()
        mock_service_instance.send_welcome_email.side_effect = Exception(
            "Persistent failure"
        )
        mock_email_service.return_value = mock_service_instance

        result = send_welcome_email.apply(args=[self.user.email])

        # Should complete but return False indicating failure
        self.assertTrue(result.successful())
        self.assertFalse(result.result)


# Re-enable logging after tests
logging.disable(logging.NOTSET)
