#!/bin/bash

# Update & Upgrade APT (Debian/Ubuntu packages)
echo "Updating APT packages..."
sudo apt update && sudo apt upgrade -y

# Update Snap packages (if installed)
if command -v snap &> /dev/null; then
    echo "Updating Snap packages..."
    sudo snap refresh
fi

# Update Flatpak packages (if installed)
if command -v flatpak &> /dev/null; then
    echo "Updating Flatpak packages..."
    flatpak update -y
fi

# Clean up unused packages
echo "Cleaning up..."
sudo apt autoremove -y
sudo apt autoclean

# Ask for reboot
read -p "Updates completed. Reboot now? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Rebooting..."
    sudo reboot
else
    echo "Reboot skipped."
fi