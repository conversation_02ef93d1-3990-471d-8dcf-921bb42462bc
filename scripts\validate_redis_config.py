#!/usr/bin/env python3
"""
Redis Configuration Validation Script
Validates Redis configuration files and Docker setup
"""
import sys
from pathlib import Path


def validate_redis_config():
    """Validate Redis configuration files and setup"""
    print("🔍 Validating Redis configuration...")

    # Check if we're in the correct directory
    if not Path("docker-compose.yml").exists():
        print(
            "❌ Error: docker-compose.yml not found. Run this script from the project root."
        )
        return False

    # Check Redis configuration file
    redis_conf = Path("redis/redis.conf")
    if not redis_conf.exists():
        print("❌ Error: redis/redis.conf not found")
        return False

    print("✅ redis/redis.conf exists")

    # Check ACL file
    acl_file = Path("redis/users.acl")
    if not acl_file.exists():
        print("❌ Error: redis/users.acl not found")
        return False

    print("✅ redis/users.acl exists")

    # Validate Redis configuration content
    with open(redis_conf, "r") as f:
        config_content = f.read()

    required_settings = [
        "protected-mode no",
        "bind 0.0.0.0",
        "port 6379",
        "aclfile /etc/redis/users.acl",
        "pidfile /data/redis_6379.pid",
    ]

    for setting in required_settings:
        if setting not in config_content:
            print(f"❌ Error: Required setting '{setting}' not found in redis.conf")
            return False
        print(f"✅ Found: {setting}")

    # Validate ACL file content
    with open(acl_file, "r") as f:
        acl_lines = [line.strip() for line in f.readlines() if line.strip()]

    if not acl_lines:
        print("❌ Error: ACL file is empty")
        return False

    # Check that all lines start with 'user'
    for line in acl_lines:
        if not line.startswith("user "):
            print(f"❌ Error: ACL line must start with 'user': {line}")
            return False

    if "user default on nopass" not in " ".join(acl_lines):
        print("❌ Error: Default user configuration not found in users.acl")
        return False

    print("✅ ACL file contains valid default user configuration")

    # Check Docker Compose files
    compose_files = ["docker-compose.yml", "docker-compose.prod.yml"]

    for compose_file in compose_files:
        if not Path(compose_file).exists():
            print(f"⚠️  Warning: {compose_file} not found")
            continue

        with open(compose_file, "r") as f:
            compose_content = f.read()

        required_mounts = [
            "./redis/redis.conf:/etc/redis/redis.conf:ro",
            "./redis/users.acl:/etc/redis/users.acl:ro",
        ]

        for mount in required_mounts:
            if mount not in compose_content:
                print(
                    f"❌ Error: Required volume mount '{mount}' not found in {compose_file}"
                )
                return False

        print(f"✅ {compose_file} has correct Redis volume mounts")

    print("\n🎉 Redis configuration validation successful!")
    print("\nNext steps:")
    print("1. Start Redis: docker-compose up redis -d")
    print("2. Test connection: python scripts/test_redis_connection.py")
    print("3. Check logs: docker-compose logs redis")

    return True


if __name__ == "__main__":
    success = validate_redis_config()
    sys.exit(0 if success else 1)
