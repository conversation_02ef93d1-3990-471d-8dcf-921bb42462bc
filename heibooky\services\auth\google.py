import logging

import requests
from allauth.socialaccount.models import SocialAccount, SocialApp
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from apps.users.models import User, UserProfile
from apps.users.serializers import UserSerializer
from django.conf import settings
from django.core.files.base import ContentFile
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token as google_id_token
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from services.email import AccountEmailService

logger = logging.getLogger("heibooky")


class CustomGoogleOAuth2Adapter(GoogleOAuth2Adapter):
    """
    Custom Google OAuth2 adapter that handles the case of multiple social apps.
    """

    def get_app(self, request):
        try:
            app = SocialApp.objects.filter(provider="google").latest("id")
            return app
        except SocialApp.DoesNotExist:
            logger.error("No Google OAuth app configured")
            raise SocialApp.DoesNotExist(
                "No Google OAuth app found. Please configure one in the admin."
            )
        except Exception as e:
            logger.error(f"Error retrieving Google OAuth app: {str(e)}")
            raise


@method_decorator(csrf_exempt, name="dispatch")
class GoogleLoginView(APIView):
    """
    View for handling Google OAuth2 authentication using ID tokens.
    """

    permission_classes = (AllowAny,)

    def _get_client_id(self):
        """Get the Google client ID from the SocialApp model"""
        try:
            app = SocialApp.objects.filter(provider="google").latest("id")
            return app.client_id
        except SocialApp.DoesNotExist:
            logger.error("No Google OAuth app configured")
            raise SocialApp.DoesNotExist(
                "No Google OAuth app found. Please configure one in the admin."
            )

    def _verify_id_token(self, token):
        """Verify the Google ID token and return the user info"""
        try:
            client_id = self._get_client_id()
            # Verify the token
            idinfo = google_id_token.verify_oauth2_token(
                token, google_requests.Request(), client_id
            )

            # ID token is valid, return the user info
            return idinfo
        except ValueError as e:
            logger.error(f"Invalid ID token: {str(e)}")
            raise ValueError(f"Invalid ID token: {str(e)}")

    def _get_or_create_user(self, user_info):
        """Get or create a user based on the Google user info"""
        email = user_info.get("email")
        if not email:
            raise ValueError("Email not provided by Google")

        # Check if user exists
        user = User.objects.filter(email=email).first()

        # If user doesn't exist, create one
        if not user:
            name = user_info.get("name", "")
            user = User.objects.create_user(
                email=email, name=name, is_verified=True  # Google verified the email
            )

        # Ensure social account exists
        social_account = SocialAccount.objects.filter(
            user=user, provider="google"
        ).first()

        if not social_account:
            # Create social account connection
            SocialAccount.objects.create(
                user=user,
                provider="google",
                uid=str(user_info.get("sub")),
                extra_data=user_info,
            )

        return user

    def _update_profile_image(self, user, picture_url):
        """
        Fetch and save the user's Google profile image only if the user doesn't already have one.
        """
        try:
            if picture_url:
                # Get or create the user profile
                profile = UserProfile.objects.get_or_create(user=user)[0]

                # Only update the image if the user doesn't already have one
                if not profile.image or not profile.image.name:
                    logger.info(
                        f"Setting profile image from Google for user {user.email}"
                    )
                    response = requests.get(picture_url)
                    if response.status_code == 200:
                        profile.image.save(
                            f"google_profile_{user.id}.jpg",
                            ContentFile(response.content),
                            save=True,
                        )
                else:
                    logger.info(
                        f"User {user.email} already has a profile image, skipping Google profile image update"
                    )
        except Exception as e:
            logger.error(f"Error updating profile image: {str(e)}")

    def post(self, request):
        """Handle the Google ID token authentication"""
        try:
            # Get the ID token from the request
            id_token = request.data.get("id_token")
            if not id_token:
                return Response(
                    {"error": "No ID token provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Verify the ID token
            try:
                user_info = self._verify_id_token(id_token)
            except ValueError as e:
                return Response({"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED)

            # Get or create the user
            try:
                user = self._get_or_create_user(user_info)
            except ValueError as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

            # Update profile image if needed
            picture_url = user_info.get("picture")
            if picture_url:
                self._update_profile_image(user, picture_url)

            # Send welcome email for new users (if this is their first login)
            try:
                _, created = UserProfile.objects.get_or_create(user=user)
                if created:
                    email_service = AccountEmailService()
                    email_service.send_welcome_email(user)
            except Exception as e:
                logger.error(f"Failed to send welcome email: {str(e)}")

            # Generate tokens
            refresh = RefreshToken.for_user(user)

            # Prepare response data
            response_data = {
                "user": UserSerializer(user).data,
                "tokens": {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                },
            }

            response = Response(response_data)

            # Set cookie headers for tokens if configured
            if getattr(settings, "REST_AUTH", {}).get("JWT_AUTH_COOKIE"):
                cookie_name = settings.REST_AUTH["JWT_AUTH_COOKIE"]
                response.set_cookie(
                    cookie_name,
                    str(refresh.access_token),
                    httponly=True,
                    samesite="Lax",
                    secure=not settings.DEBUG,
                )

            if getattr(settings, "REST_AUTH", {}).get("JWT_AUTH_REFRESH_COOKIE"):
                refresh_cookie_name = settings.REST_AUTH["JWT_AUTH_REFRESH_COOKIE"]
                response.set_cookie(
                    refresh_cookie_name,
                    str(refresh),
                    httponly=True,
                    samesite="Lax",
                    secure=not settings.DEBUG,
                )

            return response

        except Exception as e:
            logger.error(f"Unexpected error in Google authentication: {str(e)}")
            return Response(
                {
                    "error": "Authentication failed",
                    "detail": str(e),
                    "code": "server_error",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
