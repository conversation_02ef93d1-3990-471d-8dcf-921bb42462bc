from apps.integrations.models import (
    DownloadableTemplate,
    Invoice,
    Notification,
    Payout,
    StripeCustomer,
    SUAPIActionLog,
)
from django.contrib import admin


@admin.register(SUAPIActionLog)
class SUAPIActionLogAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "property_id",
        "action",
        "description",
        "status",
        "timestamp",
    )
    list_filter = ("action", "status", "timestamp")
    search_fields = ("user__name", "property_id", "description")
    readonly_fields = (
        "user",
        "property_id",
        "action",
        "description",
        "status",
        "timestamp",
        "details",
    )
    date_hierarchy = "timestamp"
    ordering = ["-timestamp"]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "user",
                    "property_id",
                    "action",
                    "description",
                    "status",
                    "timestamp",
                )
            },
        ),
        (
            "Additional Details",
            {
                "fields": ("details",),
            },
        ),
    )

    def has_add_permission(self, request):
        # Disable adding new logs from the admin interface
        return False


class PayoutInline(admin.TabularInline):
    """
    Inline view for Payouts linked to a Customer.
    """

    model = Payout
    extra = 0  # No extra empty rows
    fields = (
        "stripe_payment_intent_id",
        "amount",
        "currency",
        "status",
        "created_at",
        "updated_at",
    )
    readonly_fields = ("created_at", "updated_at")
    can_delete = False
    show_change_link = True


class InvoiceInline(admin.TabularInline):
    """
    Inline view for Invoices linked to a Payout.
    """

    model = Invoice
    extra = 0  # No extra empty rows
    fields = ("pdf_file", "created_at")
    readonly_fields = (
        "pdf_file",
        "created_at",
        "digithera_reference",
        "sdi_status",
        "progressive_number",
    )
    can_delete = False
    show_change_link = True


@admin.register(StripeCustomer)
class StripeCustomerAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Stripe Customer model.
    """

    list_display = ("user", "stripe_customer_id", "created_at")
    search_fields = ("user__email", "stripe_customer_id")
    list_filter = ("created_at",)
    readonly_fields = ("created_at",)
    inlines = [PayoutInline]
    ordering = ("-created_at",)  # Sort by most recently created


@admin.register(Payout)
class PaymentAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Payout model.
    """

    list_display = (
        "customer",
        "stripe_payment_intent_id",
        "amount",
        "currency",
        "status",
        "created_at",
        "updated_at",
    )
    search_fields = ("stripe_payment_intent_id", "customer__user__email")
    list_filter = ("status", "currency", "created_at")
    readonly_fields = ("created_at", "updated_at")
    inlines = [InvoiceInline]
    ordering = ("-created_at",)  # Sort by most recently created


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Invoice model.
    """

    list_display = ("payout", "pdf_file", "created_at")
    search_fields = ("payout__stripe_payment_intent_id", "progressive_number")
    list_filter = ("created_at",)
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)  # Sort by most recently created


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Notification model.
    """

    list_display = ("user", "title", "is_read", "created_at")
    search_fields = ("user__name", "title", "message")
    list_filter = ("is_read", "created_at")
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)


class DownloadableTemplateAdmin(admin.ModelAdmin):
    list_display = ("title", "category", "created_at")
    search_fields = ("title", "description")
    list_filter = ("category", "created_at")
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)


admin.site.register(DownloadableTemplate, DownloadableTemplateAdmin)
