"""
URL configuration for the Heibooky project.
"""

from apps.monitoring.views import HealthCheckView
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from django.views.generic import TemplateView
from services.auth import GoogleLoginView

urlpatterns = [
    # Root-level health check for easy external monitoring
    path("health/", HealthCheckView.as_view(), name="root-health-check"),
    path("admin/", admin.site.urls),
    path("accounts/", include("allauth.urls")),
    path("auth/", include("dj_rest_auth.urls")),
    path("auth/google/", GoogleLoginView.as_view(), name="google_login"),
    path("auth/registration/", include("dj_rest_auth.registration.urls")),
    path("billing/", include("apps.billing.urls")),
    path("booking/", include("apps.booking.urls")),
    path("support/", include("apps.support.urls")),
    path("integrations/", include("apps.integrations.urls")),
    path("monitoring/", include("apps.monitoring.urls")),
    path("office/", include("apps.office.urls")),
    path("pricing/", include("apps.pricing.urls")),
    path("reviews/", include("apps.reviews.urls")),
    path("stay/", include("apps.stay.urls")),
    path("test/", TemplateView.as_view(template_name="test.html")),
    path("users/", include("apps.users.urls")),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
