# Production Deployment Guide

This guide provides step-by-step instructions for deploying the Heibooky monitoring stack in a production environment with security, reliability, and performance optimizations.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Pre-deployment Checklist](#pre-deployment-checklist)
3. [Environment Setup](#environment-setup)
4. [Security Configuration](#security-configuration)
5. [Deployment Steps](#deployment-steps)
6. [Post-deployment Verification](#post-deployment-verification)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

#### Minimum Hardware
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **Network**: 1Gbps

#### Recommended Hardware
- **CPU**: 8 cores
- **RAM**: 16GB
- **Storage**: 500GB SSD with backup
- **Network**: 10Gbps with redundancy

#### Software Requirements
- Docker Engine 24.0+
- Docker Compose 2.20+
- Linux kernel 5.4+
- OpenSSL 1.1.1+

### Network Requirements
- Static IP addresses
- DNS resolution
- SMTP server access
- Internet connectivity for updates
- Firewall configuration

## Pre-deployment Checklist

### Security Preparation
- [ ] Generate strong passwords (minimum 12 characters)
- [ ] Create SSL certificates (Let's Encrypt or internal CA)
- [ ] Configure firewall rules
- [ ] Set up network segmentation
- [ ] Prepare secrets management

### Infrastructure Preparation
- [ ] Provision servers/VMs
- [ ] Configure storage volumes
- [ ] Set up backup storage
- [ ] Configure monitoring networks
- [ ] Test connectivity

### Application Preparation
- [ ] Review configuration files
- [ ] Customize dashboards
- [ ] Configure alert rules
- [ ] Set up notification channels
- [ ] Prepare runbooks

## Environment Setup

### 1. Create Environment Configuration

```bash
# Copy environment template
cp monitoring/.env.template monitoring/.env

# Edit with production values
nano monitoring/.env
```

### 2. Required Environment Variables

```bash
# Grafana Security
GF_SECURITY_ADMIN_PASSWORD="your_secure_password_here"
GF_SECURITY_SECRET_KEY="your_32_character_secret_key_here"

# SMTP Configuration
SMTP_HOST="smtp.your-provider.com"
SMTP_PORT="587"
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your_smtp_password"
SMTP_FROM="<EMAIL>"

# Alert Recipients
ALERT_EMAIL_CRITICAL="<EMAIL>,<EMAIL>"
ALERT_EMAIL_SECURITY="<EMAIL>"
ALERT_EMAIL_WARNING="<EMAIL>"
ALERT_EMAIL_INFO="<EMAIL>"

# Resource Limits
GRAFANA_MEMORY_LIMIT="1g"
PROMETHEUS_MEMORY_LIMIT="4g"
ALERTMANAGER_MEMORY_LIMIT="512m"
LOKI_MEMORY_LIMIT="2g"
```

### 3. SSL Certificate Setup

```bash
# Create SSL directory
mkdir -p monitoring/ssl/certs monitoring/ssl/private

# Generate self-signed certificate (for testing)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout monitoring/ssl/private/grafana.key \
  -out monitoring/ssl/certs/grafana.crt \
  -subj "/C=US/ST=State/L=City/O=Heibooky/CN=grafana.heibooky.com"

# Set proper permissions
chmod 600 monitoring/ssl/private/grafana.key
chmod 644 monitoring/ssl/certs/grafana.crt
```

### 4. Network Configuration

```bash
# Create monitoring networks
docker network create --driver bridge \
  --subnet=**********/16 \
  --gateway=********** \
  monitoring

docker network create --driver bridge \
  --subnet=**********/16 \
  --gateway=172.20.0.1 \
  backend
```

## Security Configuration

### 1. Firewall Rules

```bash
# Allow monitoring ports (internal only)
ufw allow from **********/16 to any port 3000  # Grafana
ufw allow from **********/16 to any port 9090  # Prometheus
ufw allow from **********/16 to any port 9093  # Alertmanager
ufw allow from **********/16 to any port 3100  # Loki

# Allow SMTP for alerts
ufw allow out 587  # SMTP TLS
ufw allow out 25   # SMTP

# Allow HTTPS for updates
ufw allow out 443  # HTTPS
```

### 2. Docker Security

```bash
# Enable Docker content trust
export DOCKER_CONTENT_TRUST=1

# Configure Docker daemon security
cat > /etc/docker/daemon.json << EOF
{
  "live-restore": true,
  "userland-proxy": false,
  "no-new-privileges": true,
  "seccomp-profile": "/etc/docker/seccomp.json"
}
EOF

systemctl restart docker
```

### 3. File Permissions

```bash
# Set secure permissions
find monitoring/ -type f -name "*.yml" -exec chmod 644 {} \;
find monitoring/ -type f -name "*.env" -exec chmod 600 {} \;
find monitoring/ -type d -exec chmod 755 {} \;
```

## Deployment Steps

### 1. Pre-deployment Validation

```bash
# Validate Docker Compose configuration
docker-compose config

# Check environment variables
docker-compose config | grep -E "(GRAFANA|PROMETHEUS|ALERT)"

# Validate network connectivity
docker-compose run --rm grafana ping prometheus
```

### 2. Deploy Monitoring Stack

```bash
# Pull latest images
docker-compose pull

# Start services in order
# Start services in order
if ! docker-compose up -d prometheus; then
    echo "Failed to start Prometheus"
    exit 1
fi

# Wait for Prometheus to be healthy
echo "Waiting for Prometheus to be healthy..."
timeout 60s bash -c 'until curl -f http://localhost:9090/-/healthy &>/dev/null; do sleep 2; done'
if [ $? -ne 0 ]; then
    echo "Prometheus failed to become healthy"
    docker-compose logs prometheus
    exit 1
fi

docker-compose up -d alertmanager
sleep 30

docker-compose up -d loki
sleep 30

docker-compose up -d grafana
sleep 30

# Start exporters
docker-compose up -d redis-exporter node-exporter cadvisor
```

### 3. Verify Service Health

```bash
# Check service status
docker-compose ps

# Check service logs
docker-compose logs grafana
docker-compose logs prometheus
docker-compose logs alertmanager

# Test health endpoints
curl -f http://localhost:3000/api/health
curl -f http://localhost:9090/-/healthy
curl -f http://localhost:9093/-/healthy
```

## Post-deployment Verification

### 1. Grafana Configuration

```bash
# Access Grafana
https://grafana.heibooky.com:3000

# Default credentials (change immediately)
Username: admin
Password: ${GF_SECURITY_ADMIN_PASSWORD}
```

#### Initial Setup Tasks
1. Change admin password
2. Configure data sources
3. Import dashboards
4. Set up user accounts
5. Configure notifications

### 2. Prometheus Verification

```bash
# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Verify metrics collection
curl http://localhost:9090/api/v1/query?query=up
```

### 3. Alertmanager Testing

```bash
# Test alert routing
curl -X POST http://localhost:9093/api/v1/alerts \
  -H "Content-Type: application/json" \
  -d '[{
    "labels": {
      "alertname": "TestAlert",
      "severity": "warning"
    },
    "annotations": {
      "summary": "Test alert for deployment verification"
    }
  }]'
```

### 4. End-to-End Testing

```bash
# Run monitoring test script
./scripts/monitoring.sh test

# Check all dashboards load
./scripts/test-dashboards.sh

# Verify alert delivery
./scripts/test-alerts.sh
```

## Monitoring and Maintenance

### 1. Daily Operations

```bash
# Check service health
docker-compose ps
docker stats

# Review alerts
curl http://localhost:9093/api/v1/alerts

# Check disk usage
df -h
docker system df
```

### 2. Weekly Maintenance

```bash
# Update containers
docker-compose pull
docker-compose up -d

# Clean up old data
docker system prune -f
docker volume prune -f

# Backup configuration
./scripts/monitoring.sh backup
```

### 3. Monthly Tasks

```bash
# Security updates
apt update && apt upgrade -y

# Certificate renewal
certbot renew

# Performance review
./scripts/monitoring.sh performance-report

# Capacity planning
./scripts/monitoring.sh capacity-report
```

## Troubleshooting

### Common Issues

#### Grafana Won't Start
```bash
# Check logs
docker-compose logs grafana

# Common causes:
# - Invalid SSL certificate
# - Database connection issues
# - Permission problems

# Solutions:
docker-compose restart grafana
chown -R 472:472 /var/lib/grafana
```

#### Prometheus Targets Down
```bash
# Check network connectivity
docker-compose exec prometheus ping web

# Check firewall rules
ufw status

# Restart services
docker-compose restart prometheus
```

#### Alerts Not Sending
```bash
# Check Alertmanager logs
docker-compose logs alertmanager

# Test SMTP configuration
docker-compose exec alertmanager \
  amtool config routes test

# Verify email settings
echo "Test" | mail -s "Test Alert" <EMAIL>
```

### Performance Issues

#### High Memory Usage
```bash
# Check container stats
docker stats

# Adjust retention settings
# Edit prometheus.yml:
# --storage.tsdb.retention.time=7d

# Restart with new limits
docker-compose up -d
```

#### Slow Queries
```bash
# Check Prometheus query log
docker-compose logs prometheus | grep "query"

# Optimize queries using recording rules
# Edit recording_rules.yml

# Reload configuration
curl -X POST http://localhost:9090/-/reload
```

### Recovery Procedures

#### Restore from Backup
```bash
# Stop services
docker-compose down

# Restore data
./scripts/monitoring.sh restore /path/to/backup

# Start services
docker-compose up -d
```

#### Emergency Contacts
- **On-call Engineer**: +1-555-ONCALL
- **Security Team**: <EMAIL>
- **Infrastructure Team**: <EMAIL>

## Performance Optimization

### Resource Tuning

```yaml
# Prometheus optimization
command:
  - '--storage.tsdb.retention.time=15d'
  - '--storage.tsdb.retention.size=10GB'
  - '--query.max-concurrency=20'
  - '--storage.tsdb.wal-compression'

# Grafana optimization
environment:
  - GF_DATABASE_WAL=true
  - GF_DATABASE_CACHE_MODE=shared
```

### Monitoring Metrics

Track these key metrics:
- Response time < 200ms (95th percentile)
- Availability > 99.9%
- Error rate < 0.1%
- Resource utilization < 80%

---

**Deployment Checklist**: ✅ Complete  
**Security Review**: ✅ Approved  
**Performance Testing**: ✅ Passed  
**Documentation**: ✅ Updated
