"""
Django management command to diagnose WebSocket connection issues
Usage: python manage.py diagnose_websocket [--token TOKEN] [--chat-id CHAT_ID]
"""

import asyncio
import json

import websockets
from apps.support.models import Chat
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.tokens import AccessToken

import redis


class Command(BaseCommand):
    help = "Diagnose WebSocket connection issues for support chat"

    def add_arguments(self, parser):
        parser.add_argument(
            "--token", type=str, help="JWT token to test authentication"
        )
        parser.add_argument("--chat-id", type=str, help="Chat ID to test connection")
        parser.add_argument(
            "--host",
            type=str,
            default="localhost",
            help="WebSocket host (default: localhost)",
        )
        parser.add_argument(
            "--port", type=int, default=8000, help="WebSocket port (default: 8000)"
        )
        parser.add_argument("--ssl", action="store_true", help="Use WSS instead of WS")

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🔍 WebSocket Diagnostic Tool"))
        self.stdout.write("=" * 50)

        # Run all diagnostic checks
        self.check_django_configuration()
        self.check_redis_connection()
        self.check_database_connectivity()

        if options["token"]:
            self.check_jwt_token(options["token"])

        if options["chat_id"]:
            self.check_chat_access(options["chat_id"], options["token"])

        if options["token"] and options["chat_id"]:
            asyncio.run(
                self.test_websocket_connection(
                    options["token"],
                    options["chat_id"],
                    options["host"],
                    options["port"],
                    options["ssl"],
                )
            )

        self.stdout.write("\n" + "=" * 50)
        self.stdout.write(self.style.SUCCESS("✅ Diagnostic complete"))

    def check_django_configuration(self):
        """Check Django Channels configuration"""
        self.stdout.write("\n📋 Checking Django Configuration...")

        # Check ASGI application
        asgi_app = getattr(settings, "ASGI_APPLICATION", None)
        if asgi_app:
            self.stdout.write(f"  ✅ ASGI_APPLICATION: {asgi_app}")
        else:
            self.stdout.write(self.style.ERROR("  ❌ ASGI_APPLICATION not configured"))

        # Check Channels in INSTALLED_APPS
        if "channels" in settings.INSTALLED_APPS:
            self.stdout.write("  ✅ Channels installed")
        else:
            self.stdout.write(self.style.ERROR("  ❌ Channels not in INSTALLED_APPS"))

        # Check Channel Layers
        channel_layers = getattr(settings, "CHANNEL_LAYERS", {})
        if channel_layers:
            default_layer = channel_layers.get("default", {})
            backend = default_layer.get("BACKEND", "Not configured")
            self.stdout.write(f"  ✅ Channel Layer Backend: {backend}")

            if "InMemory" in backend:
                self.stdout.write(
                    self.style.WARNING("  ⚠️  Using InMemory layer (development only)")
                )
            elif "Redis" in backend:
                self.stdout.write("  ✅ Using Redis layer (production ready)")
        else:
            self.stdout.write(self.style.ERROR("  ❌ CHANNEL_LAYERS not configured"))

        # Check CORS settings
        cors_origins = getattr(settings, "CORS_ALLOWED_ORIGINS", [])
        if cors_origins:
            self.stdout.write(
                f"  ✅ CORS Origins configured: {len(cors_origins)} origins"
            )
            for origin in cors_origins[:3]:  # Show first 3
                self.stdout.write(f"    - {origin}")
            if len(cors_origins) > 3:
                self.stdout.write(f"    ... and {len(cors_origins) - 3} more")
        else:
            self.stdout.write(self.style.WARNING("  ⚠️  No CORS origins configured"))

    def check_redis_connection(self):
        """Check Redis connectivity"""
        self.stdout.write("\n🔴 Checking Redis Connection...")

        try:
            redis_url = getattr(settings, "REDIS_URL", None)
            if not redis_url:
                self.stdout.write(self.style.ERROR("  ❌ REDIS_URL not configured"))
                return

            self.stdout.write(f"  📍 Redis URL: {redis_url}")

            # Test Redis connection
            r = redis.from_url(redis_url)
            response = r.ping()

            if response:
                self.stdout.write("  ✅ Redis connection successful")

                # Test basic operations
                r.set("websocket_test", "test_value", ex=10)
                value = r.get("websocket_test")
                if value == b"test_value":
                    self.stdout.write("  ✅ Redis read/write operations working")
                    r.delete("websocket_test")
                else:
                    self.stdout.write(
                        self.style.WARNING("  ⚠️  Redis read/write test failed")
                    )

            else:
                self.stdout.write(self.style.ERROR("  ❌ Redis ping failed"))

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Redis connection error: {str(e)}")
            )

    def check_database_connectivity(self):
        """Check database connectivity for support models"""
        self.stdout.write("\n🗄️  Checking Database Connectivity...")

        try:
            # Test Chat model
            chat_count = Chat.objects.count()
            self.stdout.write(
                f"  ✅ Chat model accessible: {chat_count} chats in database"
            )

            # Test User model
            User = get_user_model()
            user_count = User.objects.count()
            self.stdout.write(
                f"  ✅ User model accessible: {user_count} users in database"
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Database connectivity error: {str(e)}")
            )

    def check_jwt_token(self, token):
        """Validate JWT token"""
        self.stdout.write("\n🔐 Checking JWT Token...")

        try:
            access_token = AccessToken(token)
            user_id = access_token.get("user_id")

            if user_id:
                self.stdout.write(f"  ✅ Token valid, user_id: {user_id}")

                # Check if user exists
                User = get_user_model()
                try:
                    user = User.objects.get(id=user_id)
                    self.stdout.write(f"  ✅ User found: {user.email}")
                    self.stdout.write(f"  📊 User is_staff: {user.is_staff}")
                    self.stdout.write(f"  📊 User is_active: {user.is_active}")
                except User.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f"  ❌ User with ID {user_id} not found")
                    )
            else:
                self.stdout.write(
                    self.style.ERROR("  ❌ Token does not contain user_id")
                )

        except TokenError as e:
            self.stdout.write(self.style.ERROR(f"  ❌ Invalid token: {str(e)}"))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Token validation error: {str(e)}")
            )

    def check_chat_access(self, chat_id, token=None):
        """Check chat access permissions"""
        self.stdout.write("\n💬 Checking Chat Access...")

        try:
            # Check if chat exists
            chat = Chat.objects.filter(id=chat_id).first()
            if not chat:
                self.stdout.write(
                    self.style.ERROR(f"  ❌ Chat with ID {chat_id} not found")
                )
                return

            self.stdout.write(f"  ✅ Chat found: {chat.id}")
            self.stdout.write(f"  📊 Chat status: {chat.status}")
            self.stdout.write(f"  📊 Chat priority: {chat.priority}")
            self.stdout.write(f"  📊 Chat user: {chat.user.email}")

            # Check permissions if token provided
            if token:
                try:
                    access_token = AccessToken(token)
                    user_id = access_token.get("user_id")

                    User = get_user_model()
                    user = User.objects.get(id=user_id)

                    if user.is_staff:
                        self.stdout.write(
                            "  ✅ Staff user - access granted to all chats"
                        )
                    elif str(user.id) == str(chat.user.id):
                        self.stdout.write("  ✅ User owns this chat - access granted")
                    else:
                        self.stdout.write(
                            self.style.ERROR(
                                "  ❌ User does not own this chat and is not staff"
                            )
                        )

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"  ❌ Permission check error: {str(e)}")
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Chat access check error: {str(e)}")
            )

    async def test_websocket_connection(self, token, chat_id, host, port, use_ssl):
        """Test actual WebSocket connection"""
        self.stdout.write("\n🌐 Testing WebSocket Connection...")

        protocol = "wss" if use_ssl else "ws"
        url = f"{protocol}://{host}:{port}/ws/support/{chat_id}/?token={token}"

        self.stdout.write(f"  📍 Connecting to: {url}")

        try:
            async with websockets.connect(url, timeout=10) as websocket:
                self.stdout.write("  ✅ WebSocket connection established")

                # Test ping
                ping_message = {"type": "ping"}
                await websocket.send(json.dumps(ping_message))
                self.stdout.write("  📤 Sent ping message")

                # Wait for pong
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    response_data = json.loads(response)

                    if response_data.get("type") == "pong":
                        self.stdout.write("  ✅ Received pong response")
                    else:
                        self.stdout.write(f"  📥 Received: {response_data}")

                except asyncio.TimeoutError:
                    self.stdout.write(
                        self.style.WARNING("  ⚠️  No response to ping (timeout)")
                    )

                # Test chat message
                chat_message = {
                    "type": "chat.message",
                    "message": "Test message from diagnostic tool",
                }
                await websocket.send(json.dumps(chat_message))
                self.stdout.write("  📤 Sent test chat message")

                # Wait for any responses
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=2)
                        response_data = json.loads(response)
                        self.stdout.write(
                            f'  📥 Received: {response_data.get("type", "unknown")}'
                        )
                except asyncio.TimeoutError:
                    self.stdout.write("  ⏰ No more responses (timeout)")

        except websockets.exceptions.ConnectionClosed as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Connection closed: {e.code} - {e.reason}")
            )
            self.decode_close_code(e.code)
        except websockets.exceptions.InvalidStatusCode as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Invalid status code: {e.status_code}")
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"  ❌ Connection error: {str(e)}"))

    def decode_close_code(self, code):
        """Decode WebSocket close codes"""
        close_codes = {
            1008: "Policy Violation (likely CORS issue)",
            4001: "No authentication token provided",
            4002: "Invalid authentication token",
            4003: "Token authentication error",
            4004: "No chat_id provided in URL",
            4005: "Chat not found",
            4006: "Access denied to chat",
            4007: "Error retrieving chat details",
        }

        if code in close_codes:
            self.stdout.write(f"  📋 Close code meaning: {close_codes[code]}")
        else:
            self.stdout.write(f"  📋 Unknown close code: {code}")
