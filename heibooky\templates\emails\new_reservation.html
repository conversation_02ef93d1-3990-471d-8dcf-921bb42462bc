{% extends "emails/base_email.html" %}

{% block title %}
    {{ translations.new_reservation_notification }}
{% endblock %}

{% block content %}
    <tr>
        <td class="content">
            <div class="main-content">
                <h2 class="reservation-title">{{ translations.new_reservation_received }}</h2>
                <p class="reservation-subtitle">{{ translations.reservation_review_prompt }}</p>

                <div class="reservation-section">
                    <div class="section-header">
                        <span class="section-icon">🏨</span>
                        <h3>{{ translations.reservation_info }}</h3>
                    </div>
                    <table class="details-table">
                        <tr>
                            <td class="detail-label">{{ translations.reservation_id }}</td>
                            <td class="detail-value highlight">{{ reservation.id }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">{{ translations.checkin_date }}</td>
                            <td class="detail-value">{{ reservation.checkin_date|date:"d/m/Y" }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">{{ translations.checkout_date }}</td>
                            <td class="detail-value">{{ reservation.checkout_date|date:"d/m/Y" }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">{{ translations.guests }}</td>
                            <td class="detail-value">
                                <span class="guest-count">{{ reservation.number_of_adults }}</span> {{ translations.adults }}<br>
                                <span class="guest-count">{{ reservation.number_of_children }}</span> {{ translations.children }}<br>
                                <span class="guest-count">{{ reservation.number_of_infants }}</span> {{ translations.infants }}
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="guest-section">
                    <div class="section-header">
                        <span class="section-icon">👤</span>
                        <h3>{{ translations.guest_info }}</h3>
                    </div>
                    <table class="details-table">
                        <tr>
                            <td class="detail-label">{{ translations.guest_name }}</td>
                            <td class="detail-value">{{ customer.first_name }} {{ customer.last_name }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">{{ translations.email }}</td>
                            <td class="detail-value">{{ customer.email }}</td>
                        </tr>
                        <tr>
                            <td class="detail-label">{{ translations.phone }}</td>
                            <td class="detail-value">{{ customer.telephone }}</td>
                        </tr>
                    </table>
                </div>

                <p class="action-prompt">{{ translations.action_prompt }}</p>
            </div>
        </td>
    </tr>
{% endblock %}

{% block style %}
{{ block.super }}
<style>
    .main-content {
        padding: 20px;
    }

    .reservation-title {
        color: #113158;
        font-size: 28px;
        margin-bottom: 10px;
        text-align: center;
    }

    .reservation-subtitle {
        color: #666;
        margin-bottom: 30px;
        text-align: center;
    }

    .reservation-section,
    .price-section,
    .guest-section {
        background: #ffffff;
        border-radius: 8px;
        margin-bottom: 25px;
        box-shadow: 0 2px 4px rgba(17, 49, 88, 0.1);
        overflow: hidden;
    }

    .section-header {
        background: #113158;
        color: white;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .section-icon {
        font-size: 20px;
    }

    .details-table,
    .price-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        padding: 15px;
    }

    .detail-label,
    .price-label {
        color: #666;
        font-weight: 600;
        padding: 12px 20px;
        width: 40%;
    }

    .detail-value,
    .price-value {
        color: #113158;
        padding: 12px 20px;
    }

    .highlight {
        color: #FCB51F;
        font-weight: bold;
    }

    .price-section {
        background: #113158;
    }

    .price-table {
        background: white;
        margin: 15px;
        border-radius: 6px;
    }

    .guest-count {
        color: #FCB51F;
        font-weight: bold;
        font-size: 18px;
    }

    .action-prompt {
        background: #f8f9fa;
        border-left: 4px solid #FCB51F;
        padding: 15px;
        margin: 20px 0;
        color: #113158;
    }

    @media only screen and (max-width: 600px) {
        .detail-label,
        .price-label {
            width: 50%;
        }
        
        .reservation-title {
            font-size: 24px;
        }
    }
</style>
{% endblock %}