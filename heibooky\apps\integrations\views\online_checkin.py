import logging

from apps.integrations.models import PropertyOnlineCheckIn
from apps.integrations.serializers import (
    OnlineCheckInConfigSerializer,
    OnlineCheckInStatusSerializer,
    TestConnectionRequestSerializer,
    TestConnectionResponseSerializer,
)
from apps.stay.models import Property
from apps.users.permissions import VerifiedOwnerPermission
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from services.online_checkin import OnlineCheckInService

logger = logging.getLogger(__name__)


class OnlineCheckInStatusView(APIView):
    """
    API view to check the status of online check-in for a property.
    """

    permission_classes = [IsAuthenticated, VerifiedOwnerPermission]

    def get(self, request, property_id):
        """
        Get the online check-in status for a property.

        Args:
            request: HTTP request
            property_id: UUID of the property

        Returns:
            Response: Online check-in status
        """
        try:
            # Get the property and verify ownership
            property_instance = get_object_or_404(Property, id=property_id)

            # Check if the user has permission to access this property
            self.check_object_permissions(request, property_instance)

            # Get or create the online check-in configuration
            config, created = PropertyOnlineCheckIn.objects.get_or_create(
                property=property_instance,
                defaults={
                    "is_enabled": False,
                    "istat_enabled": False,
                    "alloggati_enabled": False,
                },
            )

            # If online check-in is not enabled, return basic status
            if not config.is_enabled:
                return Response(
                    {
                        "is_enabled": False,
                        "istat_connected": False,
                        "alloggati_connected": False,
                        "last_sync": config.last_sync,
                        "next_sync": config.next_sync,
                    }
                )

            # Get connection status from service
            connection_status = OnlineCheckInService.get_connection_status(property_id)

            # Prepare response data
            response_data = {
                "is_enabled": config.is_enabled,
                "istat_connected": (
                    connection_status["istat_connected"]
                    if config.istat_enabled
                    else False
                ),
                "alloggati_connected": (
                    connection_status["alloggati_connected"]
                    if config.alloggati_enabled
                    else False
                ),
                "last_sync": config.last_sync,
                "next_sync": config.next_sync,
            }

            # Serialize and return the response
            serializer = OnlineCheckInStatusSerializer(response_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(
                f"Error getting online check-in status for property {property_id}: {str(e)}"
            )
            return Response(
                {
                    "error": _(
                        "An error occurred while retrieving online check-in status."
                    )
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class OnlineCheckInConfigView(APIView):
    """
    API view to manage online check-in configuration for a property.
    """

    permission_classes = [IsAuthenticated, VerifiedOwnerPermission]

    def get(self, request, property_id):
        """
        Get the online check-in configuration for a property.

        Args:
            request: HTTP request
            property_id: UUID of the property

        Returns:
            Response: Online check-in configuration
        """
        try:
            # Get the property and verify ownership
            property_instance = get_object_or_404(Property, id=property_id)

            # Check if the user has permission to access this property
            self.check_object_permissions(request, property_instance)

            # Get or create the online check-in configuration
            config, created = PropertyOnlineCheckIn.objects.get_or_create(
                property=property_instance,
                defaults={
                    "is_enabled": False,
                    "istat_enabled": False,
                    "alloggati_enabled": False,
                },
            )

            # Serialize and return the configuration
            serializer = OnlineCheckInConfigSerializer(config)
            return Response(serializer.data)

        except Exception as e:
            logger.error(
                f"Error getting online check-in configuration for property {property_id}: {str(e)}"
            )
            return Response(
                {
                    "error": _(
                        "An error occurred while retrieving online check-in configuration."
                    )
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def post(self, request, property_id):
        """
        Save the online check-in configuration for a property.

        Args:
            request: HTTP request
            property_id: UUID of the property

        Returns:
            Response: Updated online check-in configuration
        """
        try:
            # Get the property and verify ownership
            property_instance = get_object_or_404(Property, id=property_id)

            # Check if the user has permission to access this property
            self.check_object_permissions(request, property_instance)

            # Get or create the online check-in configuration
            config, created = PropertyOnlineCheckIn.objects.get_or_create(
                property=property_instance,
                defaults={
                    "is_enabled": False,
                    "istat_enabled": False,
                    "alloggati_enabled": False,
                },
            )

            # Validate and update the configuration
            serializer = OnlineCheckInConfigSerializer(config, data=request.data)
            if serializer.is_valid():
                # Check if online check-in is being enabled
                if not config.is_enabled and serializer.validated_data.get(
                    "is_enabled", False
                ):
                    # Set next sync time when enabling
                    config.next_sync = OnlineCheckInService.calculate_next_sync_time()

                # Save the configuration
                serializer.save()

                # Log the configuration change
                logger.info(
                    f"Online check-in configuration updated for property {property_id} by user {request.user.id}"
                )

                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(
                f"Error saving online check-in configuration for property {property_id}: {str(e)}"
            )
            return Response(
                {
                    "error": _(
                        "An error occurred while saving online check-in configuration."
                    )
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class TestConnectionView(APIView):
    """
    API view to test connection with authorities.
    """

    permission_classes = [IsAuthenticated, VerifiedOwnerPermission]

    def post(self, request, property_id):
        """
        Test connection with a specific authority.

        Args:
            request: HTTP request
            property_id: UUID of the property

        Returns:
            Response: Test connection result
        """
        try:
            # Validate request data
            request_serializer = TestConnectionRequestSerializer(data=request.data)
            if not request_serializer.is_valid():
                return Response(
                    request_serializer.errors, status=status.HTTP_400_BAD_REQUEST
                )

            # Get the property and verify ownership
            property_instance = get_object_or_404(Property, id=property_id)

            # Check if the user has permission to access this property
            self.check_object_permissions(request, property_instance)

            # Get the connection type from the request
            connection_type = request_serializer.validated_data["connection_type"]

            # Get or create the online check-in configuration
            config, created = PropertyOnlineCheckIn.objects.get_or_create(
                property=property_instance,
                defaults={
                    "is_enabled": False,
                    "istat_enabled": False,
                    "alloggati_enabled": False,
                },
            )

            # Check if the requested connection type is enabled
            if connection_type == "istat" and not config.istat_enabled:
                return Response(
                    {"error": _("ISTAT reporting is not enabled for this property.")},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            elif connection_type == "alloggati" and not config.alloggati_enabled:
                return Response(
                    {
                        "error": _(
                            "Alloggati Web reporting is not enabled for this property."
                        )
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Test the connection
            test_result = OnlineCheckInService.test_connection(
                property_id, connection_type
            )

            # Log the test result
            if test_result["success"]:
                logger.info(
                    f"Successful connection test for property {property_id} with {connection_type.upper()}"
                )
            else:
                logger.warning(
                    f"Failed connection test for property {property_id} with {connection_type.upper()}: {test_result['error']}"
                )

            # Serialize and return the result
            response_serializer = TestConnectionResponseSerializer(test_result)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(
                f"Error testing connection for property {property_id}: {str(e)}"
            )
            return Response(
                {"error": _("An error occurred while testing the connection.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
