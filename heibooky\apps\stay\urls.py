from apps.stay.views import *
from django.urls import include, path
from rest_framework.routers import DefaultRouter

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r"locations", LocationViewSet, basename="location")
router.register(r"property", PropertyViewSet, basename="property")
router.register(r"team", TeamViewSet, basename="team")
router.register(r"photos", PhotoViewSet, basename="photos")
router.register(r"guest-info", GuestArrivalInfoViewSet, basename="guest-info")
router.register(
    r"property-metadata", PropertyMetadataViewSet, basename="property-metadata"
)
router.register(r"rooms", RoomViewSet, basename="rooms")
router.register(r"room-amenities", RoomAmenityViewSet, basename="room-amenities")

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path("", include(router.urls)),
    path("amenity/", PropertyAmenityAPIView.as_view(), name="amenities"),
    path(
        "settings-status/",
        PropertySettingsStatusView.as_view(),
        name="property-settings-status",
    ),
    path(
        "team/verify_invite/", VerifyTeamInviteView.as_view(), name="verify-team-invite"
    ),
]
