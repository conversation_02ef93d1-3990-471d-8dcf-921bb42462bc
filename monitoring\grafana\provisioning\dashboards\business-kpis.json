{"dashboard": {"id": null, "title": "Business KPIs - Heibooky", "tags": ["he<PERSON><PERSON>y", "business", "kpi", "metrics", "production"], "style": "dark", "timezone": "browser", "time": {"from": "now-24h", "to": "now"}, "refresh": "5m", "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "User Activity Overview", "type": "stat", "targets": [{"expr": "sum(increase(django_http_requests_total{handler=~\".*login.*\"}[24h]))", "legendFormat": "Daily Logins", "refId": "A"}, {"expr": "sum(increase(django_http_requests_total{handler=~\".*register.*\"}[24h]))", "legendFormat": "Daily Registrations", "refId": "B"}, {"expr": "count(count by (user_id) (django_http_requests_total{user_id!=\"\"}))", "legendFormat": "Active Users", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 10}, {"color": "green", "value": 100}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Booking Activity", "type": "timeseries", "targets": [{"expr": "sum(rate(django_http_requests_total{handler=~\".*booking.*create.*\"}[5m])) * 3600", "legendFormat": "Bookings Created/hour", "refId": "A"}, {"expr": "sum(rate(django_http_requests_total{handler=~\".*booking.*update.*\"}[5m])) * 3600", "legendFormat": "Bookings Modified/hour", "refId": "B"}, {"expr": "sum(rate(django_http_requests_total{handler=~\".*booking.*cancel.*\"}[5m])) * 3600", "legendFormat": "Bookings Cancelled/hour", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "short", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 3, "title": "Revenue Metrics", "type": "timeseries", "targets": [{"expr": "sum(rate(django_http_requests_total{handler=~\".*payment.*success.*\"}[5m])) * 3600", "legendFormat": "Successful Payments/hour", "refId": "A"}, {"expr": "sum(rate(django_http_requests_total{handler=~\".*payment.*failed.*\"}[5m])) * 3600", "legendFormat": "Failed Payments/hour", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "short", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 4, "title": "API Usage by Endpoint", "type": "table", "targets": [{"expr": "topk(20, sum by (handler) (rate(django_http_requests_total[1h])))", "legendFormat": "", "refId": "A", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 10}]}, "custom": {"align": "auto", "displayMode": "color-background"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Error Rate by Business Function", "type": "table", "targets": [{"expr": "(\n  sum by (handler) (rate(django_http_requests_total{status=~\"5..\"}[1h])) /\n  sum by (handler) (rate(django_http_requests_total[1h]))\n) * 100", "legendFormat": "", "refId": "A", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "custom": {"align": "auto", "displayMode": "color-background"}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "User Session Duration", "type": "histogram", "targets": [{"expr": "histogram_quantile(0.50, sum(rate(django_session_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(django_session_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum(rate(django_session_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P99", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "s", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 7, "title": "<PERSON><PERSON>", "type": "timeseries", "targets": [{"expr": "rate(django_cache_hits_total[5m]) / (rate(django_cache_hits_total[5m]) + rate(django_cache_misses_total[5m])) * 100", "legendFormat": "Cache Hit Rate %", "refId": "A"}, {"expr": "rate(django_cache_operations_total[5m])", "legendFormat": "Cache Operations/sec", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Cache Hit Rate %"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "min", "value": 0}, {"id": "max", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Cache Operations/sec"}, "properties": [{"id": "unit", "value": "ops"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 8, "title": "Database Query Performance", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(django_db_query_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P95 Query Time", "refId": "A"}, {"expr": "rate(django_db_queries_total[5m])", "legendFormat": "Queries/sec", "refId": "B"}, {"expr": "rate(django_db_slow_queries_total[5m])", "legendFormat": "Slow Queries/sec", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "P95 Query Time"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Queries/sec"}, "properties": [{"id": "unit", "value": "qps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Slow Queries/sec"}, "properties": [{"id": "unit", "value": "qps"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}], "templating": {"list": [{"name": "time_range", "type": "custom", "options": [{"text": "Last Hour", "value": "1h"}, {"text": "Last 6 Hours", "value": "6h"}, {"text": "Last 24 Hours", "value": "24h"}, {"text": "Last 7 Days", "value": "7d"}, {"text": "Last 30 Days", "value": "30d"}], "current": {"text": "Last 24 Hours", "value": "24h"}}, {"name": "business_function", "type": "query", "datasource": "Prometheus", "query": "label_values(django_http_requests_total, handler)", "refresh": 1, "includeAll": true, "allValue": ".*", "current": {"text": "All", "value": "$__all"}}]}, "annotations": {"list": [{"name": "Business Events", "datasource": "Prometheus", "enable": true, "expr": "increase(django_business_events_total[1m])", "iconColor": "green", "titleFormat": "Business Event: {{event_type}}", "textFormat": "{{description}}"}]}}, "overwrite": true}