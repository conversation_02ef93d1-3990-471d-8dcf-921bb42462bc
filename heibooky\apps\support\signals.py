import logging

from django.db import transaction
from django.db.models.signals import post_save
from django.dispatch import receiver

from .models import SupportMessage
from .tasks import send_admin_to_user_notification, send_user_to_admin_notification

logger = logging.getLogger(__name__)


@receiver(post_save, sender=SupportMessage)
def send_support_notification(sender, instance, created, **kwargs):
    """
    Signal handler to send email notifications when a new support message is created.
    Runs asynchronously after the transaction is committed.
    """
    if not created:
        return

    def send_notification():
        try:
            if instance.is_from_support:
                # Admin to user message - send notification after 5 minutes if unread
                send_admin_to_user_notification.apply_async(
                    args=[str(instance.id)], countdown=300  # 5 minutes in seconds
                )
                logger.info(
                    f"Scheduled admin-to-user notification for message {instance.id} in 5 minutes"
                )
            else:
                # User to admin message - send notification immediately
                send_user_to_admin_notification.delay(str(instance.id))
                logger.info(
                    f"Sent immediate user-to-admin notification for message {instance.id}"
                )

        except Exception as e:
            logger.error(
                f"Failed to schedule support notification for message {instance.id}: {str(e)}"
            )

    # Execute after the current transaction
    transaction.on_commit(send_notification)
