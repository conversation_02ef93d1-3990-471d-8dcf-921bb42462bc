import logging
import uuid
from io import BytesIO

from django.core.files.base import ContentFile
from PIL import Image

logger = logging.getLogger(__name__)


def compress_image(image_file, quality=85, max_size=1200, output_format="JPEG"):
    """
    Compress an image file to reduce its size while maintaining good quality.

    Args:
        image_file: Django ImageField or file-like object
        quality: JPEG compression quality (1-100, default 85)
        max_size: Maximum size in pixels for the longest dimension
        output_format: Output format (JPEG, PNG, WEBP)

    Returns:
        ContentFile with the compressed image
    """
    try:
        # Open the image using PIL
        img = Image.open(image_file)

        # Convert to RGB if needed (to avoid issues with RGBA mode)
        if img.mode in ("RGBA", "LA", "P"):
            img = img.convert("RGB")

        # Resize if the image is larger than max_size
        if max(img.size) > max_size:
            # Calculate the resize ratio
            ratio = max_size / max(img.size)
            new_size = (int(img.size[0] * ratio), int(img.size[1] * ratio))
            img = img.resize(new_size, Image.LANCZOS)

        # Save to BytesIO buffer
        output = BytesIO()

        # Determine format
        if output_format.upper() == "JPEG":
            img.save(output, format="JPEG", optimize=True, quality=quality)
        elif output_format.upper() == "PNG":
            img.save(output, format="PNG", optimize=True)
        elif output_format.upper() == "WEBP":
            img.save(output, format="WEBP", quality=quality)
        else:
            # Default to JPEG
            img.save(output, format="JPEG", optimize=True, quality=quality)

        # Get the content
        output.seek(0)

        # Generate a unique filename
        filename = f"{uuid.uuid4().hex}.{output_format.lower()}"

        return ContentFile(output.read(), name=filename)

    except Exception as e:
        logger.error(f"Error compressing image: {str(e)}")
        return None


def find_best_format(image_file):
    """
    Determine the best format for an image based on its content.

    Args:
        image_file: Django ImageField or file-like object

    Returns:
        Tuple of (format, params) for the best format
    """
    try:
        img = Image.open(image_file)

        # Check if image has transparency
        has_transparency = img.mode in ("RGBA", "LA") or (
            img.mode == "P" and "transparency" in img.info
        )

        if has_transparency:
            # PNG is better for images with transparency
            return "PNG", {"optimize": True}
        else:
            # JPEG is generally smaller for photos without transparency
            return "JPEG", {"quality": 85, "optimize": True}

    except Exception as e:
        logger.error(f"Error determining best format: {str(e)}")
        return "JPEG", {"quality": 85, "optimize": True}  # Default to JPEG


def generate_cover_image(image_file, max_size=800):
    """
    Generate a compressed version of an image suitable for use as a cover image.

    Args:
        image_file: Django ImageField or file-like object
        max_size: Maximum size in pixels for the longest dimension

    Returns:
        ContentFile with the compressed image
    """
    try:
        # Determine best format
        format_name, format_params = find_best_format(image_file)

        # Compress with determined format
        return compress_image(
            image_file,
            quality=format_params.get("quality", 85),
            max_size=max_size,
            output_format=format_name,
        )

    except Exception as e:
        logger.error(f"Error generating cover image: {str(e)}")
        return None
