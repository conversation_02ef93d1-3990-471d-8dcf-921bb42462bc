# Recording Rules for Performance Optimization
# These rules pre-compute frequently used queries to improve dashboard performance

groups:
  # High-frequency rules for critical application metrics
  - name: django_high_frequency_rules
    interval: 15s
    rules:
      # HTTP Request Rate (requests per second) - Optimized aggregation
      - record: django:http_requests_per_second
        expr: |
          sum(rate(django_http_requests_total[5m])) by (job, instance, method)
        labels:
          component: "application"
          metric_type: "throughput"

      # HTTP Request Rate by Status Code - Optimized for dashboard queries
      - record: django:http_requests_per_second_by_status
        expr: |
          sum(rate(django_http_requests_total[5m])) by (job, instance, status)
        labels:
          component: "application"
          metric_type: "throughput"

      # HTTP Error Rate (percentage) - Pre-computed for alerts
      - record: django:http_error_rate
        expr: |
          (
            sum(rate(django_http_requests_total{status=~"4..|5.."}[5m])) by (job, instance) /
            clamp_min(sum(rate(django_http_requests_total[5m])) by (job, instance), 1)
          ) * 100
        labels:
          component: "application"
          metric_type: "error_rate"

      # HTTP 95th Percentile Latency
      - record: django:http_request_duration_95th_percentile
        expr: |
          histogram_quantile(
            0.95,
            sum(rate(django_http_request_duration_seconds_bucket[5m])) by (le)
          )
        labels:
          job: django-app

      # HTTP 99th Percentile Latency
      - record: django:http_request_duration_99th_percentile
        expr: histogram_quantile(0.99, rate(django_http_request_duration_seconds_bucket[5m]))
        labels:
          job: django-app

      # Average Request Duration
      - record: django:http_request_duration_average
        expr: |
          rate(django_http_request_duration_seconds_sum[5m]) /
          rate(django_http_request_duration_seconds_count[5m])
        labels:
          job: django-app

      # Active Requests
      - record: django:http_requests_active
        expr: django_http_requests_active
        labels:
          job: django-app

  - name: system_recording_rules
    interval: 30s
    rules:
      # CPU Usage Percentage
      - record: node:cpu_usage_percent
        expr: |
          100 - (
            avg by (instance) (
              rate(node_cpu_seconds_total{mode="idle"}[5m])
            ) * 100
          )

      # Memory Usage Percentage
      - record: node:memory_usage_percent
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
            node_memory_MemTotal_bytes
          ) * 100

      # Disk Usage Percentage
      - record: node:disk_usage_percent
        expr: |
          (
            (node_filesystem_size_bytes - node_filesystem_free_bytes) /
            node_filesystem_size_bytes
          ) * 100

      # Network I/O Rate
      - record: node:network_io_bytes_per_second
        expr: |
          rate(node_network_receive_bytes_total[5m]) +
          rate(node_network_transmit_bytes_total[5m])

      # Disk I/O Rate
      - record: node:disk_io_bytes_per_second
        expr: |
          rate(node_disk_read_bytes_total[5m]) +
          rate(node_disk_written_bytes_total[5m])

  - name: redis_recording_rules
    interval: 30s
    rules:
      # Redis Memory Usage Percentage
      - record: redis:memory_usage_percent
        expr: |
          (redis_memory_used_bytes / clamp_min(redis_config_maxmemory_bytes, 1)) * 100
      # Redis Connection Rate
      - record: redis:connections_per_second
        expr: rate(redis_connected_clients[5m])

      # Redis Command Rate
      - record: redis:commands_per_second
        expr: rate(redis_commands_processed_total[5m])

      # Redis Hit Rate
      - record: redis:hit_rate_percent
        expr: |
          (
            rate(redis_keyspace_hits_total[5m]) /
            (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m]))
          ) * 100

  - name: celery_recording_rules
    interval: 30s
    rules:
      # Celery Task Rate
      - record: celery:tasks_per_second
        expr: rate(celery_tasks_total[5m])

      # Celery Task Failure Rate
      - record: celery:task_failure_rate_percent
        expr: |
          (
          (
            rate(celery_tasks_total{state="FAILURE"}[5m]) /
            clamp_min(rate(celery_tasks_total[5m]), 1)
          ) * 100
      - record: celery:queue_length
        expr: celery_queue_length

      # Celery Worker Count
      - record: celery:workers_active
        expr: celery_workers_total

  - name: application_health_recording_rules
    interval: 60s
    rules:
      # Application Availability
      - record: app:availability_percent
        expr: |
          (
            avg_over_time(up{job="django-app"}[5m])
          ) * 100

      # Database Availability
      - record: app:database_availability_percent
        expr: |
          (
            avg_over_time(up{job="postgres"}[5m])
          ) * 100

      # Cache Availability
      - record: app:cache_availability_percent
        expr: |
          (
            avg_over_time(up{job="redis"}[5m])
          ) * 100

      # Overall System Health Score
      - record: app:health_score
        expr: |
          (
            app:availability_percent +
            app:database_availability_percent +
            app:cache_availability_percent
          ) / 3

  - name: security_recording_rules
    interval: 60s
    rules:
      # Failed Login Attempts Rate
      - record: security:failed_login_rate
        expr: rate(django_http_requests_total{status="401"}[5m])

      # Suspicious Request Rate (4xx errors)
      - record: security:suspicious_request_rate
        expr: rate(django_http_requests_total{status=~"4.."}[5m])

      # High Error Rate Indicator
      - record: security:high_error_rate_indicator
        expr: |
          (
            rate(django_http_requests_total{status=~"5.."}[5m]) /
            rate(django_http_requests_total[5m])
          ) > 0.05

  - name: performance_sli_recording_rules
    interval: 30s
    rules:
      # Availability SLI (99.9% target)
      - record: sli:availability
        expr: |
          (
            rate(django_http_requests_total{status!~"5.."}[5m]) /
            rate(django_http_requests_total[5m])
          )

      # Latency SLI (95% of requests under 500ms)
      - record: sli:latency
        expr: |
          (
            histogram_quantile(0.95, rate(django_http_request_duration_seconds_bucket[5m])) < 0.5
          )

      # Error Rate SLI (less than 1% error rate)
      - record: sli:error_rate
        expr: |
          (
            rate(django_http_requests_total{status=~"5.."}[5m]) /
            rate(django_http_requests_total[5m])
          ) < 0.01

      # Throughput SLI (requests per second)
      - record: sli:throughput
        expr: rate(django_http_requests_total[5m])

  # Performance optimization rules - Medium frequency
  - name: performance_optimization_rules
    interval: 30s
    rules:
      # Pre-computed dashboard queries for better performance
      - record: dashboard:cpu_usage_by_instance
        expr: |
          100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
        labels:
          component: "system"
          metric_type: "utilization"

      - record: dashboard:memory_usage_by_instance
        expr: |
          ((node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes) * 100
        labels:
          component: "system"
          metric_type: "utilization"

      - record: dashboard:disk_usage_by_mountpoint
        expr: |
          ((node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes) * 100
        labels:
          component: "system"
          metric_type: "utilization"

      # Application performance aggregations
      - record: dashboard:request_rate_by_endpoint
        expr: |
          sum(rate(django_http_requests_total[5m])) by (method, handler)
        labels:
          component: "application"
          metric_type: "throughput"

      - record: dashboard:error_rate_by_endpoint
        expr: |
          sum(rate(django_http_requests_total{status=~"4..|5.."}[5m])) by (method, handler)
        labels:
          component: "application"
          metric_type: "error_rate"

      - record: dashboard:latency_by_endpoint_p95
        expr: |
          histogram_quantile(0.95, sum(rate(django_http_request_duration_seconds_bucket[5m])) by (method, handler, le))
        labels:
          component: "application"
          metric_type: "latency"
          quantile: "0.95"

      # Database performance metrics
      - record: dashboard:db_connections_active
        expr: |
          sum(pg_stat_activity_count) by (instance, datname)
        labels:
          component: "database"
          metric_type: "connections"

      - record: dashboard:db_query_duration_p95
        expr: |
          histogram_quantile(0.95, sum(rate(pg_stat_statements_mean_time_seconds_bucket[5m])) by (instance, le))
        labels:
          component: "database"
          metric_type: "latency"
          quantile: "0.95"

      # Cache performance metrics
      - record: dashboard:redis_hit_ratio
        expr: |
          (rate(redis_keyspace_hits_total[5m]) / (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m]))) * 100
        labels:
          component: "cache"
          metric_type: "hit_ratio"

      - record: dashboard:redis_memory_efficiency
        expr: |
          (redis_memory_used_bytes / redis_config_maxmemory_bytes) * 100
        labels:
          component: "cache"
          metric_type: "utilization"

  # Capacity planning rules - Low frequency
  - name: capacity_planning_rules
    interval: 60s
    rules:
      # Resource trend analysis
      - record: capacity:cpu_trend_1h
        expr: |
          avg_over_time(node:cpu_usage_percent[1h])
        labels:
          component: "system"
          metric_type: "trend"
          timeframe: "1h"

      - record: capacity:memory_trend_1h
        expr: |
          avg_over_time(node:memory_usage_percent[1h])
        labels:
          component: "system"
          metric_type: "trend"
          timeframe: "1h"

      - record: capacity:disk_growth_rate
        expr: |
          (predict_linear(node_filesystem_free_bytes[6h], 3600) * -1) / 1024 / 1024 / 1024
        labels:
          component: "system"
          metric_type: "growth_rate"

      # Application capacity metrics
      - record: capacity:request_volume_trend_1h
        expr: |
          avg_over_time(django:http_requests_per_second[1h])
        labels:
          component: "application"
          metric_type: "trend"
          timeframe: "1h"

      - record: capacity:error_rate_trend_1h
        expr: |
          avg_over_time(django:http_error_rate[1h])
        labels:
          component: "application"
          metric_type: "trend"
          timeframe: "1h"

      # Predictive metrics for scaling decisions
      - record: capacity:cpu_saturation_risk
        expr: |
          (capacity:cpu_trend_1h > 80) and (delta(capacity:cpu_trend_1h[6h]) > 10)
        labels:
          component: "system"
          metric_type: "risk_indicator"

      - record: capacity:memory_saturation_risk
        expr: |
          (capacity:memory_trend_1h > 85) and (increase(capacity:memory_trend_1h[6h]) > 5)
        labels:
          component: "system"
          metric_type: "risk_indicator"
