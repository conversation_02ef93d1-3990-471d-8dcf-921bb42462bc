import time

from django.conf import settings
from django.core.cache import cache
from django.core.management.base import BaseCommand
from django.utils import timezone

import redis


class Command(BaseCommand):
    help = "Test Redis connection and performance (experimental)"

    def add_arguments(self, parser):
        parser.add_argument(
            "--detailed",
            action="store_true",
            help="Run detailed Redis tests including performance benchmarks",
        )
        parser.add_argument(
            "--monitor",
            action="store_true",
            help="Continuously monitor Redis connection",
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                "Testing Redis connection and performance (experimental)..."
            )
        )

        try:
            # Test basic cache operations
            self.test_basic_cache_operations()

            # Test Redis connection directly
            self.test_direct_redis_connection()

            # Test authentication
            self.test_redis_authentication()

            if options["detailed"]:
                self.run_performance_tests()

            if options["monitor"]:
                self.monitor_redis_connection()

        except (
            redis.ConnectionError,
            redis.AuthenticationError,
            redis.TimeoutError,
        ) as e:
            self.stdout.write(self.style.ERROR(f"Redis test failed: {str(e)}"))
            raise SystemExit(1)
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Unexpected error during Redis test: {str(e)}")
            )
            raise SystemExit(1)
        else:
            self.stdout.write(
                self.style.SUCCESS("All Redis tests passed successfully!")
            )

    def test_basic_cache_operations(self):
        """Test basic Django cache operations"""
        self.stdout.write("Testing basic cache operations...")

        # Test set/get
        test_key = "redis_test_key"
        test_value = "redis_test_value"

        cache.set(test_key, test_value, timeout=30)
        retrieved_value = cache.get(test_key)

        if retrieved_value != test_value:
            raise Exception(
                f"Cache test failed: expected {test_value}, got {retrieved_value}"
            )

        # Test delete
        cache.delete(test_key)
        if cache.get(test_key) is not None:
            raise Exception("Cache delete test failed")

        # Test complex data
        complex_data = {
            "timestamp": timezone.now().isoformat(),
            "data": [1, 2, 3, 4, 5],
            "nested": {"key": "value"},
        }

        cache.set("complex_test", complex_data, timeout=30)
        retrieved_complex = cache.get("complex_test")

        if retrieved_complex != complex_data:
            raise Exception("Complex data cache test failed")

        cache.delete("complex_test")

        self.stdout.write(self.style.SUCCESS("Basic cache operations passed"))

    def test_direct_redis_connection(self):
        """Test direct Redis connection"""
        self.stdout.write("Testing direct Redis connection...")

        try:
            # Get Redis URL from settings
            redis_url = getattr(settings, "REDIS_URL", "redis://localhost:6379/0")

            # Create Redis client
            r = redis.from_url(redis_url)

            # Test ping
            pong = r.ping()
            if not pong:
                raise Exception("Redis ping failed")

            # Test info
            info = r.info()
            self.stdout.write(f'Redis version: {info.get("redis_version", "unknown")}')
            self.stdout.write(
                f'Connected clients: {info.get("connected_clients", "unknown")}'
            )
            self.stdout.write(
                f'Used memory: {info.get("used_memory_human", "unknown")}'
            )

            self.stdout.write(self.style.SUCCESS("Direct Redis connection passed"))

        except Exception as e:
            raise Exception(f"Direct Redis connection failed: {str(e)}")

    def test_redis_authentication(self):
        """Test Redis authentication"""
        self.stdout.write("Testing Redis authentication...")

        try:
            redis_url = getattr(settings, "REDIS_URL", "redis://localhost:6379/0")
            r = redis.from_url(redis_url)

            # Try to execute a command that requires authentication
            r.set("auth_test", "test_value", ex=10)
            value = r.get("auth_test")

            if value != b"test_value":
                raise Exception("Authentication test failed")

            r.delete("auth_test")

            self.stdout.write(self.style.SUCCESS("Redis authentication passed"))

        except Exception as e:
            raise Exception(f"Redis authentication failed: {str(e)}")

    def run_performance_tests(self):
        """Run Redis performance benchmarks"""
        self.stdout.write("Running performance tests...")

        # Test write performance
        start_time = time.time()
        for i in range(1000):
            cache.set(f"perf_test_{i}", f"value_{i}", timeout=60)
        write_time = time.time() - start_time

        # Test read performance
        start_time = time.time()
        for i in range(1000):
            cache.get(f"perf_test_{i}")
        read_time = time.time() - start_time

        # Clean up
        try:
            for i in range(1000):
                cache.delete(f"perf_test_{i}")
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Cleanup warning: {str(e)}"))

        self.stdout.write(f"Write performance: {1000/write_time:.2f} ops/sec")
        self.stdout.write(f"Read performance: {1000/read_time:.2f} ops/sec")

        self.stdout.write(self.style.SUCCESS("Performance tests completed"))

    def monitor_redis_connection(self):
        """Continuously monitor Redis connection"""
        self.stdout.write("Starting Redis connection monitor (Press Ctrl+C to stop)...")

        try:
            redis_url = getattr(settings, "REDIS_URL", "redis://localhost:6379/0")
            r = redis.from_url(redis_url)

            while True:
                try:
                    start_time = time.time()
                    r.ping()
                    ping_time = (time.time() - start_time) * 1000

                    info = r.info()

                    self.stdout.write(
                        f'[{timezone.now().strftime("%H:%M:%S")}] '
                        f"Ping: {ping_time:.2f}ms, "
                        f'Clients: {info.get("connected_clients", 0)}, '
                        f'Memory: {info.get("used_memory_human", "unknown")}'
                    )

                    time.sleep(5)

                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Monitor error: {str(e)}"))
                    time.sleep(5)

        except KeyboardInterrupt:
            self.stdout.write("\nMonitoring stopped.")
