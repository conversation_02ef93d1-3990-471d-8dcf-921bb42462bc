services:
  web:
    build:
      context: .
      target: production
    image: davysongs/heibooky:${IMAGE_TAG:-latest}
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    command: ["doppler", "run", "--", "docker-entrypoint.sh", "--service", "web"]
    volumes:
      - static_volume:/app/heibooky/staticfiles
      - media_volume:/app/heibooky/media
      - log_volume:/var/log/heibooky
    expose:
      - 8000
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - DOPPLER_TOKEN=${DOPPLER_TOKEN_PRD}
      - DOPPLER_PROJECT=heibooky-backend
      - DOPPLER_CONFIG=prd
      - DJANGO_SETTINGS_MODULE=config.settings.production
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  celery-worker:
    build:
      context: .
      target: production
    image: davysongs/heibooky:${IMAGE_TAG:-latest}
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    command: ["doppler", "run", "--", "docker-entrypoint.sh", "--service", "celery-worker", "--skip-migrations", "--skip-static"]
    volumes:
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    user: "1000:1000"
    depends_on:
      redis:
        condition: service_healthy
      web:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - DOPPLER_TOKEN=${DOPPLER_TOKEN_PRD}
      - DOPPLER_PROJECT=heibooky-backend
      - DOPPLER_CONFIG=prd
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery-beat:
    build:
      context: .
      target: production
    image: davysongs/heibooky:${IMAGE_TAG:-latest}
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    command: ["doppler", "run", "--", "docker-entrypoint.sh", "--service", "celery-beat", "--skip-migrations", "--skip-static"]
    volumes:
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    user: "1000:1000"
    depends_on:
      redis:
        condition: service_healthy
      celery-worker:
        condition: service_started
      web:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - DOPPLER_TOKEN=${DOPPLER_TOKEN_PRD}
      - DOPPLER_PROJECT=heibooky-backend
      - DOPPLER_CONFIG=prd
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    container_name: heibooky-redis
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    user: "999:999"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
      - ./redis/users.acl:/etc/redis/users.acl:ro
    sysctls:
      - net.core.somaxconn=511
    command: redis-server /etc/redis/redis.conf
    environment:
      - DOPPLER_TOKEN=${DOPPLER_TOKEN_PRD}
      - DOPPLER_PROJECT=heibooky-backend
      - DOPPLER_CONFIG=prd
    mem_limit: 512m
    cpus: 0.5
    networks:
      - backend
      - monitoring
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:1.25-alpine
    container_name: heibooky-nginx
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/logs:/var/log/nginx
      - static_volume:/app/heibooky/staticfiles:ro
      - media_volume:/app/heibooky/media:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - ./certbot/www:/var/www/certbot:ro
      - nginx_cache:/var/cache/nginx
      - nginx_run:/var/run
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      web:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  certbot:
    image: certbot/certbot:latest
    container_name: heibooky-certbot
    restart: "no"
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
      - ./certbot/logs:/var/log/letsencrypt
    environment:
      - CERTBOT_EMAIL=${CERTBOT_EMAIL:-<EMAIL>}
      - CERTBOT_DOMAIN=${CERTBOT_DOMAIN:-backend.heibooky.com}
    networks:
      - backend
    profiles:
      - ssl

  # Optional monitoring services (only if monitoring variables are set)
  grafana:
    image: grafana/grafana:latest
    container_name: heibooky-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-admin}
      - GF_SECURITY_SECRET_KEY=${GF_SECURITY_SECRET_KEY:-defaultsecret}
      - GF_SMTP_ENABLED=${SMTP_ENABLED:-false}
      - GF_SMTP_HOST=${SMTP_HOST:-}
      - GF_SMTP_PORT=${SMTP_PORT:-587}
      - GF_SMTP_USER=${SMTP_USERNAME:-}
      - GF_SMTP_PASSWORD=${SMTP_PASSWORD:-}
      - GF_SMTP_FROM_ADDRESS=${SMTP_FROM:-}
    volumes:
      - grafana_volume:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - backend
    profiles:
      - monitoring

  prometheus:
    image: prom/prometheus:latest
    container_name: heibooky-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_volume:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - backend
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local
  log_volume:
    driver: local
  nginx_cache:
    driver: local
  nginx_run:
    driver: local
  grafana_volume:
  prometheus_volume:

networks:
  backend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
