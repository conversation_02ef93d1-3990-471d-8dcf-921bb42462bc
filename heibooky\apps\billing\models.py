import uuid

from django.core.files.storage import storages
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_countries.fields import CountryField


class BillingProfile(models.Model):
    NATURAL_PERSON = "NP"
    COMPANY = "CO"

    RECIPIENT_TYPE_CHOICES = [
        (NATURAL_PERSON, _("Natural Person")),
        (COMPANY, _("Company")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    date_of_birth = models.DateField()
    nationality = CountryField()
    gender = models.Char<PERSON><PERSON>(max_length=10)
    recipient_type = models.CharField(
        max_length=2, choices=RECIPIENT_TYPE_CHOICES, default=NATURAL_PERSON
    )
    company_name = models.CharField(max_length=255, blank=True, null=True)
    iban = models.Char<PERSON><PERSON>(max_length=34)
    id_document = models.FileField(
        upload_to="uploads/id_documents/",
        blank=True,
        null=True,
        storage=storages["document_storage"],
    )
    company_document = models.FileField(
        upload_to="uploads/company_documents/",
        blank=True,
        null=True,
        storage=storages["document_storage"],
    )
    owner = models.OneToOneField("users.User", on_delete=models.CASCADE)

    def get_full_name(self):
        if self.recipient_type == self.COMPANY:
            return f"{self.company_name}"
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        return f"{self.get_full_name()}"


class BillingAddress(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    billing_profile = models.OneToOneField(
        BillingProfile, on_delete=models.CASCADE, related_name="billing_address"
    )
    street_number = models.CharField(max_length=255)
    postcode = models.CharField(max_length=10)
    city = models.CharField(max_length=100)
    country = CountryField()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Billing Address for {self.billing_profile}"

    class Meta:
        verbose_name_plural = "Billing addresses"


class Taxation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    billing_profile = models.OneToOneField(
        BillingProfile, on_delete=models.CASCADE, related_name="taxation"
    )
    has_vat_number = models.BooleanField(default=False)
    vat_number = models.CharField(max_length=20, blank=True, null=True)
    tin_number = models.CharField(max_length=20, blank=True, null=True)
    tin_country = CountryField(blank=True, null=True)
    rent_more_than_4_properties = models.BooleanField(default=False)

    def __str__(self):
        return f"Taxation for {self.billing_profile}"
