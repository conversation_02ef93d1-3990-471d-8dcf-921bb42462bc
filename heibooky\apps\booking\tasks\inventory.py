import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List

from apps.booking.models import Booking, BookingBlock
from apps.pricing.models import RoomRate
from apps.stay.models import Property, Room
from celery import shared_task
from django.db import transaction
from django.utils import timezone
from services.su_api import update_inventory

logger = logging.getLogger(__name__)


def format_date_range(start_date, end_date) -> List[Dict[str, str]]:
    """
    Format date range for SU API inventory control.

    Args:
        start_date: Start date of the range
        end_date: End date of the range

    Returns:
        List containing a dictionary with from/to date values
    """
    return [
        {"from": start_date.strftime("%Y-%m-%d"), "to": end_date.strftime("%Y-%m-%d")}
    ]


def get_effective_start_date(start_date):
    """
    Returns the later of start_date and today.
    """
    today = timezone.now().date()
    return max(start_date, today)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def set_room_inventory_for_year(self, room_id: str) -> Dict[str, Any]:
    """
    Task to set room inventory for a one-year period after room onboarding.

    Args:
        room_id: ID of the Room that was just onboarded

    Returns:
        Dictionary with task result information
    """
    try:
        # Retrieve the room
        room = Room.objects.select_related("property").get(id=room_id)

        if not room.property.is_onboarded or not room.is_onboarded:
            logger.warning(
                f"Room {room_id} or its property is not onboarded. Skipping inventory update."
            )
            return {"status": "warning", "message": "Room or property not onboarded"}

        # Calculate one year period from today
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=366)

        # Format date range
        date_range = format_date_range(start_date, end_date)

        # Prepare inventory data with room quantity
        room_id_str = str(room.id)[:8]  # Get the first 8 characters for SU API
        rate_plan_id = (
            room.property.rate_plans.filter(is_onboarded=True).first().id
            if room.property.rate_plans.filter(is_onboarded=True).exists()
            else None
        )
        if not rate_plan_id:
            logger.error(
                f"No onboarded rate plan found for property {room.property.id}"
            )
            return {"status": "failed", "message": "No onboarded rate plan found"}
        inventory_data = [
            {
                **date,
                "rate": [{"rateplanid": rate_plan_id}],
                "roomstosell": str(room.quantity),
                "price": [
                    {
                        "NumberOfGuests": str(i),
                        "value": str(room.room_rate),
                    }
                    for i in range(1, room.max_occupancy + 1)
                ],
            }
            for date in date_range
        ]

        logger.info(
            f"Setting room {room_id_str} inventory for one year period: {start_date} to {end_date}"
        )

        try:
            response = update_inventory(
                property_instance=room.property,
                room_id=room_id_str,
                inventory_data=inventory_data,
            )

            # Check if the response indicates success
            is_successful = (
                response.get("Success") == "Success"
                or response.get("Status") == "Success"
            )

            if is_successful:
                logger.info(
                    f"Successfully set one-year inventory for room {room_id_str}: {response}"
                )
                return {
                    "status": "success",
                    "room_id": room_id_str,
                    "date_range": f"{start_date} to {end_date}",
                    "quantity": str(room.quantity),
                    "response": response,
                }
            else:
                logger.error(
                    f"Failed to set one-year inventory for room {room_id_str}: {response}"
                )
                return {"status": "failed", "room_id": room_id_str, "error": response}

        except Exception as e:
            logger.error(
                f"Error setting one-year inventory for room {room_id_str}: {str(e)}",
                exc_info=True,
            )
            self.retry(exc=e)
            return {"status": "failed", "message": str(e)}

    except Room.DoesNotExist:
        logger.error(f"Room with ID {room_id} not found")
        return {"status": "failed", "message": "Room not found"}
    except Exception as e:
        logger.error(
            f"Error in set_room_inventory_for_year for room {room_id}: {str(e)}",
            exc_info=True,
        )
        self.retry(exc=e)
        return {"status": "failed", "message": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def sync_inventory_for_empty_dates(
    self, property_id: str = None, days_ahead: int = 366
) -> Dict[str, Any]:
    """
    Task to synchronize inventory for dates that don't have bookings, room rates, or booking blocks.
    This ensures that the SU API has correct inventory data for all available dates.

    Args:
        property_id: Optional specific property ID to process. If None, processes all onboarded properties.
        days_ahead: Number of days ahead to check and sync (default: 366 days)

    Returns:
        Dictionary with task result information
    """
    try:
        # Determine which properties to process
        if property_id:
            properties = Property.objects.filter(id=property_id, is_onboarded=True)
            if not properties.exists():
                logger.warning(f"Property {property_id} not found or not onboarded")
                return {
                    "status": "warning",
                    "message": "Property not found or not onboarded",
                }
        else:
            properties = Property.objects.filter(is_onboarded=True)

        if not properties.exists():
            logger.warning("No onboarded properties found")
            return {"status": "warning", "message": "No onboarded properties found"}

        # Calculate date range (today to days_ahead from now)
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=days_ahead)

        total_properties = properties.count()
        processed_properties = 0
        total_rooms_processed = 0
        total_dates_synced = 0
        failed_properties = []

        logger.info(
            f"Starting inventory sync for {total_properties} properties from {start_date} to {end_date}"
        )

        for property_instance in properties:
            try:
                # Get all active rooms for this property
                rooms = Room.objects.filter(property=property_instance, is_active=True)

                if not rooms.exists():
                    logger.warning(
                        f"No active rooms found for property {property_instance.id}"
                    )
                    continue

                property_rooms_processed = 0
                property_dates_synced = 0

                # Process each room to find and sync empty dates
                for room in rooms:
                    try:
                        # Find dates that need inventory synchronization
                        dates_to_sync = _find_empty_dates(room, start_date, end_date)

                        if not dates_to_sync:
                            logger.debug(f"No empty dates found for room {room.id}")
                            continue

                        # Group consecutive dates into ranges
                        date_ranges = _group_consecutive_dates(dates_to_sync)

                        # Prepare inventory data with room's base quantity
                        room_id = str(room.id)[:8]  # First 8 characters for SU API
                        rate_plan_id = (
                            room.property.rate_plans.filter(is_onboarded=True)
                            .first()
                            .id
                            if room.property.rate_plans.filter(
                                is_onboarded=True
                            ).exists()
                            else None
                        )
                        if not rate_plan_id:
                            logger.error(
                                f"No onboarded rate plan found for property {room.property.id}"
                            )
                            continue
                        logger.info(
                            f"Rate plan ID for property {room.property.id}: {rate_plan_id}"
                        )

                        # Convert date ranges to the format expected by SU API
                        inventory_data = [
                            {
                                **date_range,
                                "rate": [{"rateplanid": rate_plan_id}],
                                "roomstosell": str(room.quantity),
                                "price": [
                                    {
                                        "NumberOfGuests": str(i),
                                        "value": str(room.room_rate),
                                    }
                                    for i in range(1, room.max_occupancy + 1)
                                ],
                            }
                            for date_range in date_ranges
                        ]

                        # Send inventory update to SU API
                        response = update_inventory(
                            property_instance=property_instance,
                            room_id=room_id,
                            inventory_data=inventory_data,
                        )

                        # Check if the response indicates success
                        is_successful = (
                            response.get("Success") == "Success"
                            or response.get("Status") == "Success"
                        )

                        if is_successful:
                            property_rooms_processed += 1
                            property_dates_synced += len(dates_to_sync)
                            logger.info(
                                f"Successfully synced {len(dates_to_sync)} dates for room {room_id}: {response}"
                            )
                        else:
                            logger.error(
                                f"Failed to sync inventory for room {room_id}: {response}"
                            )

                    except Exception as e:
                        logger.error(
                            f"Error processing room {room.id}: {str(e)}", exc_info=True
                        )
                        continue

                if property_rooms_processed > 0:
                    processed_properties += 1
                    total_rooms_processed += property_rooms_processed
                    total_dates_synced += property_dates_synced
                    logger.info(
                        f"Property {property_instance.id}: processed {property_rooms_processed} rooms, synced {property_dates_synced} dates"
                    )

            except Exception as e:
                logger.error(
                    f"Error processing property {property_instance.id}: {str(e)}",
                    exc_info=True,
                )
                failed_properties.append(
                    {"property_id": str(property_instance.id), "error": str(e)}
                )
                continue

        # Prepare result summary
        result = {
            "status": "success" if processed_properties > 0 else "warning",
            "processed_properties": processed_properties,
            "total_properties": total_properties,
            "total_rooms_processed": total_rooms_processed,
            "total_dates_synced": total_dates_synced,
            "failed_properties": failed_properties,
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
        }

        if failed_properties:
            result["status"] = "partial_failure"
            logger.warning(
                f"Task completed with {len(failed_properties)} failed properties"
            )

        logger.info(f"Inventory sync completed: {result}")
        return result

    except Exception as e:
        logger.error(
            f"Error in sync_inventory_for_empty_dates: {str(e)}", exc_info=True
        )
        self.retry(exc=e)
        return {"status": "failed", "message": str(e)}


def _find_empty_dates(room, start_date, end_date) -> List[datetime.date]:
    """
    Find dates between start_date and end_date that don't have:
    - Active bookings
    - Room rates
    - Booking blocks

    Args:
        room: Room instance to check
        start_date: Start date for the range
        end_date: End date for the range

    Returns:
        List of dates that need inventory synchronization
    """
    try:
        dates_to_check = []
        current_date = start_date

        # Generate all dates in the range
        while current_date <= end_date:
            dates_to_check.append(current_date)
            current_date += timedelta(days=1)

        # Get dates that have bookings for this room's property
        booking_dates = set()
        bookings = Booking.objects.filter(
            property=room.property,
            checkin_date__lte=end_date,
            checkout_date__gt=start_date,
            status__in=[Booking.Status.NEW, Booking.Status.MODIFIED],
        ).exclude(
            is_manual=True
        )  # Exclude manual bookings

        for booking in bookings:
            booking_start = max(booking.checkin_date.date(), start_date)
            booking_end = min(booking.checkout_date.date(), end_date)
            current = booking_start
            while current < booking_end:  # Checkout date is exclusive
                booking_dates.add(current)
                current += timedelta(days=1)

        # Get dates that have room rates for this room
        rate_dates = set()
        room_rates = RoomRate.objects.filter(
            room=room, start_date__lte=end_date, end_date__gt=start_date, is_active=True
        )

        for rate in room_rates:
            rate_start = max(rate.start_date, start_date)
            rate_end = min(rate.end_date, end_date)
            current = rate_start
            while current <= rate_end:
                rate_dates.add(current)
                current += timedelta(days=1)

        # Get dates that have booking blocks for this room's property
        block_dates = set()
        booking_blocks = BookingBlock.objects.filter(
            property=room.property,
            start_date__lte=end_date,
            end_date__gt=start_date,
            is_active=True,
        )

        for block in booking_blocks:
            block_start = max(block.start_date, start_date)
            block_end = min(block.end_date, end_date)
            current = block_start
            while current <= block_end:
                block_dates.add(current)
                current += timedelta(days=1)

        # Find dates that don't have any of the above
        occupied_dates = booking_dates | rate_dates | block_dates
        empty_dates = [date for date in dates_to_check if date not in occupied_dates]

        logger.debug(
            f"Room {room.id}: Found {len(empty_dates)} empty dates out of {len(dates_to_check)} total dates"
        )
        return empty_dates

    except Exception as e:
        logger.error(
            f"Error finding empty dates for room {room.id}: {str(e)}", exc_info=True
        )
        return []


def _group_consecutive_dates(dates: List[datetime.date]) -> List[Dict[str, str]]:
    """
    Group consecutive dates into ranges for SU API format.

    Args:
        dates: List of dates to group

    Returns:
        List of dictionaries with 'from' and 'to' date ranges
    """
    if not dates:
        return []

    # Sort dates to ensure they're in order
    sorted_dates = sorted(dates)
    ranges = []
    start_date = sorted_dates[0]
    end_date = sorted_dates[0]

    for i in range(1, len(sorted_dates)):
        current_date = sorted_dates[i]
        # Check if current date is consecutive to the previous one
        if current_date == end_date + timedelta(days=1):
            end_date = current_date
        else:
            # Add the current range and start a new one
            ranges.append(
                {
                    "from": start_date.strftime("%Y-%m-%d"),
                    "to": end_date.strftime("%Y-%m-%d"),
                }
            )
            start_date = current_date
            end_date = current_date

    # Add the final range
    ranges.append(
        {"from": start_date.strftime("%Y-%m-%d"), "to": end_date.strftime("%Y-%m-%d")}
    )

    return ranges


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def block_rooms_task(self, block_id: int) -> Dict[str, Any]:
    """
    Task to block rooms by setting inventory to zero for the specified date range.

    Args:
        block_id: ID of the BookingBlock

    Returns:
        Dictionary with task result information
    """
    try:
        # Retrieve the booking block
        block = BookingBlock.objects.select_related("property").get(id=block_id)
        property_instance = block.property
        if not property_instance.is_onboarded:
            logger.warning(
                f"Property {property_instance.id} is not onboarded. Skipping inventory update."
            )
            return {"status": "warning", "message": "Property not onboarded"}

        # Get all active rooms for this property
        rooms = Room.objects.filter(property=property_instance, is_active=True)

        if not rooms.exists():
            logger.warning(f"No active rooms found for property {property_instance.id}")
            return {"status": "warning", "message": "No active rooms found"}

        # Format date range, using today if block.start_date is in the past
        effective_start_date = get_effective_start_date(block.start_date)
        if effective_start_date > block.end_date:
            logger.info(
                f"Block {block_id} is entirely in the past. No inventory update needed."
            )
            return {"status": "skipped", "message": "Block is in the past"}
        date_range = format_date_range(effective_start_date, block.end_date)

        success = True
        results = []

        # Set inventory to 0 for each room
        for room in rooms:
            room_id = str(room.id)[
                :8
            ]  # Get the first 8 characters as per SU API requirements

            # Add roomstosell: 0 to the date range
            inventory_data = [{**date, "roomstosell": "0"} for date in date_range]

            try:
                response = update_inventory(
                    property_instance=property_instance,
                    room_id=room_id,
                    inventory_data=inventory_data,
                )
                logger.info(
                    f"Blocked room {room_id} inventory data: {inventory_data} -> Response: {response}"
                )
                # Check if the response indicates success
                is_successful = (
                    response.get("Success") == "Success"
                    or response.get("Status") == "Success"
                )
                results.append(
                    {
                        "room_id": room_id,
                        "response": response,
                        "status": "success" if is_successful else "failed",
                    }
                )

                if not is_successful:
                    success = False
                    logger.error(f"Failed to block room {room_id}: {response}")
                else:
                    logger.info(f"Successfully blocked room {room_id}: {response}")

            except Exception as e:
                success = False
                logger.error(f"Error blocking room {room_id}: {str(e)}", exc_info=True)
                results.append(
                    {"room_id": room_id, "error": str(e), "status": "failed"}
                )

        # Update block status if successful
        if success:
            with transaction.atomic():
                block.is_active = True
                block.save(update_fields=["is_active"])

        return {
            "status": "success" if success else "partial_failure",
            "block_id": block_id,
            "results": results,
        }

    except BookingBlock.DoesNotExist:
        logger.error(f"BookingBlock with ID {block_id} not found")
        return {"status": "failed", "message": "BookingBlock not found"}
    except Exception as e:
        logger.error(
            f"Error in block_rooms_task for block {block_id}: {str(e)}", exc_info=True
        )
        self.retry(exc=e)
        return {"status": "failed", "message": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def unblock_rooms_task(self, property_id: str, start_date, end_date) -> Dict[str, Any]:
    """
    Task to unblock rooms by resetting inventory to the original quantity.

    Args:
        property_id: ID of the property
        start_date: Start date of the block period
        end_date: End date of the block period

    Returns:
        Dictionary with task result information
    """
    try:
        # Retrieve the property
        property_instance = Property.objects.get(id=property_id)
        if not property_instance.is_onboarded:
            logger.warning(
                f"Property {property_instance.id} is not onboarded. Skipping inventory update."
            )
            return {"status": "warning", "message": "Property not onboarded"}

        # Get all active rooms for this property
        rooms = Room.objects.filter(property=property_instance, is_active=True)

        if not rooms.exists():
            logger.warning(f"No active rooms found for property {property_id}")
            return {"status": "warning", "message": "No active rooms found"}

        # Format date range, using today if start_date is in the past
        effective_start_date = get_effective_start_date(start_date)
        if effective_start_date > end_date:
            logger.info(
                f"Unblock period is entirely in the past. No inventory update needed."
            )
            return {"status": "skipped", "message": "Unblock period is in the past"}
        date_range = format_date_range(effective_start_date, end_date)

        success = True
        results = []

        # For each room, set inventory back to its quantity
        for room in rooms:
            room_id = str(room.id)[:8]  # Get the first 8 characters for SU API
            room_quantity = str(room.quantity)  # Convert to string for API

            # Add roomstosell with the original quantity to the date range
            inventory_data = [
                {**date, "roomstosell": room_quantity} for date in date_range
            ]

            try:
                logger.info(
                    f"Unblocking room {room_id} inventory data: {inventory_data}"
                )
                response = update_inventory(
                    property_instance=property_instance,
                    room_id=room_id,
                    inventory_data=inventory_data,
                )
                is_successful = (
                    response.get("Success") == "Success"
                    or response.get("Status") == "Success"
                )
                results.append(
                    {
                        "room_id": room_id,
                        "response": response,
                        "status": "success" if is_successful else "failed",
                    }
                )

                if not is_successful:
                    success = False
                    logger.error(f"Failed to unblock room {room_id}: {response}")
                else:
                    logger.info(f"Successfully unblocked room {room_id}: {response}")

            except Exception as e:
                success = False
                logger.error(
                    f"Error unblocking room {room_id}: {str(e)}", exc_info=True
                )
                results.append(
                    {"room_id": room_id, "error": str(e), "status": "failed"}
                )

        return {
            "status": "success" if success else "partial_failure",
            "property_id": str(property_id),
            "results": results,
        }

    except Property.DoesNotExist:
        logger.error(f"Property with ID {property_id} not found")
        return {"status": "failed", "message": "Property not found"}
    except Exception as e:
        logger.error(
            f"Error in unblock_rooms_task for property {property_id}: {str(e)}",
            exc_info=True,
        )
        self.retry(exc=e)
        return {"status": "failed", "message": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def update_inventory_for_booking(
    self, booking_id: str, action: str = "decrease"
) -> Dict[str, Any]:
    """
    Task to update inventory based on booking creation, modification, or cancellation.

    Args:
        booking_id: ID of the Booking
        action: Action to perform - "decrease" for new bookings, "increase" for cancellations

    Returns:
        Dictionary with task result information
    """
    try:
        # Retrieve the booking
        booking = Booking.objects.select_related("property").get(id=booking_id)
        property_instance = booking.property

        # Get all active rooms for this property
        rooms = Room.objects.filter(property=property_instance, is_active=True)

        if not rooms.exists():
            logger.warning(f"No active rooms found for property {property_instance.id}")
            return {"status": "warning", "message": "No active rooms found"}

        # Format date range, using today if checkin_date is in the past
        effective_start_date = get_effective_start_date(booking.checkin_date)
        if effective_start_date > booking.checkout_date:
            logger.info(
                f"Booking {booking_id} is entirely in the past. No inventory update needed."
            )
            return {"status": "skipped", "message": "Booking is in the past"}
        date_range = format_date_range(effective_start_date, booking.checkout_date)

        success = True
        results = []

        # For each room, adjust inventory based on action
        for room in rooms:
            room_id = str(room.id)[:8]  # Get the first 8 characters for SU API
            current_quantity = int(room.quantity)

            # Determine new inventory value based on action
            if action == "decrease":
                # For new bookings, decrease inventory by 1 (minimum 0)
                new_quantity = max(0, current_quantity - 1)
            else:
                # For cancellations, restore inventory by 1 (up to original quantity)
                new_quantity = min(current_quantity + 1, room.quantity)

            # Add roomstosell with the updated quantity to the date range
            inventory_data = [
                {**date, "roomstosell": str(new_quantity)} for date in date_range
            ]

            try:
                response = update_inventory(
                    property_instance=property_instance,
                    room_id=room_id,
                    inventory_data=inventory_data,
                )
                is_successful = (
                    response.get("Success") == "Success"
                    or response.get("Status") == "Success"
                )
                results.append(
                    {
                        "room_id": room_id,
                        "response": response,
                        "status": "success" if is_successful else "failed",
                    }
                )

                if not is_successful:
                    success = False
                    logger.error(
                        f"Failed to update inventory for room {room_id}: {response}"
                    )
                else:
                    logger.info(
                        f"Successfully updated inventory for room {room_id}: {response}"
                    )

            except Exception as e:
                success = False
                logger.error(
                    f"Error updating inventory for room {room_id}: {str(e)}",
                    exc_info=True,
                )
                results.append(
                    {"room_id": room_id, "error": str(e), "status": "failed"}
                )

        return {
            "status": "success" if success else "partial_failure",
            "booking_id": booking_id,
            "results": results,
        }

    except Booking.DoesNotExist:
        logger.error(f"Booking with ID {booking_id} not found")
        return {"status": "failed", "message": "Booking not found"}
    except Exception as e:
        logger.error(
            f"Error in update_inventory_for_booking for booking {booking_id}: {str(e)}",
            exc_info=True,
        )
        self.retry(exc=e)
        return {"status": "failed", "message": str(e)}
