from apps.billing.views import *
from django.urls import include, path
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r"billing-profiles", BillingProfileViewSet, basename="billing-profile")
router.register(r"billing-addresses", BillingAddressViewSet, basename="billing-address")
router.register(r"taxation", TaxationViewSet, basename="taxation")

urlpatterns = [
    path("", include(router.urls)),
    path("summary/", BillingSummaryView.as_view(), name="billing-summary"),
]
