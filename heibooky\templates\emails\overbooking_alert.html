<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translations.overbooking_alert }}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f4f4;
            color: #113158;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(17, 49, 88, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: #ffffff;
            text-align: center;
            padding: 20px 0;
        }
        .logo-container img {
            width: 80px; 
            height: 80px; 
            border-radius: 50%;
        }
        .alert-badge {
            background-color: #ffffff;
            color: #dc3545;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            margin: 10px 0;
            display: inline-block;
        }
        .content {
            padding: 40px 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .section-title {
            color: #113158;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #FCB51F;
            padding-bottom: 5px;
        }
        .property-info, .booking-info {
            display: grid;
            grid-template-columns: 140px 1fr;
            gap: 10px;
            margin-bottom: 8px;
        }
        .label {
            font-weight: bold;
            color: #666;
        }
        .value {
            color: #113158;
        }
        .booking-card {
            background-color: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .booking-card.existing {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .booking-card.new {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .booking-card h4 {
            margin: 0 0 10px 0;
            color: #113158;
        }
        .overlap-alert {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .overlap-alert .overlap-dates {
            font-weight: bold;
            color: #856404;
            font-size: 16px;
        }
        .action-button {
            display: inline-block;
            background-color: #FCB51F;
            color: #113158;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            background: linear-gradient(135deg, #113158 0%, #1a4a85 100%);
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            padding: 20px;
            font-size: 13px;
        }
        .footer a {
            color: #FCB51F;
            text-decoration: none;
        }
        .price {
            color: #28a745;
            font-weight: bold;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-new { background-color: #e3f2fd; color: #1976d2; }
        .status-modified { background-color: #fff3e0; color: #f57c00; }
        .status-request { background-color: #f3e5f5; color: #7b1fa2; }
        .status-cancelled { background-color: #ffebee; color: #d32f2f; }
        .status-completed { background-color: #e8f5e8; color: #388e3c; }
    </style>
</head>
<body>
    <table role="presentation" class="email-container" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td class="header">
                <div class="logo-container">
                    <img src="{{ logo_url }}" alt="Heibooky">
                </div>
                <div class="alert-badge">🚨 OVERBOOKING</div>
                <h1>{{ translations.overbooking_detected }}</h1>
            </td>
        </tr>

        <tr>
            <td class="content">
                <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    {{ translations.overbooking_message }}
                </p>
                <p style="font-size: 16px; line-height: 1.6; color: #dc3545; font-weight: bold;">
                    {{ translations.overbooking_action_required }}
                </p>

                <div class="section">
                    <div class="section-title">{{ translations.property_details }}</div>
                    <div class="property-info">
                        <span class="label">{{ translations.property_name }}:</span>
                        <span class="value">{{ property.name }}</span>
                    </div>
                    <div class="property-info">
                        <span class="label">{{ translations.property_id }}:</span>
                        <span class="value">{{ property.hotel_id }}</span>
                    </div>
                    <div class="property-info">
                        <span class="label">{{ translations.chain_id }}:</span>
                        <span class="value">{{ property.chain_id }}</span>
                    </div>
                </div>

                <div class="overlap-alert">
                    <div style="margin-bottom: 10px;">
                        <strong>{{ translations.overlap_period }}</strong>
                    </div>
                    <div class="overlap-dates">
                        {{ translations.overlap_from }}: {{ overlap_start|date:"d/m/Y" }} - {{ translations.overlap_to }}: {{ overlap_end|date:"d/m/Y" }}
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">{{ translations.booking_details }}</div>
                    
                    <div class="booking-card existing">
                        <h4>{{ translations.existing_booking }}</h4>
                        <div class="booking-info">
                            <span class="label">{{ translations.booking_id }}:</span>
                            <span class="value">{{ existing_booking.id }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.guest_name }}:</span>
                            <span class="value">{{ existing_booking.reservation_data.guest_name|default:"N/A" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.checkin_date }}:</span>
                            <span class="value">{{ existing_booking.checkin_date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.checkout_date }}:</span>
                            <span class="value">{{ existing_booking.checkout_date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.channel }}:</span>
                            <span class="value">{{ existing_booking.get_channel_code_display|default:"N/A" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.booking_status }}:</span>
                            <span class="value">
                                <span class="status-badge status-{{ existing_booking.status }}">{{ existing_booking.get_status_display }}</span>
                            </span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.total_price }}:</span>
                            <span class="value price">€{{ existing_booking.reservation_data.total_price|default:"0.00" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.booking_date }}:</span>
                            <span class="value">{{ existing_booking.booking_date|date:"d/m/Y H:i" }}</span>
                        </div>
                    </div>

                    <div class="booking-card new">
                        <h4>{{ translations.new_booking }}</h4>
                        <div class="booking-info">
                            <span class="label">{{ translations.booking_id }}:</span>
                            <span class="value">{{ new_booking.id }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.guest_name }}:</span>
                            <span class="value">{{ new_booking.reservation_data.guest_name|default:"N/A" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.checkin_date }}:</span>
                            <span class="value">{{ new_booking.checkin_date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.checkout_date }}:</span>
                            <span class="value">{{ new_booking.checkout_date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.channel }}:</span>
                            <span class="value">{{ new_booking.get_channel_code_display|default:"N/A" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.booking_status }}:</span>
                            <span class="value">
                                <span class="status-badge status-{{ new_booking.status }}">{{ new_booking.get_status_display }}</span>
                            </span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.total_price }}:</span>
                            <span class="value price">€{{ new_booking.reservation_data.total_price|default:"0.00" }}</span>
                        </div>
                        <div class="booking-info">
                            <span class="label">{{ translations.booking_date }}:</span>
                            <span class="value">{{ new_booking.booking_date|date:"d/m/Y H:i" }}</span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center;">
                    <a href="{{ admin_url }}" class="action-button">
                        {{ translations.view_bookings }}
                    </a>
                </div>
            </td>
        </tr>
        <tr>
            <td class="footer">
                <p style="margin: 0; font-weight: bold;">{{ translations.admin_action_required }}</p>
                <p style="margin: 5px 0 0 0;">&copy;2025 Heibooky. {{ translations.all_rights_reserved }}</p>
                <p style="margin: 5px 0 0 0;">{{ translations.questions_email_us }} <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </td>
        </tr>
    </table>
</body>
</html>
