"""
Tests for the resend verification functionality
"""

import time
from unittest.mock import Mock, patch

from django.core.cache import cache
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APITestCase

from apps.users.models import User


class ResendVerificationViewTest(APITestCase):
    """Test cases for the ResendVerificationView"""

    def setUp(self):
        """Set up test data"""
        self.url = reverse("resend-verification")
        self.user_data = {
            "email": "<EMAIL>",
            "name": "Test User",
            "phone": "+1234567890",
        }
        # Clear cache before each test
        cache.clear()

    def tearDown(self):
        """Clean up after each test"""
        cache.clear()

    def test_resend_verification_success(self):
        """Test successful resend of verification code"""
        # Create an unverified user
        user = User.objects.create_user(
            email=self.user_data["email"],
            name=self.user_data["name"],
            phone=self.user_data["phone"],
            is_verified=False,
        )

        with patch("apps.users.views.AccountEmailService") as mock_email_service, patch(
            "apps.users.views.VerificationService"
        ) as mock_verification_service:

            # Mock the services
            mock_email_instance = Mock()
            mock_verification_instance = Mock()
            mock_email_service.return_value = mock_email_instance
            mock_verification_service.return_value = mock_verification_instance
            mock_verification_instance.generate_code.return_value = "1234"

            response = self.client.post(self.url, {"email": user.email})

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn(
                "Verification code sent successfully", response.data["detail"]
            )

            # Verify the services were called
            mock_verification_instance.generate_code.assert_called_once()
            mock_email_instance.send_verification_email.assert_called_once_with(
                user.email, "1234"
            )
            mock_verification_instance.store_code.assert_called_once_with(
                user.email, "1234"
            )

    def test_resend_verification_already_verified(self):
        """Test resend for already verified user"""
        # Create a verified user
        user = User.objects.create_user(
            email=self.user_data["email"],
            name=self.user_data["name"],
            phone=self.user_data["phone"],
            is_verified=True,
        )

        response = self.client.post(self.url, {"email": user.email})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("already verified", response.data["detail"])

    def test_resend_verification_user_not_found(self):
        """Test resend for non-existent user (should return generic message for security)"""
        response = self.client.post(self.url, {"email": "<EMAIL>"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("If the email exists", response.data["detail"])

    def test_resend_verification_invalid_email(self):
        """Test resend with invalid email format"""
        response = self.client.post(self.url, {"email": "invalid-email"})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("email", response.data["errors"])

    def test_resend_verification_missing_email(self):
        """Test resend without email field"""
        response = self.client.post(self.url, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("email", response.data["errors"])

    def test_rate_limiting_cooldown(self):
        """Test rate limiting with cooldown period"""
        user = User.objects.create_user(
            email=self.user_data["email"],
            name=self.user_data["name"],
            is_verified=False,
        )

        with patch("apps.users.views.AccountEmailService") as mock_email_service, patch(
            "apps.users.views.VerificationService"
        ) as mock_verification_service:

            # Mock the services
            mock_email_instance = Mock()
            mock_verification_instance = Mock()
            mock_email_service.return_value = mock_email_instance
            mock_verification_service.return_value = mock_verification_instance
            mock_verification_instance.generate_code.return_value = "1234"

            # First request should succeed
            response1 = self.client.post(self.url, {"email": user.email})
            self.assertEqual(response1.status_code, status.HTTP_200_OK)

            # Second request immediately should be rate limited
            response2 = self.client.post(self.url, {"email": user.email})
            self.assertEqual(response2.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
            self.assertIn("Too many requests", response2.data["errors"]["email"][0])

    def test_rate_limiting_max_attempts(self):
        """Test rate limiting with maximum attempts"""
        user = User.objects.create_user(
            email=self.user_data["email"],
            name=self.user_data["name"],
            is_verified=False,
        )

        # Mock timezone.now to control time for testing
        base_time = timezone.now()

        with patch("apps.users.views.AccountEmailService") as mock_email_service, patch(
            "apps.users.views.VerificationService"
        ) as mock_verification_service, patch(
            "apps.users.views.timezone.now"
        ) as mock_now:

            # Mock the services
            mock_email_instance = Mock()
            mock_verification_instance = Mock()
            mock_email_service.return_value = mock_email_instance
            mock_verification_service.return_value = mock_verification_instance
            mock_verification_instance.generate_code.return_value = "1234"

            # Simulate time progression to bypass cooldown but test max attempts
            time_increments = [0, 300, 600]  # 0, 5 minutes, 10 minutes

            for i, time_increment in enumerate(time_increments):
                mock_now.return_value = base_time + timezone.timedelta(
                    seconds=time_increment
                )

                response = self.client.post(self.url, {"email": user.email})

                if i < 3:  # First 3 attempts should succeed (within cooldown logic)
                    # Note: This test might need adjustment based on actual rate limiting logic
                    pass
                else:
                    self.assertEqual(
                        response.status_code, status.HTTP_429_TOO_MANY_REQUESTS
                    )

    def test_email_service_failure(self):
        """Test handling of email service failure"""
        user = User.objects.create_user(
            email=self.user_data["email"],
            name=self.user_data["name"],
            is_verified=False,
        )

        with patch("apps.users.views.AccountEmailService") as mock_email_service, patch(
            "apps.users.views.VerificationService"
        ) as mock_verification_service:

            # Mock the services with failure
            mock_email_instance = Mock()
            mock_verification_instance = Mock()
            mock_email_service.return_value = mock_email_instance
            mock_verification_service.return_value = mock_verification_instance
            mock_verification_instance.generate_code.return_value = "1234"
            mock_email_instance.send_verification_email.side_effect = Exception(
                "Email service failed"
            )

            response = self.client.post(self.url, {"email": user.email})

            self.assertEqual(
                response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            self.assertIn(
                "Failed to send verification code",
                response.data["errors"]["general"][0],
            )

    def test_case_insensitive_email(self):
        """Test that email comparison is case insensitive"""
        user = User.objects.create_user(
            email="<EMAIL>",
            name=self.user_data["name"],
            is_verified=False,
        )

        with patch("apps.users.views.AccountEmailService") as mock_email_service, patch(
            "apps.users.views.VerificationService"
        ) as mock_verification_service:

            # Mock the services
            mock_email_instance = Mock()
            mock_verification_instance = Mock()
            mock_email_service.return_value = mock_email_instance
            mock_verification_service.return_value = mock_verification_instance
            mock_verification_instance.generate_code.return_value = "1234"

            # Test with uppercase email
            response = self.client.post(self.url, {"email": "<EMAIL>"})

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn(
                "Verification code sent successfully", response.data["detail"]
            )


class EmailVerificationMixinTest(TestCase):
    """Test cases for the EmailVerificationMixin"""

    def setUp(self):
        """Set up test data"""
        from apps.users.views import EmailVerificationMixin

        self.mixin = EmailVerificationMixin()
        cache.clear()

    def tearDown(self):
        """Clean up after each test"""
        cache.clear()

    def test_check_rate_limit_no_previous_attempts(self):
        """Test rate limit check with no previous attempts"""
        is_allowed, remaining_time = self.mixin.check_rate_limit("<EMAIL>")
        self.assertTrue(is_allowed)
        self.assertEqual(remaining_time, 0)

    def test_record_attempt(self):
        """Test recording an attempt"""
        email = "<EMAIL>"
        self.mixin.record_attempt(email)

        # Check that the attempt was recorded
        cache_key = f"resend_verification:{email}"
        attempts_key = f"resend_verification_attempts:{email}"

        self.assertIsNotNone(cache.get(cache_key))
        self.assertEqual(cache.get(attempts_key), 1)

    def test_rate_limit_cooldown(self):
        """Test rate limiting cooldown period"""
        email = "<EMAIL>"

        # Record an attempt
        self.mixin.record_attempt(email)

        # Check that immediate second attempt is blocked
        is_allowed, remaining_time = self.mixin.check_rate_limit(email)
        self.assertFalse(is_allowed)
        self.assertGreater(remaining_time, 0)

    def test_rate_limit_max_attempts(self):
        """Test rate limiting with maximum attempts"""
        email = "<EMAIL>"

        # Record maximum attempts
        for _ in range(3):
            self.mixin.record_attempt(email)

        # The next check should be blocked due to max attempts
        is_allowed, remaining_time = self.mixin.check_rate_limit(email)
        self.assertFalse(is_allowed)
        self.assertGreater(remaining_time, 0)
